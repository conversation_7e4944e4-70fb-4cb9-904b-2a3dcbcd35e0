<?xml version="1.0" encoding="UTF-8"?>
<config>
	 <param key="CS_DOMAIN" name="美的售后系统域名地址" type="string" description="例：http://csuat.midea.com/c-css-ipms" value="http://csuat.midea.com/c-css-ipms"/>
	 <param key="LDAP_IP" name="LDAP的IP地址" type="string" description="在美的LDAP系统里申请后会提供IP" value="************"/>
	 <param key="LDAP_PORT" name="LDAP的端口号" type="string" description="在美的LDAP系统里申请后会提供端口" value="389"/>
	 <param key="LDAP_APP_ID" name="LDAP的应用id" type="string" description="在美的LDAP系统里申请后会提供应用id" value="ccscc_bind"/>
	 <param key="LDAP_APP_PWD" name="LDAP的应用密码" type="string" description="在美的LDAP系统里申请后会提供应用密码" value="zKtNO4"/>
	 <param key="CACHE_FAIL_TIME" name="缓存失效时长" type="string" description="单位：分钟" value="10"/>
	 <param key="ORDER_CREATE_USER_INFO" name="ivr创建催单创建人信息" type="string" description="三个参数必填,例如：账号;姓名;部门编号" value="wang;益哒;001001"/>
	 <param key="ORDER_CREATE_TYPE_INFO" name="ivr创建催单服务请求信息json串" type="string" description="9个参数必填:服务请求5个，业务类型4个" value="" />
     <param key="SYNC_ENGINEER_USER_TIME" name="售后工程师同步时间段" type="string" description="时间格式hh:mm，多个时间逗号分隔" value="02:00,10:00,12:00,16:00"/>
     <param key="SYNC_ENGINEER_USER_DURATION" name="售后工程师同步跨天数" type="string" description="售后工程师增量同步时，向前跨越天数，例如：1" value="1"/>
     <param key="CS_PORT" name="美的售后端口" type="string" description="css接口 ：http://csuat.midea.com口" value="http://csuat.midea.com"/>
	<param key="TASK_DELCALLBACKUSER_ON_OFF" name="定时任务删除待回呼用户开关" type="string" description="定时任务删除待回呼用户开关 ON or OFF" value="OFF"/>
     <param key="CSS_APPKEY" name="CSS的APPKEY" type="string" description="CSS的APPKEY" value="695e71fd-db46-487c-a337-be7605e5112d"/>
     <param key="CSS_CLIENTID" name="CSS的CLIENTID" type="string" description="CSS的CLIENTID" value="0da25f1c-355e-452e-9e6b-c54ed7e07ad6"/>
	<param key="IOT_ERROR_APP_ID" name="IOT报障信息接口鉴权id" type="string" description="IOT报障信息接口鉴权id" value="10066"/>
	<param key="IOT_ERROR_APP_SECRET" name="IOT报障信息接口鉴权秘钥" type="string" description="IOT报障信息接口鉴权秘钥" value="8374451f8b994b27783f3f08235e7ed0"/>
	<param key="CS_COMPENSATE_DOMAIN" name="售后系统现金补偿接口所在域名" type="string" description="例：https://apiuat.midea.com/c-css/wom-bff-ipms" value="https://apiuat.midea.com/c-css/wom-bff-ipms"/>
	<param key="RP_DOMAIN" name="IOT接口域名" type="string" description="IOT接口域名" value="https://datarev.midea.com"/>
	<param key="CS_DOMAIN_NEW" name="售后系统接口所在新域名" type="string" description="售后系统接口所在新域名" value="https://apiprod.midea.com"/>
	<param key="CS_API_KEY" name="CS鉴权key" type="string" description="CS鉴权key" value="80b09b9dbd144718b08b340b3789c9bf"/>
	<param key="CS_API_SECRET" name="CS鉴权secret" type="string" description="CS鉴权secret" value="73f6ad124f63400fb66c446fe2df0f68"/>
	<!-- AIMP 服务相关配置 -->
	<param key="AIMP_SERVICE_URL" name="AIMP服务分类接口地址" type="string" description="AIMP服务分类接口地址" value="https://aimpapi.midea.com/t-aigc/aimp-customer-classification/llm/customer-assistant/service_classification"/>
	<param key="AIMP_AUTHORIZATION" name="AIMP鉴权密钥" type="string" description="AIMP鉴权密钥" value="msk-2f8ea0a83063bdcab01d18f9a5ed1c9386d3c60da95246bb6bf6d055ac9181c9"/>
	<param key="CUSTOMER_SERVICE_SUMMARY_API_URL" name="客服工单服务总结接口地址" type="string" description="客服工单服务总结接口地址" value=""/>
	<param key="CUSTOMER_SERVICE_SUMMARY_API_KEY" name="客服工单服务总结API密钥" type="string" description="客服工单服务总结API密钥" value=""/>
</config>
