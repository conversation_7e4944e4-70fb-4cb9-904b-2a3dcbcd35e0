package com.yunqu.cc.cssgw.enums;

import com.github.kevinsawicki.http.HttpRequest;
import com.yunqu.cc.cssgw.base.OrderURL;

/**
 * <p>
 * 对接CSS接口
 * </p>
 *
 * @ClassName OrderReqCommand
 * <AUTHOR> Copy This Tag)
 * @Description 对接CSS接口
 * @Since create in 2022/10/25 9:32
 * @Version v1.0
 * @Copyright Copyright (c) 2022
 * @Company 广州云趣信息科技有限公司
 */
public enum OrderReqCommandEnum {
	/**
	 * ENGINER_QUERY：工程师信息查询
	 */
	ENGINEER_QUERY("ENGINEER_QUERY", OrderURL.CS_DOMAIN_NEW+"/c-css/sup-bff-ipms/cc/bff/sup/engineer/queryEngineerOuInfo", HttpRequest.METHOD_POST, 1),
	/**
	 * SERVICE_ORDER_QUERY：服务单详情信息查询接口
	 */
	SERVICE_ORDER_QUERY("SERVICE_ORDER_QUERY", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/serviceorder/queryserviceorder", HttpRequest.METHOD_POST, 0),
	/**
	 * SERVICE_PROCESS_QUERY：服务过程查询接口
	 * 0926增加鉴权
	 */
	SERVICE_PROCESS_QUERY("SERVICE_PROCESS_QUERY", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/serviceorder/listserviceprocess", HttpRequest.METHOD_POST, 0),
	/**
	 * QUERY_DISPATCH_ORDER：派工历史接口
	 * 0926增加鉴权
	 */
	QUERY_DISPATCH_ORDER("QUERY_DISPATCH_ORDER", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/querydispatchorder", HttpRequest.METHOD_POST, 0),
	/**
	 * QUERY_CONTACT_USER_REQUIRE：命令信息查询接口
	 * 0926增加鉴权
	 */
	QUERY_CONTACT_USER_REQUIRE("QUERY_CONTACT_USER_REQUIRE", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/serviceorder/querycontactuserrequire", HttpRequest.METHOD_POST, 0),
	/**
	 * SPARE_PARTS_RECORD_QUERY:备件记录查询接口
	 */
	SPARE_PARTS_RECORD_QUERY("SPARE_PARTS_RECORD_QUERY", "", HttpRequest.METHOD_POST, 0),
	/**
	 * SPARE_PARTS_LOGISTICS_QUERY:备件物流查询
	 */
	SPARE_PARTS_LOGISTICS_QUERY("SPARE_PARTS_LOGISTICS_QUERY", "", HttpRequest.METHOD_POST, 0),
	/**
	 * QUERY_CHARGE_DETAILS:收费查询接口
	 * 0926增加鉴权
	 */
	QUERY_CHARGE_DETAILS("QUERY_CHARGE_DETAILS", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/order/querychargedetails", HttpRequest.METHOD_POST, 0),
	/**
	 * APPRAISAL_PROGRESS_QUERY:鉴定进度查询
	 */
	APPRAISAL_PROGRESS_QUERY("APPRAISAL_PROGRESS_QUERY", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryidentifyarchives", HttpRequest.METHOD_POST, 0),
	/**
	 * ENGINEER_SCHEDULE_QUERY:工程师日程查询
	 */
	ENGINEER_SCHEDULE_QUERY("ENGINEER_SCHEDULE_QUERY", "", HttpRequest.METHOD_POST, 0),
	/**
	 * INSTALLATION_FILE_QUERY:安装档案查询
	 * 0926增加鉴权
	 */
	INSTALLATION_FILE_QUERY("INSTALLATION_FILE_QUERY", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryinstallarchives", HttpRequest.METHOD_POST, 0),
	/**
	 * INSTALLATION_FILE_DETAIL_QUERY:安装档案详情查询
	 * 0926增加鉴权
	 */
	INSTALLATION_FILE_DETAIL_QUERY("INSTALLATION_FILE_DETAIL_QUERY", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryinstallarchivesdetail", HttpRequest.METHOD_POST, 0),
	/**
	 * QUERY_REPAIR_ARCHIVES:维修档案查询
	 * 0926增加鉴权
	 */
	QUERY_REPAIR_ARCHIVES("QUERY_REPAIR_ARCHIVES", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryrepairarchives", HttpRequest.METHOD_POST, 0),
	/**
	 * QUERY_REPAIR_ARCHIVES_DETAIL:维修档案详情查询
	 * 0926增加鉴权
	 */
	QUERY_REPAIR_ARCHIVES_DETAIL("QUERY_REPAIR_ARCHIVES_DETAIL", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryrepairarchivesdetail", HttpRequest.METHOD_POST, 0),
	/**
	 * QUERY_PROLONG_SERV_ARCHIVES:延保档案查询
	 * 0926增加鉴权
	 */
	QUERY_PROLONG_SERV_ARCHIVES("QUERY_PROLONG_SERV_ARCHIVES", OrderURL.CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryprolongservarchives", HttpRequest.METHOD_POST, 0),
	/**
	 * QUERY_DIRECT_DELIVERY_SPARE_PARTS:备件直发查询
	 */
	QUERY_DIRECT_DELIVERY_SPARE_PARTS("QUERY_DIRECT_DELIVERY_SPARE_PARTS", "", HttpRequest.METHOD_POST, 0),
	/**************************以上位11月版本****************************/


	/**************************以下位12月版本****************************/
	/**
	 * PAGE_COMPLAINT_HANDLE_INFO:投诉列表查询
	 */
	PAGE_COMPLAINT_HANDLE_INFO("PAGE_COMPLAINT_HANDLE_INFO", OrderURL.CS_DOMAIN+"/csscc/api/wom/complaintinfo/pagecomplainthandleinfo", HttpRequest.METHOD_POST, 0),
	/**
	 * QUERY_COMPLAINT_DETAIL:投诉详情查询
	 */
	QUERY_COMPLAINT_DETAIL("QUERY_COMPLAINT_DETAIL", OrderURL.CS_DOMAIN+"/csscc/api/wom/complaintinfo/querycomplaintdetail", HttpRequest.METHOD_POST, 0),
	/**
	 * CREATE_UPGRADE_HANDLE_INFO:投诉升级
	 * MODIFY_UPGRADE_HANDLE_INFO:投诉升级
	 */
	CREATE_UPGRADE_HANDLE_INFO("CREATE_UPGRADE_HANDLE_INFO", OrderURL.CS_DOMAIN+"/csscc/api/wom/complaintupgradehandle/createupgradehandleinfo", HttpRequest.METHOD_POST, 0),
	MODIFY_UPGRADE_HANDLE_INFO("MODIFY_UPGRADE_HANDLE_INFO", OrderURL.CS_DOMAIN+"/csscc/api/wom/complaintupgradehandle/modifyupgradehandleinfo", HttpRequest.METHOD_POST, 0),
	/**
	 * UPDATE_COMPLAINTS_HANDLE:跟进信息/闭环提交
	 * DOCOMPLAINT_HANDLE:跟进信息/闭环提交
	 */
	UPDATE_COMPLAINTS_HANDLE("UPDATE_COMPLAINTS_HANDLE", OrderURL.CS_DOMAIN+"/csscc/api/wom/complaintinfo/updatecomplaintshandle", HttpRequest.METHOD_POST, 0),
	DOCOMPLAINT_HANDLE("DOCOMPLAINT_HANDLE", OrderURL.CS_DOMAIN+"/csscc/api/wom/complaintinfo/docomplainthandle", HttpRequest.METHOD_POST, 0),
	/**
	 * DISPATCH_COMPLAINT_HANDLE:跟进信息/闭环提交
	 * DOCOMPLAINT_HANDLE:跟进信息/闭环提交
	 */
	DISPATCH_COMPLAINT_HANDLE("DISPATCH_COMPLAINT_HANDLE", OrderURL.CS_DOMAIN+"/csscc/api/wom/complaintinfo/dispatchcomplainthandle", HttpRequest.METHOD_POST, 0),
	/**
	 * DO_SAVE_CONTACT_ORDER:接入单创建
	 */
	DO_SAVE_CONTACT_ORDER("DO_SAVE_CONTACT_ORDER", OrderURL.CS_DOMAIN+"/csscc/api/wom/contactorder/dosavecontactorder", HttpRequest.METHOD_POST, 0),
	/**
	 * DO_UPDATE_CONTACT_ORDER:接入单修改
	 */
	DO_UPDATE_CONTACT_ORDER("DO_UPDATE_CONTACT_ORDER", OrderURL.CS_DOMAIN+"/csscc/api/wom/contactorder/doupdatecontactorder", HttpRequest.METHOD_POST, 0),
	/**
	 * PAGE_CONTACT_ORDER:接入单查询
	 */
	PAGE_CONTACT_ORDER("PAGE_CONTACT_ORDER", OrderURL.CS_DOMAIN+"/csscc/api/wom/contactorder/pagecontactorder", HttpRequest.METHOD_POST, 0),
	/**
	 * 命令单cc内部状态
	 */
	ORDER_SHEET_STATE_QUERY("ORDER_SHEET_STATE_QUERY", "", HttpRequest.METHOD_POST, 0),
	/**
	 * POINTS_QUERY:积分查询
	 */
	POINTS_QUERY("POINTS_QUERY", OrderURL.CS_DOMAIN+"/api/mcsp_uc/mcsp-uc-bff/integral/countVipPoint.do", HttpRequest.METHOD_POST, 0),
	/**
	 * POINT_EXCHANGE_DAILY_QUERY:积分兑换流水查询
	 */
	POINT_EXCHANGE_DAILY_QUERY("POINT_EXCHANGE_DAILY_QUERY", "", HttpRequest.METHOD_POST, 0),
	/**
	 * RECORD_EXCHANGED_GOODS_LOGISTICS_INFORMATION_QUERY:兑换物品的记录物流信查询
	 */
	RECORD_EXCHANGED_GOODS_LOGISTICS_INFORMATION_QUERY("RECORD_EXCHANGED_GOODS_LOGISTICS_INFORMATION_QUERY", "", HttpRequest.METHOD_POST, 0);


	private String command;

	private String url;

	private String method;

	/**
	 * 参数提交方式：
	 * 0 -默认,在url上拼参数 x-www-form-urlencode
	 * 1 -json方式
	 */
	private Integer type;

	OrderReqCommandEnum(String command, String url, String method, Integer type) {
		this.command = command;
		this.url = url;
		this.method = method;
		this.type = type;
	}

	public String getCommand() {
		return command;
	}

	public void setCommand(String command) {
		this.command = command;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	/**
	 * 获取实例
	 *
	 * @param command 执行器编码
	 * @return com.yunqu.cc.cssgw.enums.OrderReqCommandActuator
	 * <AUTHOR>
	 * @date 2022/10/25 9:52
	 */
	public static OrderReqCommandEnum getInstance(String command) {
		OrderReqCommandEnum result = null;
		for (OrderReqCommandEnum orderReqCommandActuator : OrderReqCommandEnum.values()) {
			if (orderReqCommandActuator.getCommand().equals(command)) {
				result = orderReqCommandActuator;
				break;
			}
		}
		return result;
	}
}
