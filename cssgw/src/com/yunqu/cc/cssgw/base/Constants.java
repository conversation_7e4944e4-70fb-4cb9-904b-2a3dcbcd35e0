package com.yunqu.cc.cssgw.base;

import com.yunqu.cc.cssgw.utils.ConfigUtil;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_NAME = "yw-ds";     //默认数据源名称

	public final static String MARS_DS_NAME = "mars-ds";     //Mars数据源

	public final static String GENUSER_DS_NAME = "genuser-ds";     //亿讯数据源名称

	public final static String APP_NAME = "cssgw";     //应用

	public static final String CSS_BASE_URL = OrderURL.CS_DOMAIN+"/csscc/";

	public final static String LOG_CONTACT_NAME = "cssgw_contact";     //工单接入单接口对接日志

	public final static String LOG_CREATE_ORDER_NAME = "cssgw_create_order";     //工单接入单接口对接日志

	public final static String LOG_REVISIT_NAME = "cssgw_revisit";     //工单回访接口对接日志

	public final static String LOG_ORDER_NAME = "cssgw_order";     //工单其他接口对接日志

	public final static String LOG_INTERFACE_NAME = "cssgw-interface";//对接售后接口日志

	public final static String LOG_SYNC_CSS_NAME = "cssgw-sync-css"; //售后组织架构接口日志

	public final static String CSSGW_SYNC_CSS_ORG = "cssgw-sync-css-org"; //售后组织架构接口日志

	public final static String CSSGW_SYNC_CSS_ROLE = "cssgw-sync-css-role"; //售后角色接口日志

	public final static String CSSGW_AIXJ = "cssgw-aixj"; //小结接口分析日志

	public final static String CSSGW_SYNC_CSS_USER_11 = "cssgw-sync-css-user-11"; //售后账号用户日志
	public final static String CSSGW_SYNC_CSS_USER_0 = "cssgw-sync-css-user-0"; //MIP账号用户日志
	public final static String CSSGW_SYNC_CSS_USER_5 = "cssgw-sync-css-user-5"; //外部账号用户日志
	public final static String CSSGW_SYNC_CSS_USER_12 = "cssgw-sync-css-user-12"; //工程师用户日志

	public final static String LOG_IOT_NAME = "cssgw_iot";     //工单IOT回访接口对接日志

    public final static String USER_TYPE_CSS ="11";  	//售后账号（售后系统创建的账号包括网点账号）

    public final static String USER_TYPE_MIP ="0";		//内部账号（mip账号）

    public final static String USER_TYPE_OUTER ="5";	//外部账号

    public final static String USER_TYPE_ENGINEER ="12";//工程师账号

    public final static String ORG_STATUS_NORMAL ="01";//组织架构同步状态：正常

    public final static String ORG_STATUS_DELETED ="02";//组织架构同步状态：已删除

	/**缓存失效时间*/
	public static final int CACHE_FAIL_TIME = 60*ConfigUtil.getInt("CACHE_FAIL_TIME",10);

	/**催单创建服务请求信息*/
	public static final String ORDER_CREATE_TYPE_INFO = ConfigUtil.getString("ORDER_CREATE_TYPE_INFO");

	/**催单创建创建人信息*/
	public static final String ORDER_CREATE_USER_INFO = ConfigUtil.getString("ORDER_CREATE_USER_INFO");

	/**定时任务删除待回拨用户开关*/
	public static final String TASK_DELCALLBACKUSER_ON_OFF =  ConfigUtil.getString("TASK_DELCALLBACKUSER_ON_OFF","OFF");

	public static final String CSS_APPKEY = ConfigUtil.getString("CSS_APPKEY","695e71fd-db46-487c-a337-be7605e5112d");

	public static final String CSS_CLIENTID = ConfigUtil.getString("CSS_CLIENTID","0da25f1c-355e-452e-9e6b-c54ed7e07ad6");

	public static final String CSS_BASE_ADDR = ConfigUtil.getString("CSS_BASE_ADDR","");

}
