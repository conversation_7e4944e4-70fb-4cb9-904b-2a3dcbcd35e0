package com.yunqu.cc.notice.inf;

import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.SyncInfoModel;
import com.yq.busi.common.service.SyncService;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.cc.notice.base.Constants;
/**
 * 通知模块统一处理接口
 *
 */
public class InfService extends IService  {
	private Logger logger = LogEngine.getLogger(Constants.APP_NAME, Constants.APP_NAME);
	
	private EasyQuery query =  EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command"); 
		if(ServiceCommand.NOTICE_ADD_USER_NOTICE.equals(command)){
			JSONObject rjson = addUserNotice(json);
			return rjson;
		}if(ServiceCommand.NOTICE_SRH_USER_NOTICE.equals(command)){
			JSONObject rjson = srhUserNotice(json);
			return rjson;
		}else{
			JSONObject result = new JSONObject();
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "不存在的command,请检查！");
			return result;
		}
	}
	/**
	 * 添加一条通知
	 * @param json
	 * @return
	 */
	public JSONObject addUserNotice(JSONObject json){
		JSONObject rjson = JsonUtil.createInfRespJson(json);
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+ "收到接口新增通知的 Request 参数列表:" + json.toJSONString());
		
		String createUserAcc = json.getString("createUserAcc");
		String createUserDeptCode = json.getString("createUserDeptCode");
		String userAcc = json.getString("userAcc");
		String deptCode = json.getString("deptCode");
		String type = json.getString("type");
		String module = json.getString("module");
		String title = json.getString("title");
		String content = json.getString("content");
		String url = json.getString("url");
		String receiverType = json.getString("receiverType");
		String createTime = DateUtil.getCurrentDateStr();
		String remindTime = json.getString("remindTime");
		if(StringUtils.isNotBlank(remindTime)){
			createTime = remindTime;
			logger.info("createTime:" + createTime);
		}

		String method = json.getString("method");
		if(StringUtils.isBlank(method)){
			method = "01";
		}
		
		
		if (StringUtils.isBlank(createUserAcc) ||StringUtils.isBlank(createUserDeptCode)||StringUtils.isBlank(userAcc) ||StringUtils.isBlank(deptCode)||StringUtils.isBlank(type)||StringUtils.isBlank(module)||StringUtils.isBlank(title)
				||StringUtils.isBlank(content)||StringUtils.isBlank(url)||StringUtils.isBlank(receiverType)) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "添加用户通知失败:参数createUserAcc、createUserDeptCode、userAcc、deptCode、type、module、title、content、url、receiverType不能为空!");
			rjson.put("respCode", GWConstants.RET_CODE_MSG_CHEK_ERROR);
			rjson.put("respDesc", "参数createUserAcc、createUserDeptCode、userAcc、deptCode、type、module、title、content、url、receiverType不能为空!");
			return rjson;
		}
		try {
			query.begin();
			String noticeId = IDGenerator.getDefaultNUMID();
			EasyRecord record = new EasyRecord("C_CF_NOTICE","ID");
			record.put("ID", noticeId);
			record.put("USER_ACC", createUserAcc);
			record.put("DEPT_CODE", createUserDeptCode);
			record.put("CREATE_TIME", createTime);
			record.put("TYPE", type);
			record.put("MODULE", module);
			record.put("TITLE", title);
			record.put("CONTENT", content);
			record.put("URL",url );
			record.put("BAKUP", json.getString("bakup"));
			record.put("IS_READ", DictConstants.DICT_SY_YN_N);
			record.put("RECEIVER_TYPE", receiverType);
			record.put("METHOD", method);
			query.save(record);
			//推送给个人
			if("01".equals(receiverType)){
				String userAccs[] = userAcc.split(";");
				for(String acc : userAccs){
					EasyRecord record2 = new EasyRecord("C_CF_NOTICE_USER","ID");
					record2.put("ID", IDGenerator.getDefaultNUMID());
					record2.put("NOTICE_ID", noticeId);
					record2.put("CREATE_TIME", createTime);
					record2.put("RECEIVER_TYPE", receiverType);
					record2.put("RECEIVER", acc);
//					record2.put("USER_ACC", acc);
//					record2.put("DEPT_CODE", deptCode);
					record2.put("IS_READ", DictConstants.DICT_SY_YN_N);
					query.save(record2);
				}
			}
			//推送给部门
			if("02".equals(receiverType)){
				String deptCodes[] = deptCode.split(";");
				for(String dept : deptCodes){
					EasyRecord record2 = new EasyRecord("C_CF_NOTICE_USER","ID");
					record2.put("ID", IDGenerator.getDefaultNUMID());
					record2.put("NOTICE_ID", noticeId);
					record2.put("CREATE_TIME", createTime);
					record2.put("RECEIVER_TYPE", receiverType);
					record2.put("RECEIVER", dept);
//					record2.put("USER_ACC", userAcc);
//					record2.put("DEPT_CODE", dept);
					record2.put("IS_READ", DictConstants.DICT_SY_YN_N);
					query.save(record2);
				}
			}
			
			//推送给所有人
			if("03".equals(receiverType)){
				EasyRecord record2 = new EasyRecord("C_CF_NOTICE_USER","ID");
				record2.put("ID", IDGenerator.getDefaultNUMID());
				record2.put("NOTICE_ID", noticeId);
				record2.put("CREATE_TIME", createTime);
				record2.put("RECEIVER_TYPE", receiverType);
				record2.put("RECEIVER", "ALL");
//				record2.put("USER_ACC", userAcc);
//				record2.put("DEPT_CODE", dept);
				record2.put("IS_READ", DictConstants.DICT_SY_YN_N);
				query.save(record2);
			}
			
			query.commit();
			
			rjson.put("respCode", GWConstants.RET_CODE_SUCCESS);
			rjson.put("respDesc", "保存成功");
		} catch (Exception e) {
			rjson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			rjson.put("respDesc", "程序异常");
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "保存用户通知失败,失败原因：" + e.getMessage(),e);
			try {
				query.roolback();
			} catch (SQLException e1) {
			}
		}
		return rjson;
	}

	/**
	 * 查询用户能查看的通知
	 * @param json
	 * @return
	 */
	public JSONObject srhUserNotice(JSONObject json){
//		logger.info(CommonUtil.getClassNameAndMethod(this)+ "收到接口Request 参数列表:" + json.toJSONString());
		
		JSONObject rjson = JsonUtil.createInfRespJson(json);
		
		String userAcc = json.getString("userAcc");
		String deptCode = json.getString("deptCode");
		
		
		if (StringUtils.isBlank(userAcc) ||StringUtils.isBlank(deptCode)) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "添加用户通知失败:参数userAcc、deptCode不能为空!");
			rjson.put("respCode", GWConstants.RET_CODE_MSG_CHEK_ERROR);
			rjson.put("respDesc", "参数userAcc、deptCode不能为空!");
			return rjson;
		}
		
		try {
			SyncInfoModel model = SyncService.getSyncInfo("notice-srh-"+userAcc);
			String srhTime = model.getSyncTime();
			String currTime = DateUtil.getCurrentDateStr();
			
			EasySQL sql = new EasySQL();
//			sql.append(" SELECT T.TYPE,COUNT(1) CNT,MAX(T.URL) URL  FROM C_CF_NOTICE T WHERE T.RECEIVER_TYPE='01' AND T.CREATE_TIME>=? AND T.CREATE_TIME<=? AND EXISTS (SELECT 1 FROM C_CF_NOTICE_USER T2 WHERE T2.NOTICE_ID = T.ID AND T2.USER_ACC=?) GROUP BY T.TYPE ");
//			sql.append(" UNION ALL ");
//			sql.append(" SELECT T.TYPE,COUNT(1) CNT,MAX(T.URL) URL  FROM C_CF_NOTICE T WHERE T.RECEIVER_TYPE='02'  AND T.CREATE_TIME>=? AND T.CREATE_TIME<=? AND EXISTS (SELECT 1 FROM C_CF_NOTICE_USER T2 WHERE T2.NOTICE_ID = T.ID AND T2.DEPT_CODE=?) GROUP BY T.TYPE ");
//			sql.append(" UNION ALL ");
//			sql.append(" SELECT T.TYPE,COUNT(1) CNT,MAX(T.URL) URL  FROM C_CF_NOTICE T WHERE T.RECEIVER_TYPE='03' AND T.CREATE_TIME>=? AND T.CREATE_TIME<=? GROUP BY TYPE ");
			
			//查询出需要汇总显示的
			sql.append(" SELECT T2.TYPE,COUNT(1) CNT,MAX(T2.URL) URL FROM C_CF_NOTICE_USER T1,C_CF_NOTICE T2  WHERE T1.NOTICE_ID = T2.ID   ");
			sql.append(" AND  T1.RECEIVER IN (?,?,'ALL') AND T2.METHOD='01' AND T2.CREATE_TIME >=? GROUP BY T2.TYPE  ");
			List<EasyRow> list = query.queryForList(sql.getSQL(),new Object[]{userAcc,deptCode,srhTime});
			
			//查询出需要分条显示的
			EasySQL sql2 = new EasySQL();
			sql2.append(" SELECT T2.TYPE,T2.TITLE,T2.CONTENT,T2.URL  FROM C_CF_NOTICE_USER T1,C_CF_NOTICE T2  WHERE T1.NOTICE_ID = T2.ID    ");
			sql2.append(" AND  T1.RECEIVER IN (?,?,'ALL') AND T2.METHOD='02' AND T2.CREATE_TIME >=?  ");
			List<EasyRow> list2 = query.queryForList(sql2.getSQL(),new Object[]{userAcc,deptCode,srhTime});
			
			//查询出备忘录
			EasySQL sql3 = new EasySQL();
			sql3.append(" SELECT T.CONTENT FROM C_CF_AGENT_MEMOS T WHERE T.USER_ACC =? AND  T.REMIND_TIME>=? AND T.REMIND_TIME<=?    ");
			List<EasyRow> list3 = query.queryForList(sql3.getSQL(),new Object[]{userAcc,srhTime,currTime});

			//右下角展示content内容
			EasySQL sql4 = new EasySQL();
			sql4.append(" SELECT T2.TYPE,T2.CONTENT FROM C_CF_NOTICE_USER T1,C_CF_NOTICE T2  WHERE T1.NOTICE_ID = T2.ID   ");
			sql4.append(" AND  T1.RECEIVER IN (?,?,'ALL') AND T2.METHOD='03' AND T2.CREATE_TIME >=? GROUP BY T2.TYPE,T2.CONTENT  ");
			List<EasyRow> list4 = query.queryForList(sql4.getSQL(),new Object[]{userAcc,deptCode,srhTime});
			
			rjson.put("respCode", GWConstants.RET_CODE_SUCCESS);
			rjson.put("respDesc", "查询成功");
			
			//更新同步时间，确保下次不再显示
			//update  等待用户点击应答后修改
//			model.setSyncTime(currTime);
//			SyncService.updateSyncTime(model);
			
			int noticeNum = 0;
			
			StringBuffer sb = new StringBuffer();
			sb.append("您有如下通知:<br/>");
			
			//汇总通知
			if(CommonUtil.listIsNotNull(list)){
				for(EasyRow row : list){
					sb.append(row.getColumnValue("TYPE")+":"+row.getColumnValue("CNT")+"<br/>");
					noticeNum ++;
				}
			}

			//新增方式，右下角谈content
			if(CommonUtil.listIsNotNull(list4)){
				for(EasyRow row : list4){
					sb.append(row.getColumnValue("CONTENT")+"<br/>");
					noticeNum ++;
				}
			}

			//备忘录
			if(CommonUtil.listIsNotNull(list3)){
				for(EasyRow row : list3){
					sb.append("备忘录:"+row.getColumnValue("CONTENT")+"<br/>");
					noticeNum ++;
				}
			}
			
			//弹屏通知
			if(CommonUtil.listIsNotNull(list2)){
				sb.append("弹屏通知数量:"+list2.size()+"<br/>");
				JSONArray array = new JSONArray();
				for(EasyRow row : list2){
					String title = row.getColumnValue("TITLE");
					String url = row.getColumnValue("URL");
					JSONObject j = new JSONObject();
					j.put("title", title);
					j.put("url", url);
					array.add(j);
					noticeNum ++;
				}
				rjson.put("popNotices", array);
			}
			
			if(noticeNum>0){
				rjson.put("notice", sb.toString());
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询到用户["+userAcc+"]通知:"+sb.toString());
			}
			rjson.put("noticeNum", noticeNum);
			return rjson;
		} catch (SQLException e) {
			rjson.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			rjson.put("respDesc", "程序异常");
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "用户通知查询失败,失败原因：" + e.getMessage(),e);
		}
		return rjson;
	}
}
