package com.yunqu.cc.aiTraining.util.poi;

import com.yunqu.cc.aiTraining.enums.Formats;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import java.awt.Color;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;


public class PoiUtil {
	
	private static Map<String,XSSFCellStyle> styleMap = new HashMap<String,XSSFCellStyle>(); //存储单元格样式的Map 
	
	/**
	 * 初始化格式Map
	 */

	public static void initStyleMap(XSSFWorkbook  book) {
		styleMap.clear();
		styleMap.put("text", getBodyStyle(book));
		styleMap.put("int", getIntStyle(book));
		styleMap.put("double", getDoubleStyle(book));
		styleMap.put("percent", getPercentStyle(book));
		styleMap.put("percent2", getPercent2Style(book));
	}
	
	/** 
     * 合并单元格后给合并后的单元格加边框 
     *  
     * @param region 
     * @param cs 
     */  
	@SuppressWarnings("unused")
	private static void setRegionStyle(CellRangeAddress region, XSSFCellStyle cs,XSSFSheet sheet) {  
        int toprowNum = region.getFirstRow();  
        for (int i = toprowNum; i <= region.getLastRow(); i++)  
        {  
            XSSFRow row = sheet.getRow(i);  
            for (int j = region.getFirstColumn(); j <= region.getLastColumn(); j++)  
            {  
                XSSFCell cell = row.getCell(j); 
                cell.setCellStyle(cs);  
            }  
        }  
    }  
  
    /** 
     * 设置表头的单元格样式 
     *  
     * @return 
     */  
    public static XSSFCellStyle getHeadStyle(XSSFWorkbook wb)  
    {  
    	
        // 创建单元格样式  
        XSSFCellStyle cellStyle = wb.createCellStyle();  
        // 设置单元格的背景颜色为淡蓝色  
        cellStyle.setFillForegroundColor(new XSSFColor(new Color(23,166,240)));  
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);  
        // 设置单元格居中对齐  
        cellStyle.setAlignment(HorizontalAlignment.CENTER);  
        // 设置单元格垂直居中对齐  
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);  
        // 创建单元格内容显示不下时自动换行  
        cellStyle.setWrapText(false);  
        // 设置单元格字体样式  
        XSSFFont font = wb.createFont();  
        // 设置字体加粗  
        font.setBold(true);  
        font.setFontName("宋体");  
        font.setFontHeight((short) 200);  
        cellStyle.setFont(font);  
        // 设置单元格边框为细线条  
        //cellStyle.setBorderLeft(XSSFCellStyle.BORDER_THIN);  
        cellStyle.setBorderLeft(BorderStyle.THIN);  
        cellStyle.setBorderBottom(BorderStyle.THIN);  
        cellStyle.setBorderRight(BorderStyle.THIN);  
        cellStyle.setBorderTop(BorderStyle.THIN);  
        return cellStyle;  
    }  
  
    /** 
     * 设置表体的单元格样式 
     *  
     * @return 
     */  
    public static XSSFCellStyle getBodyStyle(XSSFWorkbook wb)  
    {  
        // 创建单元格样式  
        XSSFCellStyle cellStyle = wb.createCellStyle(); 
        // 设置单元格居中对齐  
        cellStyle.setAlignment(HorizontalAlignment.CENTER);  
        // 设置单元格垂直居中对齐  
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);  
        // 创建单元格内容显示不下时自动换行  
        cellStyle.setWrapText(false);  
        // 设置单元格字体样式  
        XSSFFont font = wb.createFont();  
        // 设置字体加粗  
        font.setBold(false);  
        font.setFontName("宋体");  
        font.setFontHeight((short) 200);  
        cellStyle.setFont(font);  
        // 设置单元格边框为细线条  
        cellStyle.setBorderLeft(BorderStyle.THIN);  
        cellStyle.setBorderBottom(BorderStyle.THIN);  
        cellStyle.setBorderRight(BorderStyle.THIN);  
        cellStyle.setBorderTop(BorderStyle.THIN);  
        return cellStyle;  
    }
/*    

	public static void exportExcel(List<Object[]> list, String[] titles, OutputStream outputStream)  
    {  
        // 创建一个workbook 对应一个excel应用文件  
        XSSFWorkbook workBook = new XSSFWorkbook();  
        // 在workbook中添加一个sheet,对应Excel文件中的sheet  
        XSSFSheet sheet = workBook.createSheet("sheet1");  
        
        XSSFCellStyle headStyle = getHeadStyle(workBook);  
        //XSSFCellStyle bodyStyle = getBodyStyle(workBook);  
        initStyleMap(workBook); 
        // 构建表头  
        XSSFRow headRow = sheet.createRow(0);  
        XSSFCell cell = null;  
        for (int i = 0; i < titles.length; i++)  
        {  
            cell = headRow.createCell(i);  
            cell.setCellStyle(headStyle);  
            cell.setCellValue(titles[i]);  
        }  
        // 构建表体数据  
        if (list != null && list.size() > 0)  
        {  
            for (int j = 0; j < list.size(); j++)  
            {  
                XSSFRow bodyRow = sheet.createRow(j + 1);  
                Object[] arr = list.get(j);
                
                for(int i = 0;i< titles.length;i++){
                	cell = bodyRow.createCell(i);  
                	setMyCellValue(cell,Formats.TEXT,arr[i].toString()); 
                }
            }  
        }  
        try  
        {  
            workBook.write(outputStream);  
            outputStream.flush();  
        }  
        catch (IOException e)  
        {  
            e.printStackTrace();  
        }  
        finally  
        {  
        	try {
				outputStream.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
        }  
  
    }*/
	/**
	 * cell赋值和单元格属性
	 * @param cell
	 * @param value
	 */
    public static void setMyCellValue(XSSFCell cell,Formats format,String value){
    	switch(format){
    		case INT :
    			cell.setCellStyle(styleMap.get(Formats.INT.getKey()));
				cell.setCellValue(Integer.parseInt(value));
    		case DOUBLE :
    			cell.setCellStyle(styleMap.get(Formats.DOUBLE.getKey()));
				cell.setCellValue(Double.parseDouble(value)); 
    		case PERCENT : 
    			cell.setCellStyle(styleMap.get(Formats.PERCENT.getKey()));
				cell.setCellValue(Double.parseDouble(value.replace("%",""))/100);
    		case PERCENT1 :
    			cell.setCellStyle(styleMap.get(Formats.PERCENT1.getKey()));
				cell.setCellValue(Double.parseDouble(value.replace("%",""))/100);
    		case PERCENT2 :
    			cell.setCellStyle(styleMap.get(Formats.PERCENT2.getKey()));
				cell.setCellValue(Double.parseDouble(value.replace("%",""))/100);
			default :
				cell.setCellStyle(styleMap.get(Formats.TEXT.getKey()));
				cell.setCellValue(value);
   	 	}
    }
    
	/** 
     * 获取excel的Workbook 
     * @throws IOException  
     */  
    public static XSSFWorkbook getExcelWorkbook(InputStream inputStream) throws IOException{  
    	XSSFWorkbook  book = null;  
        try {  
        	book = new XSSFWorkbook(inputStream);  
        } catch (Exception e) {  
            throw new RuntimeException(e.getMessage());  
        } finally {  
            if(inputStream != null){  
            	inputStream.close();  
            }  
        }  
        return book;  
    }  
      
    /** 
     * 根据索引 返回Sheet 
     * @param number 
     */  
    public static XSSFSheet  getSheetByNum(XSSFWorkbook book,int number){  
    	XSSFSheet  sheet = null;  
        try {  
            sheet = book.getSheetAt(number-1);  
        } catch (Exception e) {  
            throw new RuntimeException(e.getMessage());  
        }  
        return sheet;  
    }  

    
    /** 
     * 设置单元格数据格式： double数
     * @return 
     */  
	public static XSSFCellStyle getDoubleStyle(XSSFWorkbook wb)  
    {  
        // 创建单元格样式  
        XSSFCellStyle cellStyle = getBodyStyle(wb); 
        // 创建单元格数据格式
        DataFormat df = wb.createDataFormat();
        // 数据格式：数值型
        cellStyle.setDataFormat(df.getFormat("0.0"));
        return cellStyle;  
    }
    
    /** 
     * 设置单元格数据格式： 整数
     * @return 
     */  
	public static XSSFCellStyle getIntStyle(XSSFWorkbook wb)  
    {  
        // 创建单元格样式  
        XSSFCellStyle cellStyle = getBodyStyle(wb); 
        // 创建单元格数据格式
        DataFormat df = wb.createDataFormat();
        // 数据格式：数值型
        cellStyle.setDataFormat(df.getFormat("0"));
        return cellStyle;  
    }
    
    /** 
     * 设置单元格数据格式 百分比0.0%
     * @return 
     */  
	public static XSSFCellStyle getPercentStyle(XSSFWorkbook wb)  
    {  
        // 创建单元格样式  
        XSSFCellStyle cellStyle = getBodyStyle(wb);  
        // 创建单元格数据格式
        DataFormat df = wb.createDataFormat();
        // 数据格式：数值型
        cellStyle.setDataFormat(df.getFormat("0.0%"));
        return cellStyle;  
    }
    
    /** 
     *设置单元格数据格式 百分比0.00%
     * @return 
     */  
	public static XSSFCellStyle getPercent2Style(XSSFWorkbook wb)  
    {  
        // 创建单元格样式  
        XSSFCellStyle cellStyle = getBodyStyle(wb);  
        // 创建单元格数据格式
        DataFormat df = wb.createDataFormat();
        // 数据格式：数值型
        cellStyle.setDataFormat(df.getFormat("0.00%"));
        return cellStyle;  
    }
    
	public static String toLetterString(int number) {  
        if (number < 1) {//   
            return null;  
        }  
        if (number < 27) {  
            return String.valueOf((char) ('A' + number - 1));  
        }  
        if (number % 26 == 0) {  
            return toLetterString(number / 26 - 1) + "Z";  
        }  
        return toLetterString(number / 26)+ String.valueOf((char) ('A' + number % 26 - 1));  
    } 
}
