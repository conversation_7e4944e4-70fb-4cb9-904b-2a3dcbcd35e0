package com.yunqu.cc.aiTraining.servlet;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.aiTraining.base.AppBaseServlet;
import com.yunqu.cc.aiTraining.base.CommonLogger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;
import java.util.*;

/**
 * <AUTHOR>
 */
@WebServlet("/servlet/User/*")
public class UserServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;

    public String actionForUser() {
        HttpServletRequest request = this.getRequest();
        JSONObject entInfo = this.getJSONObject();
        String enable = request.getParameter("enable") == null ? "false" : request.getParameter("enable");//是否多选
        String expandAll = request.getParameter("expandAll") == null ? "false" : request.getParameter("expandAll");//是否全部展开
        String oldIds = request.getParameter("oldIds") == null ? "" : request.getParameter("oldIds");//默认选中的值
        String single = request.getParameter("single") == null ? "false" : request.getParameter("single");//是否判断最低一级（适用单选）
        String oldName = request.getParameter("oldName") == null ? "" : request.getParameter("oldName");//默认选中的值
        String userSingle = request.getParameter("userSingle") == null ? "false" : request.getParameter("userSingle");//默认多选
        String isSecond = request.getParameter("isSecond") == null ? "false" : request.getParameter("isSecond");//是否是当前页面第二个选人页面
        this.setAttr("oldNames", oldName);
        this.setAttr("single", single);
        this.setAttr("expandAll", expandAll);
        this.setAttr("oldIds", oldIds);
        this.setAttr("enable", enable);
        this.setAttr("userSingle", userSingle);
        if (isSecond.equals("true")) {
            return "/pages/user/singlePeople1.jsp";
        }
        if (userSingle.equals("true")) {
            return "/pages/user/singlePeople.jsp";//人员单选界面
        } else {
            return "/pages/user/people.jsp";
        }
    }

    /**
     * 翻译用户名称
     *
     * @return
     */
    public EasyResult actionForGetUser() {
        HttpServletRequest request = this.getRequest();
        JSONObject entInfo = this.getJSONObject();
        String ids = entInfo.getString("ids");
        String[] id = ids.split(",");
        String userAcct = "";
        String userName = "";
        Map<String, String> map = new HashMap<String, String>();
        for (String s : id) {
            userAcct = "".equals(userAcct) ? "'" + s + "'" : userAcct + ",'" + s + "'";
        }
        EasyQuery query = getQuery();
        List<EasyRow> list = new ArrayList<EasyRow>();
        StringBuffer sbsql = new StringBuffer();
        try {

            String replacedWsercodes = userAcct;
            String[] strs = replacedWsercodes.split(",");
            List list1 = Arrays.asList(strs);
            StringBuffer sqlString = new StringBuffer();
            for (int i = 0; i < list1.size(); i++) {
                if (i == (list1.size() - 1)) {
                    sqlString.append(list1.get(i)); //SQL拼装，最后一条不加“,”。  
                } else if ((i % 999) == 0 && i > 0) {
                    sqlString.append(list1.get(i)).append(") or EUL.USER_ACCT  in ("); //解决ORA-01795问题  
                } else {
                    sqlString.append(list1.get(i)).append(",");
                }
            }
            CommonLogger.logger.info(sqlString);
            list = query.queryForList("SELECT DISTINCT  EUL.USER_ACCT,EU.USERNAME   FROM mars.EASI_USER EU  LEFT JOIN mars.EASI_USER_LOGIN EUL ON EU.USER_ID=EUL.USER_ID WHERE EUL.USER_ACCT IN (" + sqlString.toString() + ") ", null);
            for (EasyRow row : list) {
                map.put(row.getColumnValue("USER_ACCT"), row.getColumnValue("USERNAME"));
                //userName="".equals(userName)?row.getColumnValue("USERNAME").toString():userName+","+row.getColumnValue("USERNAME").toString();

            }
        } catch (SQLException e) {
            e.printStackTrace();
            CommonLogger.logger.info(e);
        }
        return EasyResult.ok(map);
    }

    /**
     * 获取输入账号的姓名
     *
     * @return
     */
    public EasyResult queryForGetUsers() {
        CommonLogger.logger.info("进入方法》》》》》");
        JSONObject jsonObject = this.getJSONObject();
        JSONArray array = jsonObject.getJSONArray("id");
        String str = "";
        for (int i = 0; i < array.size(); i++) {
            String ix = array.getString(i);
            if (!"".equals(ix)) {
                str += "'" + ix + "'" + ",";
            }
        }
        CommonLogger.logger.info("方法测试1》》》》》");
        str = str.length() > 0 ? str.substring(0, str.length() - 1) : "''";
        List<JSONObject> lis = new ArrayList<>();
        String sql = "select  DISTINCT ru.username,eul.user_acct "
                + " from mars.EASI_USER  ru left join mars.easi_user_login eul on ru.user_id=eul.user_id  "
                + " where eul.user_acct in(" + str + ") or ru.username  in(" + str + ")";
        try {
            CommonLogger.logger.info("方法测试2》》》》》" + sql);
            List<EasyRow> list = this.getQuery().queryForList(sql, null);
            CommonLogger.logger.info("方法测试3》》》》》" + list.size());
            if (list != null && list.size() > 0) {
                for (EasyRow easyRow : list) {
                    CommonLogger.logger.info("方法测试4》》》》》" + easyRow.toJSONObject().toJSONString());
                    JSONObject obj = new JSONObject();
                    String userName = easyRow.getColumnValue("USERNAME");
                    String userAcct = easyRow.getColumnValue("USER_ACCT");
                    obj.put("userName", userName);
                    obj.put("userAcct", userAcct);
                    lis.add(obj);
                }
            }
            return EasyResult.ok(lis, "");
        } catch (SQLException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return null;
    }
}
