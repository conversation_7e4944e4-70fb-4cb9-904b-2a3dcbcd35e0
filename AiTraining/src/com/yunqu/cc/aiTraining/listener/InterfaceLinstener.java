package com.yunqu.cc.aiTraining.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yq.busi.common.base.ServiceID;
import com.yunqu.cc.aiTraining.base.Constants;

@WebListener
public class InterfaceLinstener extends ServiceContextListener{

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		
		List<ServiceResource> list = new ArrayList<ServiceResource>();

		ServiceResource Resource = new ServiceResource();
		Resource.appName = Constants.APP_NAME;
		Resource.className = "com.yunqu.cc.aiTraining.inf.TrainingInfService"	;
		Resource.description = "AI培训统一处理接口";
		Resource.serviceId = "AI_TRAINING_INTERFACE";
		Resource.serviceName = "AI培训统一处理接口";
		list.add(Resource);

        return list;
	}

}
