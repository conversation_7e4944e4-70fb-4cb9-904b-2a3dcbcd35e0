package com.yunqu.cc.ccreport.dao.traffic;

import com.alibaba.fastjson.JSONObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.yunqu.cc.ccreport.base.CommLogger;


public class TrafficSql {

	/**
	 * 呼入量及接通率报表
	 * @return
	 */
	public static EasySQL notificationSql(String startDate, String endDate) {
		EasySQL sql = new EasySQL();
		sql.append("SELECT * FROM (SELECT T1.ST_DATE,T2.LINE_TYPE_CODE,T2.LINE_TYPE_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,");
		sql.append("SUM(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.ANSWER_CALL_IN)/SUM(T1.TOTAL_CALL_IN))*100,2) || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE T1,C_QUEUE T2");
		sql.append("WHERE t1.QUEUE_ID=t2.ID and T1.ST_TYPE='01'");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append("GROUP BY T1.ST_DATE,T2.LINE_TYPE_CODE,T2.LINE_TYPE_NAME");
		sql.append("ORDER BY T1.ST_DATE DESC,T2.LINE_TYPE_CODE)");
		sql.append("UNION ALL");
		sql.append("SELECT '总计' ST_DATE,'99' LINE_TYPE_CODE,'所有' LINE_TYPE_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,");
		sql.append("SUM(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.ANSWER_CALL_IN)/SUM(T1.TOTAL_CALL_IN))*100,2) || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE T1,C_QUEUE T2");
		sql.append("WHERE t1.QUEUE_ID=t2.ID and T1.ST_TYPE='01'");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		
		CommLogger.logger.debug("呼入量及接通率报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * (315/316)大小电业务统计报表
	 * @return
	 */
	public static EasySQL sizeOfElectricQuerySql(String startDate, String endDate) {
		EasySQL sql = new EasySQL();
		sql.append("SELECT * from (SELECT T1.ST_DATE,T2.HOTLINE_NAME,T2.LINE_TYPE_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,");
		sql.append("SUM(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.ANSWER_CALL_IN)/SUM(T1.TOTAL_CALL_IN))*100,2) || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE T1 ,C_QUEUE T2");
		sql.append("WHERE 1=1 AND T1.ST_TYPE='01'");
		sql.append("AND T2.ID = T1.QUEUE_ID");
		sql.append("AND T2.QUEUE_CODE IS NOT NULL AND T2.HOTLINE_NAME in('315智能热线','316智能热线')");
		sql.append(startDate, "AND T1.ST_DATE >= ?");
		sql.append(endDate, "AND T1.ST_DATE <= ?");
		sql.append("GROUP BY T1.ST_DATE,T2.HOTLINE_NAME,T2.LINE_TYPE_NAME ORDER BY T1.ST_DATE DESC)");
		sql.append("UNION ALL");
		sql.append("SELECT '总计' ST_DATE,'所有' HOTLINE_NAME,'所有' LINE_TYPE_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,");
		sql.append("SUM(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.ANSWER_CALL_IN)/SUM(T1.TOTAL_CALL_IN))*100,2) || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE T1 ,C_QUEUE T2");
		sql.append("WHERE 1=1 AND T1.ST_TYPE='01'");
		sql.append("AND T2.ID = T1.QUEUE_ID");
		sql.append("AND T2.QUEUE_CODE IS NOT NULL AND T2.HOTLINE_NAME in('315智能热线','316智能热线')");
		sql.append(startDate, "AND T1.ST_DATE >= ?");
		sql.append(endDate, "AND T1.ST_DATE <= ?");
		
		CommLogger.logger.debug("(315/316)大小电业务统计报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 各事业部呼入量及接通率报表
	 * @return
	 */
	public static EasySQL divisionSql(String startDate, String endDate) {
		EasySQL sql = new EasySQL();
		sql.append("SELECT * FROM (SELECT T1.ST_DATE,T2.DIVISION_CODE,T2.DIVISION_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,");
		sql.append("SUM(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.ANSWER_CALL_IN)/SUM(T1.TOTAL_CALL_IN))*100,2) || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE T1,C_QUEUE T2");
		sql.append("WHERE t1.QUEUE_ID=t2.ID and T1.ST_TYPE='01'");
		sql.append(startDate, " AND T1.ST_DATE>=?");
		sql.append(endDate, " AND T1.ST_DATE<=?");
		sql.append("GROUP BY T1.ST_DATE,T2.DIVISION_CODE,T2.DIVISION_NAME ORDER BY T1.ST_DATE DESC,T2.DIVISION_CODE)");
		sql.append("UNION ALL");
		sql.append("SELECT '总计' ST_DATE,'99' DIVISION_CODE,'所有' DIVISION_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,");
		sql.append("SUM(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.ANSWER_CALL_IN)/SUM(T1.TOTAL_CALL_IN))*100,2) || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE T1,C_QUEUE T2");
		sql.append("WHERE t1.QUEUE_ID=t2.ID and T1.ST_TYPE='01'");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		
		CommLogger.logger.debug("各事业部呼入量及接通率报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 各热线呼入量及接通率报表
	 * @return
	 */
	public static EasySQL hotlineSql(String startDate, String endDate, String HOTLINE,String stType) {
		if(!"01".equals(stType)){
			endDate = endDate+" 23:59";
			startDate = startDate+" 00:00";
		}
		
		EasySQL sql = new EasySQL();
		sql.append("select * from (SELECT T1.ST_DATE,MAX(T2.HOTLINE) HOTLINE,T2.HOTLINE_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,");
		sql.append("SUM(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.ANSWER_CALL_IN)/SUM(T1.TOTAL_CALL_IN))*100,2) || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE T1 ,C_QUEUE T2");
		sql.append("WHERE t1.QUEUE_ID=t2.ID  ");
		sql.append(stType, "AND T1.ST_TYPE=?");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append(HOTLINE, "AND T2.HOTLINE=?");
		sql.append("GROUP BY T1.ST_DATE,T2.HOTLINE_NAME ORDER BY T1.ST_DATE DESC,HOTLINE)");
		sql.append("UNION ALL");
		sql.append("SELECT '总计' ST_DATE,'99999999' HOTLINE,'所有' HOTLINE_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,");
		sql.append("SUM(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.ANSWER_CALL_IN)/SUM(T1.TOTAL_CALL_IN))*100,2) || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE T1 ,C_QUEUE T2 ");
		sql.append("WHERE t1.QUEUE_ID=t2.ID  ");
		sql.append(stType, "AND T1.ST_TYPE=?");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append(HOTLINE, "AND T2.HOTLINE=?");
		
		CommLogger.logger.debug("各热线呼入量及接通率报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 各业务类型服务情况
	 * @return
	 */
	public static EasySQL serviceSql(String startDate, String endDate) {
		EasySQL sql = new EasySQL();
		sql.append("SELECT * FROM (SELECT T1.ST_DATE,T2.BUSI_CODE,T2.BUSI_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,");
		sql.append("SUM(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.ANSWER_CALL_IN)/SUM(T1.TOTAL_CALL_IN))*100,2) || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE T1,C_QUEUE T2");
		sql.append("WHERE t1.QUEUE_ID=t2.ID and T1.ST_TYPE='01'");
		sql.append(startDate, "AND T1.ST_DATE>=? ");
		sql.append(endDate, " AND T1.ST_DATE<=?");
		sql.append("GROUP BY T1.ST_DATE,T2.BUSI_CODE,T2.BUSI_NAME ORDER BY T1.ST_DATE DESC,T2.BUSI_CODE)");
		sql.append("UNION ALL");
		sql.append("SELECT '总计' ST_DATE,'99' BUSI_CODE,'所有' BUSI_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,");
		sql.append("SUM(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.ANSWER_CALL_IN)/SUM(T1.TOTAL_CALL_IN))*100,2) || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE T1,C_QUEUE T2 ");
		sql.append("WHERE t1.QUEUE_ID=t2.ID and T1.ST_TYPE='01'");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		
		CommLogger.logger.debug("各业务类型服务情况,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}


	/**
	 * 工作量报表需求（按时长达标）
	 * @return
	 */
	public static EasySQL statWorkloadStandardOnTime(JSONObject param) {
		String areaCode = param.getString("areaCode");
		String deptCode = param.getString("deptCode");
		String stType = param.getString("stType");
		String startDate = param.getString("startDate");
		String endDate = param.getString("endDate");
		String userAcc = param.getString("userAcc");
		String userName = param.getString("userName");
		String standardStatus = param.getString("standardStatus");
		if ("04".equals(stType) || "03".equals(stType)) {
			startDate = StringUtils.substring(startDate,0 ,16);
			endDate = StringUtils.substring(endDate,0 ,16);
		}else if ("01".equals(stType)) {
			startDate = StringUtils.substring(startDate,0,10);
			endDate = StringUtils.substring(endDate,0,10);
		}
		String stDate = StringUtils.substring(startDate,0 ,10);;

		String str = "";
		EasySQL sql = new EasySQL();
		if (isReal(startDate, endDate, stType)) {
			stType = "04";
			startDate = startDate + " 00:00";
			endDate = endDate + " 23:59";
			sql.append("SELECT substr(T2.ST_DATE,1,10) ST_DATE,T2.AREA_NAME,T2.USER_ACC,T2.USER_NAME,T2.DEPT_CODE,T2.DEPT_NAME,T2.SCHEDULING_NAME,T2.ENTRY_TIME,");
			sql.append("nvl(sum(T1.LOGIN_LEN),0) LOGIN_LEN,"); //有效在线时长/S
			sql.append("nvl(sum(T1.CALL_IN_NUM),0) CALL_IN_NUM,");
			sql.append("nvl(sum(T1.CALL_OUT_NUM),0) CALL_OUT_NUM,");
			sql.append("nvl(sum(T1.CALLS_IN_LEN),0) CALLS_IN_LEN,");
			sql.append("nvl(sum(T1.TALK_OB_LEN),0) TALK_OB_LEN,");
			sql.append("nvl(sum(T1.READY_LEN),0) READY_LEN,");
			sql.append("nvl(sum(T1.NOT_READY_LEN),0) NOT_READY_LEN,");
			sql.append("nvl(sum(T1.ACW_LEN),0) ACW_LEN,");
			sql.append("nvl(sum(T1.RING_OB_LEN),0) RING_OB_LEN,");
			sql.append("nvl(sum(T1.ANSWER_IN_LEN),0) ANSWER_IN_LEN,");
			sql.append("nvl(sum(T1.LESS_10_NUM),0) LESS_10_NUM,");
			sql.append("nvl(sum(T1.CALLS_IN_LEN)+sum(T1.TALK_OB_LEN)+sum(T1.READY_LEN)+sum(T1.RING_OB_LEN)+sum(T1.ANSWER_IN_LEN),0) EFFE_TIME,");
			sql.append("nvl(sum(T1.CALL_IN_NUM)+sum(T1.CALL_OUT_NUM)-sum(T1.LESS_10_NUM),0) EFFE_NUM,");
			sql.append("nvl(sum(T1.VIDEO_NUM),0) VIDEO_NUM");

			sql.append(",nvl(MAX(T5.STANDARD_VALUE),0) STANDARD_VALUE"); //应达标值
			sql.append(",nvl(MAX(T5.BREASTFEED_LEAVE),0) BREASTFEED_LEAVE"); //是否哺乳假 1是0否
			sql.append(",nvl(MAX(T4.ZX_LONG + T4.ZX_TRAIN_LEN),0) ZX_LONG"); //专项时长(h)=专项时长+培训时长 （这个报表没有把专项和培训时长区分开，所以直接相加）
			sql.append(",nvl(MAX(T5.EARLY_OFF_TIME_LEN),0) EARLY_OFF_TIME_LEN"); //提前下班时长(h)
			sql.append(",nvl(MAX(T5.LEAVE_TIME_LEN),0) LEAVE_TIME_LEN"); //请假时长(h)
			sql.append(",nvl(MAX(T5.REST_TIME_LEN),0) REST_TIME_LEN"); //补休时长(h)
			sql.append(",nvl(MAX(T5.WORK_TIME_LEN),0) WORK_TIME_LEN"); //上班时长(h)
			sql.append("FROM C_ST_AGENT T2 LEFT JOIN C_ST_AGENT_CALL T1 ON T2.ID=T1.ST_AGENT_ID ");
			sql.append(stDate,"LEFT JOIN C_ST_AGENT T6 ON T6.USER_ACC = T2.USER_ACC AND T6.ST_TYPE = '01' AND T6.ST_DATE = ?" );
			sql.append("LEFT JOIN C_ST_AGENT_ZX T4 ON T6.ID=T4.ST_AGENT_ID "); //专项和绩效查当天实时的
			sql.append("LEFT JOIN C_ST_AGENT_JX T5 ON T6.ID=T5.ST_AGENT_ID "); //专项和绩效查当天实时的
			str = "GROUP BY substr(T2.ST_DATE,1,10),T2.AREA_NAME,T2.USER_ACC,T2.USER_NAME,T2.DEPT_CODE,T2.DEPT_NAME,T2.SCHEDULING_NAME,T2.ENTRY_TIME";
		} else {
			sql.append("SELECT T2.ST_DATE,T2.AREA_NAME,T2.USER_ACC,T2.USER_NAME,T2.DEPT_CODE,T2.DEPT_NAME,T2.SCHEDULING_NAME,t2.ENTRY_TIME,");
			sql.append("nvl(T1.LOGIN_LEN,0) LOGIN_LEN,");
			sql.append("nvl(T1.CALL_IN_NUM,0) CALL_IN_NUM,");
			sql.append("nvl(T1.CALL_OUT_NUM,0) CALL_OUT_NUM,");
			sql.append("nvl(T1.CALLS_IN_LEN,0) CALLS_IN_LEN,");
			sql.append("nvl(T1.TALK_OB_LEN,0) TALK_OB_LEN,");
			sql.append("nvl(T1.READY_LEN,0) READY_LEN,");
			sql.append("nvl(T1.NOT_READY_LEN,0) NOT_READY_LEN,");
			sql.append("nvl(T1.ACW_LEN,0) ACW_LEN,");
			sql.append("nvl(T1.RING_OB_LEN,0) RING_OB_LEN,");
			sql.append("nvl(T1.ANSWER_IN_LEN,0) ANSWER_IN_LEN,");
			sql.append("nvl(T1.LESS_10_NUM,0) LESS_10_NUM,");
			sql.append("nvl((T1.CALLS_IN_LEN+T1.TALK_OB_LEN+T1.READY_LEN+T1.RING_OB_LEN+T1.ANSWER_IN_LEN),0) EFFE_TIME,");
			sql.append("nvl((T1.CALL_IN_NUM+T1.CALL_OUT_NUM-T1.LESS_10_NUM),0) EFFE_NUM,");
			sql.append("nvl(T1.VIDEO_NUM,0) VIDEO_NUM");

			sql.append(",nvl(T5.STANDARD_VALUE,0) STANDARD_VALUE"); //应达标值
			sql.append(",nvl(T5.BREASTFEED_LEAVE,0) BREASTFEED_LEAVE"); //是否哺乳假 1是0否
			sql.append(",nvl(T4.ZX_LONG + T4.ZX_TRAIN_LEN,0) ZX_LONG"); //专项时长(h)
			sql.append(",nvl(T5.EARLY_OFF_TIME_LEN,0) EARLY_OFF_TIME_LEN"); //提前下班时长(h)
			sql.append(",nvl(T5.LEAVE_TIME_LEN,0) LEAVE_TIME_LEN"); //请假时长(h)
			sql.append(",nvl(T5.REST_TIME_LEN,0) REST_TIME_LEN"); //补休时长(h)
			sql.append(",nvl(T5.WORK_TIME_LEN,0) WORK_TIME_LEN"); //上班时长(h)
			sql.append("FROM C_ST_AGENT T2 LEFT JOIN C_ST_AGENT_CALL T1 ON T2.ID=T1.ST_AGENT_ID ");
			sql.append("LEFT JOIN C_ST_AGENT T6 ON T6.USER_ACC = T2.USER_ACC AND T6.ST_TYPE = '01' AND T6.ST_DATE = substr(T2.ST_DATE,1,10) " );
			sql.append("LEFT JOIN C_ST_AGENT_ZX T4 ON T6.ID=T4.ST_AGENT_ID "); //专项和绩效查当天实时的
			sql.append("LEFT JOIN C_ST_AGENT_JX T5 ON T6.ID=T5.ST_AGENT_ID "); //专项和绩效查当天实时的
			str = "ORDER BY T2.ST_DATE DESC";
		}
		sql.append("WHERE 1=1 ");
		sql.append(" AND ( T1.ST_AGENT_ID IS NOT NULL OR T4.ST_AGENT_ID IS NOT NULL OR T5.ST_AGENT_ID IS NOT NULL) ");
		sql.append(stType, "AND T2.ST_TYPE = ? ");
		sql.append(startDate, "AND T2.ST_DATE >= ?" );
		sql.append(endDate, "AND T2.ST_DATE <= ? ");
		sql.appendLike(userAcc, "AND T2.USER_ACC LIKE ? ");
		sql.appendLike(userName, "AND T2.USER_NAME LIKE ? ");

		if (StringUtils.isNotBlank(areaCode)) {
			areaCode = areaCode.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.AREA_CODE in (").append(areaCode).append(")");
		}

		if (StringUtils.isNotBlank(deptCode)){
			deptCode = deptCode.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.DEPT_CODE in (").append(deptCode).append(")");
		}
		// 增加达标状态筛选条件
		if (StringUtils.isNotBlank(standardStatus)) {
			sql.append("AND (CASE WHEN (nvl((T1.CALLS_IN_LEN+T1.TALK_OB_LEN+T1.READY_LEN+T1.RING_OB_LEN+T1.ANSWER_IN_LEN),0) - ");
			sql.append("(nvl(T5.STANDARD_VALUE,0) / 8 * (CASE ");
			sql.append("WHEN T2.SCHEDULING_NAME LIKE '%休整%' AND nvl(T5.REST_TIME_LEN,0) <= 0 THEN 0 ");
			sql.append("WHEN nvl(T5.WORK_TIME_LEN,0) >= 8 AND nvl(T5.BREASTFEED_LEAVE,0) = 1 THEN nvl(T5.WORK_TIME_LEN,0) + nvl(T5.REST_TIME_LEN,0) - nvl(T5.EARLY_OFF_TIME_LEN,0) - nvl(T5.LEAVE_TIME_LEN,0) - 1 ");
			sql.append(standardStatus,"ELSE nvl(T5.WORK_TIME_LEN,0) + nvl(T5.REST_TIME_LEN,0) - nvl(T5.EARLY_OFF_TIME_LEN,0) - nvl(T5.LEAVE_TIME_LEN,0) END - nvl(T4.ZX_LONG + T4.ZX_TRAIN_LEN,0)) * 3600)) > -200 THEN '1' ELSE '0' END) = ?");
		}

		sql.append(str);

		CommLogger.logger.info("工作量报表需求（按时长达标）,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		return sql;
	}
	
	/**
	 * 坐席各项时长汇总报表
	 * @return
	 */
	public static EasySQL agentTimeSumQuerySql(String areaCode, String stType, String startDate, 
			String endDate, String userAcc, String userName) {		
		if ("04".equals(stType) || "03".equals(stType)) {
			startDate = startDate + " 00:00";
			endDate = endDate + " 23:59";
		}
		
		String str = "";
		EasySQL sql = new EasySQL();
		if (isReal(startDate, endDate, stType)) {
			stType = "04";
			startDate = startDate + " 00:00";
			endDate = endDate + " 23:59";
			sql.append("SELECT substr(T2.ST_DATE,1,10) ST_DATE,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,T2.AREA_NAME,");
			sql.append("sum(T1.LOGIN_LEN) LOGIN_LEN,sum(T1.CALL_IN_NUM) CALL_IN_NUM,");
			sql.append("sum(T1.CALL_OUT_NUM) CALL_OUT_NUM,sum(T1.CALLS_IN_LEN) CALLS_IN_LEN,");
			sql.append("sum(T1.TALK_OB_LEN) TALK_OB_LEN,sum(T1.READY_LEN) READY_LEN,");
			sql.append("sum(T1.NOT_READY_LEN) NOT_READY_LEN,sum(T1.ACW_LEN) ACW_LEN,");
			sql.append("sum(T1.RING_OB_LEN) RING_OB_LEN,sum(T1.ANSWER_IN_LEN) ANSWER_IN_LEN,");
			sql.append("sum(T1.LESS_10_NUM) LESS_10_NUM,");
			sql.append("sum(T1.CALLS_IN_LEN)+sum(T1.TALK_OB_LEN)+sum(T1.READY_LEN)+sum(T1.RING_OB_LEN)+sum(T1.ANSWER_IN_LEN) EFFE_TIME,");
			sql.append("sum(T1.CALL_IN_NUM)+sum(T1.CALL_OUT_NUM)-sum(T1.LESS_10_NUM) EFFE_NUM,");
			sql.append("sum(T1.VIDEO_NUM) VIDEO_NUM");
			str = "GROUP BY substr(T2.ST_DATE,1,10),T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,T2.AREA_NAME";
		} else {
			sql.append("SELECT T2.ST_DATE,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,T2.AREA_NAME,");
			sql.append("T1.LOGIN_LEN,T1.CALL_IN_NUM,T1.CALL_OUT_NUM,T1.CALLS_IN_LEN,T1.TALK_OB_LEN,T1.READY_LEN,");
			sql.append("T1.NOT_READY_LEN,T1.ACW_LEN,T1.RING_OB_LEN,T1.ANSWER_IN_LEN,T1.LESS_10_NUM,");
			sql.append("(T1.CALLS_IN_LEN+T1.TALK_OB_LEN+T1.READY_LEN+T1.RING_OB_LEN+T1.ANSWER_IN_LEN) EFFE_TIME,");
			sql.append("(T1.CALL_IN_NUM+T1.CALL_OUT_NUM-T1.LESS_10_NUM) EFFE_NUM,");
			sql.append("T1.VIDEO_NUM VIDEO_NUM");
			str = "ORDER BY T2.ST_DATE DESC";
		}
		sql.append("FROM C_ST_AGENT_CALL T1");
		sql.append("LEFT JOIN C_ST_AGENT T2 ON T1.ST_AGENT_ID=T2.ID WHERE 1=1");
		sql.append(stType, "AND T2.ST_TYPE = ?");
		sql.append(startDate, "AND T2.ST_DATE >= ?");
		sql.append(endDate, "AND T2.ST_DATE <= ?");
		sql.appendLike(userAcc, "AND T2.USER_ACC LIKE ?");
		sql.appendLike(userName, "AND T2.USER_NAME LIKE ?");
		
		if (StringUtils.isNotBlank(areaCode)) {
			areaCode = areaCode.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.AREA_CODE in (").append(areaCode).append(")");
		}
		sql.append(str);
		
		CommLogger.logger.debug("坐席各项时长汇总报表,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		return sql;
	}

	/**
	 * 全体客服代表工作量核查报表
	 * @return
	 */
	public static EasySQL agentWorkloadQuerySql(JSONObject param) {
		String areaCode = param.getString("areaCode");
		String stType = param.getString("stType");
		String startDate = param.getString("startDate");
		String endDate = param.getString("endDate");
		String userAcc = param.getString("userAcc");
		String userName= param.getString("userName");
		String agentDept= param.getString("agentDept");
		String standardStatus= param.getString("standardStatus");
		if ("04".equals(stType) || "03".equals(stType)) {
			startDate = startDate + " 00:00";
			endDate = endDate + " 23:59";
		}
		String stDate = StringUtils.substring(startDate,0,10);
		
		String str = "";
		EasySQL sql = new EasySQL();
		if(isReal(startDate, endDate, stType)) {
			stType = "04";
			startDate = startDate + " 00:00";
			endDate = endDate + " 23:59";
			sql.append("SELECT substr(T2.ST_DATE,1,10) ST_DATE,T2.AREA_NAME,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,T1.SCHEDULING_NAME,T2.ENTRY_TIME,");
			sql.append("nvl(sum(T3.CALL_IN_NUM),0)-nvl(sum(T3.TALK_IN_10),0) CALL_IN_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_NUM),0)-nvl(sum(T4.MANUAL_NUM),0)+nvl(sum(T4.MANUAL_ZS_NUM),0) CALL_OUT_ZS_NUM,");					
			sql.append("nvl(sum(T3.CALL_OUT_NUM),0)-nvl(sum(T3.TALK_OB_10),0) CALL_OUT_NUM,");						
			sql.append("nvl(sum(T3.CALL_OUT_ZS_05_NUM),0)+nvl(sum(T4.MANUAL_ZS_05_NUM),0) CALL_OUT_ZS_05_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_075_NUM),0)+nvl(sum(T4.MANUAL_ZS_075_NUM),0) CALL_OUT_ZS_075_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_08_NUM),0)+nvl(sum(T4.MANUAL_ZS_08_NUM),0) CALL_OUT_ZS_08_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_085_NUM),0)+nvl(sum(T4.MANUAL_ZS_085_NUM),0) CALL_OUT_ZS_085_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_09_NUM),0)+nvl(sum(T4.MANUAL_ZS_09_NUM),0) CALL_OUT_ZS_09_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_095_NUM),0)+nvl(sum(T4.MANUAL_ZS_095_NUM),0) CALL_OUT_ZS_095_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_10_NUM),0)-nvl(sum(T4.MANUAL_NUM),0)+nvl(sum(T4.MANUAL_ZS_10_NUM),0) CALL_OUT_ZS_10_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_11_NUM),0)+nvl(sum(T4.MANUAL_ZS_11_NUM),0) CALL_OUT_ZS_11_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_12_NUM),0)+nvl(sum(T4.MANUAL_ZS_12_NUM),0) CALL_OUT_ZS_12_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_13_NUM),0)+nvl(sum(T4.MANUAL_ZS_13_NUM),0) CALL_OUT_ZS_13_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_14_NUM),0)+nvl(sum(T4.MANUAL_ZS_14_NUM),0) CALL_OUT_ZS_14_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_15_NUM),0)+nvl(sum(T4.MANUAL_ZS_15_NUM),0) CALL_OUT_ZS_15_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_16_NUM),0)+nvl(sum(T4.MANUAL_ZS_16_NUM),0) CALL_OUT_ZS_16_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_17_NUM),0)+nvl(sum(T4.MANUAL_ZS_17_NUM),0) CALL_OUT_ZS_17_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_18_NUM),0)+nvl(sum(T4.MANUAL_ZS_18_NUM),0) CALL_OUT_ZS_18_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_20_NUM),0)+nvl(sum(T4.MANUAL_ZS_20_NUM),0) CALL_OUT_ZS_20_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_22_NUM),0)+nvl(sum(T4.MANUAL_ZS_22_NUM),0) CALL_OUT_ZS_22_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_23_NUM),0)+nvl(sum(T4.MANUAL_ZS_23_NUM),0) CALL_OUT_ZS_23_NUM,");		
			sql.append("nvl(sum(T3.CALL_OUT_ZS_25_NUM),0)+nvl(sum(T4.MANUAL_ZS_25_NUM),0) CALL_OUT_ZS_25_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_27_NUM),0)+nvl(sum(T4.MANUAL_ZS_27_NUM),0) CALL_OUT_ZS_27_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_30_NUM),0)+nvl(sum(T4.MANUAL_ZS_30_NUM),0) CALL_OUT_ZS_30_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_35_NUM),0)+nvl(sum(T4.MANUAL_ZS_35_NUM),0) CALL_OUT_ZS_35_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_40_NUM),0)+nvl(sum(T4.MANUAL_ZS_40_NUM),0) CALL_OUT_ZS_40_NUM,");
			sql.append("nvl(sum(T3.CALL_OUT_ZS_45_NUM),0)+nvl(sum(T4.MANUAL_ZS_45_NUM),0) CALL_OUT_ZS_45_NUM,");				
			sql.append("nvl(sum(T3.CALL_IN_ZS_NUM),0) CALL_IN_ZS_NUM,");
			sql.append("nvl(sum(T3.LESS_10_NUM),0) LESS_10_NUM,");
			sql.append("nvl(sum(T4.SP_ZS_NUM),0) SP_ZS_NUM,");
			sql.append("nvl(sum(T3.CALL_IN_NUM),0)-nvl(sum(T3.TALK_IN_10),0)+nvl(sum(T3.CALL_IN_ZS_NUM),0)+nvl(sum(T3.CALL_OUT_ZS_NUM),0)-nvl(sum(T4.MANUAL_NUM),0)+nvl(sum(T4.MANUAL_ZS_NUM),0)+nvl(sum(T4.SP_ZS_NUM),0) PURE_NUM,");
			sql.append("nvl(sum(T4.ZX_LONG),0) ZX_LONG,");
			sql.append("nvl(sum(T4.ZX_TRAIN_LEN),0)+nvl(sum(T5.TRAIN_LEN),0) TRAIN_LEN,");
			sql.append("(nvl(sum(T4.ZX_LONG),0)+nvl(sum(T4.ZX_TRAIN_LEN),0)+nvl(sum(T5.TRAIN_LEN),0)) * 17.5 ZX_ZS_NUM,");
			sql.append("nvl(sum(T3.CALL_IN_NUM),0)-nvl(sum(T3.TALK_IN_10),0)+nvl(sum(T3.CALL_IN_ZS_NUM),0)+nvl(sum(T3.CALL_OUT_ZS_NUM),0)-nvl(sum(T4.MANUAL_NUM),0)+nvl(sum(T4.MANUAL_ZS_NUM),0)+nvl(sum(T4.SP_ZS_NUM),0)+(nvl(sum(T4.ZX_LONG),0)+nvl(sum(T4.ZX_TRAIN_LEN),0)+nvl(sum(T5.TRAIN_LEN),0))*17.5 TOTAL,");
			sql.append("140/8*MAX(T1.TIMELONG) DB_NUM,");
			sql.append("nvl(sum(T3.CALL_IN_NUM),0)-nvl(sum(T3.TALK_IN_10),0)+nvl(sum(T3.CALL_IN_ZS_NUM),0)+nvl(sum(T3.CALL_OUT_ZS_NUM),0)-nvl(sum(T4.MANUAL_NUM),0)+nvl(sum(T4.MANUAL_ZS_NUM),0)+nvl(sum(T4.SP_ZS_NUM),0)+(nvl(sum(T4.ZX_LONG),0)+nvl(sum(T4.ZX_TRAIN_LEN),0)+nvl(sum(T5.TRAIN_LEN),0))*17.5 - (140/8*MAX(T1.TIMELONG)) DIFF_NUM");

			sql.append(",nvl(MAX(T8.STANDARD_VALUE),0) STANDARD_VALUE"); //应达标值
			sql.append(",nvl(MAX(T8.BREASTFEED_LEAVE),0) BREASTFEED_LEAVE"); //是否哺乳假 1是0否
			sql.append(",nvl(MAX(T8.EARLY_OFF_TIME_LEN),0) EARLY_OFF_TIME_LEN"); //提前下班时长(h)
			sql.append(",nvl(MAX(T8.LEAVE_TIME_LEN),0) LEAVE_TIME_LEN"); //请假时长(h)
			sql.append(",nvl(MAX(T8.REST_TIME_LEN),0) REST_TIME_LEN"); //补休时长(h)
			sql.append(",nvl(MAX(T8.WORK_TIME_LEN),0) WORK_TIME_LEN"); //上班时长(h)
			sql.append("FROM C_PB_SCHEDULING_PERSON T1 LEFT JOIN C_ST_AGENT T2 ON T1.USER_ACC=T2.USER_ACC AND T1.PLAN_DATE=substr(T2.ST_DATE,1,10)");
//		sql.append("LEFT JOIN C_YG_EMPLOYEE EMP ON EMP.USER_ACC=T2.USER_ACC");
			sql.append("LEFT JOIN C_ST_AGENT_CALL T3 ON T2.ID=T3.ST_AGENT_ID");
			sql.append("LEFT JOIN C_ST_AGENT_OTHER T5 ON T2.ID=T5.ST_AGENT_ID");
			sql.append(stDate,"LEFT JOIN C_ST_AGENT day ON day.USER_ACC = T2.USER_ACC AND day.ST_TYPE = '01' AND day.ST_DATE = ?" );
			sql.append("LEFT JOIN C_ST_AGENT_ZX T4 ON day.ID=T4.ID");
			sql.append("LEFT JOIN C_ST_AGENT_JX T8 ON day.ID=T8.ST_AGENT_ID");
			str = "GROUP BY substr(T2.ST_DATE,1,10),T2.AREA_NAME,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,T1.SCHEDULING_NAME,T2.ENTRY_TIME";
		} else {
			sql.append("SELECT T2.ST_DATE,T2.AREA_NAME,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,T1.SCHEDULING_NAME,T2.ENTRY_TIME,");
			sql.append("nvl(T3.CALL_IN_NUM,0)-nvl(T3.TALK_IN_10,0) CALL_IN_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_NUM,0)-nvl(T4.MANUAL_NUM,0)+nvl(T4.MANUAL_ZS_NUM,0) CALL_OUT_ZS_NUM,");			
			sql.append("nvl(T3.CALL_OUT_NUM,0)-nvl(T3.TALK_OB_10,0) CALL_OUT_NUM,");						
			sql.append("nvl(T3.CALL_OUT_ZS_05_NUM,0)+nvl(T4.MANUAL_ZS_05_NUM,0) CALL_OUT_ZS_05_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_075_NUM,0)+nvl(T4.MANUAL_ZS_075_NUM,0) CALL_OUT_ZS_075_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_08_NUM,0)+nvl(T4.MANUAL_ZS_08_NUM,0) CALL_OUT_ZS_08_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_085_NUM,0)+nvl(T4.MANUAL_ZS_085_NUM,0) CALL_OUT_ZS_085_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_09_NUM,0)+nvl(T4.MANUAL_ZS_09_NUM,0) CALL_OUT_ZS_09_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_095_NUM,0)+nvl(T4.MANUAL_ZS_095_NUM,0) CALL_OUT_ZS_095_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_10_NUM,0)-nvl(T4.MANUAL_NUM,0)+nvl(T4.MANUAL_ZS_10_NUM,0) CALL_OUT_ZS_10_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_11_NUM,0)+nvl(T4.MANUAL_ZS_11_NUM,0) CALL_OUT_ZS_11_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_12_NUM,0)+nvl(T4.MANUAL_ZS_12_NUM,0) CALL_OUT_ZS_12_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_13_NUM,0)+nvl(T4.MANUAL_ZS_13_NUM,0) CALL_OUT_ZS_13_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_14_NUM,0)+nvl(T4.MANUAL_ZS_14_NUM,0) CALL_OUT_ZS_14_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_15_NUM,0)+nvl(T4.MANUAL_ZS_15_NUM,0) CALL_OUT_ZS_15_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_16_NUM,0)+nvl(T4.MANUAL_ZS_16_NUM,0) CALL_OUT_ZS_16_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_17_NUM,0)+nvl(T4.MANUAL_ZS_17_NUM,0) CALL_OUT_ZS_17_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_18_NUM,0)+nvl(T4.MANUAL_ZS_18_NUM,0) CALL_OUT_ZS_18_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_20_NUM,0)+nvl(T4.MANUAL_ZS_20_NUM,0) CALL_OUT_ZS_20_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_22_NUM,0)+nvl(T4.MANUAL_ZS_22_NUM,0) CALL_OUT_ZS_22_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_23_NUM,0)+nvl(T4.MANUAL_ZS_23_NUM,0) CALL_OUT_ZS_23_NUM,");		
			sql.append("nvl(T3.CALL_OUT_ZS_25_NUM,0)+nvl(T4.MANUAL_ZS_25_NUM,0) CALL_OUT_ZS_25_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_27_NUM,0)+nvl(T4.MANUAL_ZS_27_NUM,0) CALL_OUT_ZS_27_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_30_NUM,0)+nvl(T4.MANUAL_ZS_30_NUM,0) CALL_OUT_ZS_30_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_35_NUM,0)+nvl(T4.MANUAL_ZS_35_NUM,0) CALL_OUT_ZS_35_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_40_NUM,0)+nvl(T4.MANUAL_ZS_40_NUM,0) CALL_OUT_ZS_40_NUM,");
			sql.append("nvl(T3.CALL_OUT_ZS_45_NUM,0)+nvl(T4.MANUAL_ZS_45_NUM,0) CALL_OUT_ZS_45_NUM,");				
			sql.append("nvl(T3.CALL_IN_ZS_NUM,0) CALL_IN_ZS_NUM,");
			sql.append("nvl(T3.LESS_10_NUM,0) LESS_10_NUM,");
			sql.append("nvl(T4.SP_ZS_NUM,0) SP_ZS_NUM,");
			sql.append("nvl(T3.CALL_IN_NUM,0)-nvl(T3.TALK_IN_10,0)+nvl(T3.CALL_IN_ZS_NUM,0)+nvl(T3.CALL_OUT_ZS_NUM,0)-nvl(T4.MANUAL_NUM,0)+nvl(T4.MANUAL_ZS_NUM,0)+nvl(T4.SP_ZS_NUM,0) PURE_NUM,");
			sql.append("nvl(T4.ZX_LONG,0) ZX_LONG,");//专项时长(h)
			sql.append("nvl(T4.ZX_TRAIN_LEN,0)+nvl(T5.TRAIN_LEN,0) TRAIN_LEN,");
			sql.append("(nvl(T4.ZX_LONG,0)+nvl(T4.ZX_TRAIN_LEN,0)+nvl(T5.TRAIN_LEN,0)) * 17.5 ZX_ZS_NUM,");
			sql.append("nvl(T3.CALL_IN_NUM,0)-nvl(T3.TALK_IN_10,0)+nvl(T3.CALL_IN_ZS_NUM,0)+nvl(T3.CALL_OUT_ZS_NUM,0)-nvl(T4.MANUAL_NUM,0)+nvl(T4.MANUAL_ZS_NUM,0)+nvl(T4.SP_ZS_NUM,0)+(nvl(T4.ZX_LONG,0)+nvl(T4.ZX_TRAIN_LEN,0)+nvl(T5.TRAIN_LEN,0))*17.5 TOTAL,");
			sql.append("140/8*T1.TIMELONG DB_NUM,");
			sql.append("nvl(T3.CALL_IN_NUM,0)-nvl(T3.TALK_IN_10,0)+nvl(T3.CALL_IN_ZS_NUM,0)+nvl(T3.CALL_OUT_ZS_NUM,0)-nvl(T4.MANUAL_NUM,0)+nvl(T4.MANUAL_ZS_NUM,0)+nvl(T4.SP_ZS_NUM,0)+(nvl(T4.ZX_LONG,0)+nvl(T4.ZX_TRAIN_LEN,0)+nvl(T5.TRAIN_LEN,0))*17.5 - (140/8*T1.TIMELONG) DIFF_NUM");

			sql.append(",nvl(T8.STANDARD_VALUE,0) STANDARD_VALUE"); //应达标值
			sql.append(",nvl(T8.BREASTFEED_LEAVE,0) BREASTFEED_LEAVE"); //是否哺乳假 1是0否
			sql.append(",nvl(T8.EARLY_OFF_TIME_LEN,0) EARLY_OFF_TIME_LEN"); //提前下班时长(h)
			sql.append(",nvl(T8.LEAVE_TIME_LEN,0) LEAVE_TIME_LEN"); //请假时长(h)
			sql.append(",nvl(T8.REST_TIME_LEN,0) REST_TIME_LEN"); //补休时长(h)
			sql.append(",nvl(T8.WORK_TIME_LEN,0) WORK_TIME_LEN"); //上班时长(h)
			sql.append("FROM C_PB_SCHEDULING_PERSON T1 LEFT JOIN C_ST_AGENT T2 ON T1.USER_ACC=T2.USER_ACC AND T1.PLAN_DATE=substr(T2.ST_DATE,1,10)");
			sql.append("LEFT JOIN C_ST_AGENT_CALL T3 ON T2.ID=T3.ST_AGENT_ID");
			sql.append("LEFT JOIN C_ST_AGENT_OTHER T5 ON T2.ID=T5.ST_AGENT_ID");
			sql.append("LEFT JOIN C_ST_AGENT T6 ON T6.USER_ACC = T2.USER_ACC AND T6.ST_TYPE = '01' AND T6.ST_DATE = T1.PLAN_DATE" );
			sql.append("LEFT JOIN C_ST_AGENT_ZX T4 ON T6.ID=T4.ST_AGENT_ID ");
			sql.append("LEFT JOIN C_ST_AGENT_JX T8 ON T6.ID=T8.ST_AGENT_ID "); //绩效查当天实时的
			str = "ORDER BY T2.ST_DATE DESC";
		}

		sql.append("WHERE T2.ST_TYPE = '" + stType + "'");
		sql.append(startDate, "AND T2.ST_DATE >= ?");
		sql.append(endDate, "AND T2.ST_DATE <= ?");
		sql.appendLike(userAcc, "AND T2.USER_ACC LIKE ?");
		sql.appendLike(userName, "AND T2.USER_NAME LIKE ?");

		if (StringUtils.isNotBlank(areaCode)) {
			areaCode = areaCode.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.AREA_CODE in (").append(areaCode).append(")");
		}

		if (StringUtils.isNotBlank(agentDept)){
			agentDept = agentDept.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.DEPT_CODE in (").append(agentDept).append(")");
		}
		// 增加达标状态筛选条件
		if (StringUtils.isNotBlank(standardStatus)) {
			sql.append(" AND (CASE WHEN (nvl(T3.CALL_IN_NUM,0)-nvl(T3.TALK_IN_10,0)+nvl(T3.CALL_IN_ZS_NUM,0)+nvl(T3.CALL_OUT_ZS_NUM,0)-nvl(T4.MANUAL_NUM,0)+nvl(T4.MANUAL_ZS_NUM,0)+nvl(T4.SP_ZS_NUM,0)+(nvl(T4.ZX_LONG,0)+nvl(T4.ZX_TRAIN_LEN,0)+nvl(T5.TRAIN_LEN,0))*17.5) - ");
			sql.append("(130/8 * (CASE ");
			sql.append("WHEN T3.SCHEDULING_NAME LIKE '%休整%' AND nvl(T5.REST_TIME_LEN,0) <= 0 THEN 0 ");
			sql.append("WHEN nvl(T8.WORK_TIME_LEN,0) >= 8 AND nvl(T8.BREASTFEED_LEAVE,0) = 1 THEN nvl(T8.WORK_TIME_LEN,0) + nvl(T8.REST_TIME_LEN,0) - nvl(T8.EARLY_OFF_TIME_LEN,0) - nvl(T8.LEAVE_TIME_LEN,0) - 1 ");
			sql.append(standardStatus,"ELSE nvl(T8.WORK_TIME_LEN,0) + nvl(T8.REST_TIME_LEN,0) - nvl(T8.EARLY_OFF_TIME_LEN,0) - nvl(T8.LEAVE_TIME_LEN,0) END)) >= 0 THEN '1' ELSE '0' END) = ?");
		}
		sql.append(str);
		
		CommLogger.logger.info("全体客服代表工作量核查报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 客服代表每日加扣分明细报表
	 * @return
	 */
	public static EasySQL addPointsQuerySql(String areaCode, String startDate, String endDate, 
			String userAcc, String userName) {
		EasySQL sql = new EasySQL();
		sql.append("SELECT T2.AREA_NAME,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,");
		sql.append("sum(T1.PLUS_POINTS) PLUS_POINTS,sum(T1.DEDUCT_POINTS) DEDUCT_POINTS, ");
		sql.append("sum(T1.PLUS_POINTS)-sum(T1.DEDUCT_POINTS) TOTAL_POINT");
		sql.append("FROM C_ST_AGENT_OTHER T1 ");
		sql.append("LEFT JOIN C_ST_AGENT T2 ON T1.ST_AGENT_ID = T2. ID");
		sql.append("WHERE T2.ST_TYPE = '01' AND (T1.PLUS_POINTS!=0 OR T1.DEDUCT_POINTS!=0)");
		sql.append(startDate, "AND T2.ST_DATE >= ?");
		sql.append(endDate, "AND T2.ST_DATE <= ?");
		sql.appendLike(userAcc, "AND T2.USER_ACC LIKE ?");
		sql.appendLike(userName, "AND T2.USER_NAME LIKE ?");

		if (StringUtils.isNotBlank(areaCode)) {
			areaCode = areaCode.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.AREA_CODE in (").append(areaCode).append(")");
		}
		sql.append("GROUP BY T2.AREA_NAME,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME");
		
		CommLogger.logger.debug("客服代表每日加扣分明细报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 各维度呼入话务量及接通率报表
	 * @return
	 */
	public static EasySQL dimenCallInQuerySql(String dimension, String stType,
			String startDate, String endDate) {
		boolean flag = isReal(startDate, endDate, stType);
		if ("04".equals(stType) || "03".equals(stType)) {
			startDate = startDate + " 00:00";
			endDate = endDate + " 23:59";
		}
		
		String str = "";
		EasySQL sql = new EasySQL();
		if(flag) {
			stType = "04";
			startDate = startDate + " 00:00";
			endDate = endDate + " 23:59";		
		} 

		if(flag && dimension.equals("01") ) {
			sql.append("select substr(t1.ST_DATE,1,10) ST_DATE,t2.DIVISION_NAME NAME");
			str = "group by substr(t1.ST_DATE,1,10),t2.DIVISION_NAME order by t2.DIVISION_NAME desc";
		}
		else if(!flag && dimension.equals("01")) {
			sql.append("select t1.ST_DATE,t2.DIVISION_NAME NAME");
			str = "group by t1.ST_DATE,t2.DIVISION_NAME order by t1.ST_DATE desc,t2.DIVISION_NAME desc";
		}
		else if(flag && dimension.equals("02")) {
			sql.append("select substr(t1.ST_DATE,1,10) ST_DATE,t2.BUSI_NAME NAME");
			str = "group by substr(t1.ST_DATE,1,10),t2.BUSI_NAME order by t2.BUSI_NAME desc";
		}
		else if(!flag && dimension.equals("02")) {
			sql.append("select t1.ST_DATE,t2.BUSI_NAME NAME");
			str = "group by t1.ST_DATE,t2.BUSI_NAME order by t1.ST_DATE desc,t2.BUSI_NAME desc";
		}
		else if(flag && dimension.equals("03")) {
			sql.append("select substr(t1.ST_DATE,1,10) ST_DATE,t2.DIVISION_NAME||'~'||t2.BUSI_NAME NAME");
			str = "group by substr(t1.ST_DATE,1,10),t2.DIVISION_NAME||'~'||t2.BUSI_NAME order by t2.DIVISION_NAME||'~'||t2.BUSI_NAME desc";
		}
		else if(!flag && dimension.equals("03")) {
			sql.append("select t1.ST_DATE,t2.DIVISION_NAME||'~'||t2.BUSI_NAME NAME");
			str = "group by t1.ST_DATE,t2.DIVISION_NAME||'~'||t2.BUSI_NAME order by t1.ST_DATE desc,t2.DIVISION_NAME||'~'||t2.BUSI_NAME desc";
		}
	
		sql.append(",sum(t1.TOTAL_CALL_IN) TOTAL_CALL_IN,sum(t1.ANSWER_CALL_IN) ANSWER_CALL_IN,sum(t1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
		sql.append("case when sum(t1.TOTAL_CALL_IN)=0 then '0' else ROUND(sum(t1.ANSWER_CALL_IN)/sum(t1.TOTAL_CALL_IN), 4) * 100 || '%' end ANSWER_RATE");
		sql.append("FROM C_ST_QUEUE t1").append("LEFT JOIN C_QUEUE t2 ON t1.QUEUE_ID = t2.ID");
		sql.append("WHERE 1=1");
		sql.append(stType, "AND t1.ST_TYPE = ?");
		sql.append(startDate, "AND t1.ST_DATE >= ?");
		sql.append(endDate, "AND t1.ST_DATE <= ?");
		sql.append(str);
		
		CommLogger.logger.debug("各维度呼入话务量及接通率报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 各区域呼入话务量实时统计
	 * @return
	 */
	public static EasySQL areaCountQuerySql(String areaCode, String lineCode,String stType, 
			String startDate, String endDate) {
		if ("04".equals(stType) || "03".equals(stType)) {
			startDate = startDate + " 00:00";
			endDate = endDate + " 23:59";
		}
		
		String str = "";
		EasySQL sql = new EasySQL();
		if (isReal(startDate, endDate, stType)) {
			stType = "04";
			startDate = startDate + " 00:00";
			endDate = endDate + " 23:59";
			sql.append("select substr(T3.ST_DATE,1,10) ST_DATE,T3.AREA_NAME,T3.LINE_TYPE_NAME,");
			sql.append("sum(T3.TOTAL_CALL_IN) TOTAL_CALL_IN,");
			sql.append("sum(T3.ANSWER_CALL_IN) ANSWER_CALL_IN,");
			sql.append("sum(T3.TOTAL_CALL_OF) TOTAL_CALL_OF,");
			sql.append("case when sum(T3.TOTAL_CALL_IN)=0 then '0%' else ROUND(sum(T3.ANSWER_CALL_IN)/sum(T3.TOTAL_CALL_IN) ,4) * 100 || '%' end ANSWER_RATE,");
			sql.append("sum(T3.MANUAL_CALL_OUT) MANUAL_CALL_OUT,sum(T3.TOTAL_CALL_IN)+sum(T3.MANUAL_CALL_OUT) CALL_NUM FROM (");
			sql.append("select T1.ST_DATE,T2.AREA_NAME,T2.LINE_TYPE_NAME,"); 
			sql.append("sum(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,sum(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,sum(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,"); 
			sql.append("max(T1.MANUAL_CALL_OUT) MANUAL_CALL_OUT");
			str = "group by T1.ST_DATE,T2.AREA_NAME,T2.LINE_TYPE_NAME)T3 group by substr(T3.ST_DATE,1,10),T3.AREA_NAME,T3.LINE_TYPE_NAME";
		} else {
			sql.append("select T1.ST_DATE,T2.AREA_NAME,T2.LINE_TYPE_NAME,");
			sql.append("sum(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,sum(T1.ANSWER_CALL_IN) ANSWER_CALL_IN,sum(T1.TOTAL_CALL_OF) TOTAL_CALL_OF,");
			sql.append("case when sum(T1.TOTAL_CALL_IN)=0 then '0%' else ROUND(sum(T1.ANSWER_CALL_IN)/sum(T1.TOTAL_CALL_IN) ,4) * 100 || '%' end ANSWER_RATE,");
			sql.append("max(T1.MANUAL_CALL_OUT) MANUAL_CALL_OUT,sum(T1.TOTAL_CALL_IN)+max(T1.MANUAL_CALL_OUT) CALL_NUM");
			str = "group by T1.ST_DATE,T2.AREA_NAME,T2.LINE_TYPE_NAME order by T1.ST_DATE desc";
		}		
		sql.append("FROM C_ST_QUEUE T1 LEFT JOIN C_QUEUE T2 ON T1.QUEUE_ID = T2.ID");
		sql.append("WHERE 1=1");
		sql.append(stType, "AND T1.ST_TYPE = ?");
		sql.append(areaCode, "AND T2.AREA_CODE = ?");
		sql.append(lineCode, "AND T2.LINE_TYPE_CODE = ?");
		sql.append(startDate, "AND T1.ST_DATE >= ?");
		sql.append(endDate, "AND T1.ST_DATE <= ?");
		sql.append(str);
		
		CommLogger.logger.debug("各区域呼入话务量实时统计,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 坐席话务量报表
	 * @return
	 */
	public static EasySQL agenTrafficQuerySql(String areaCode, String stType, String startDate, 
			String endDate, String userAcc, String userName,String agentClass,String agentBc) {		
		if ("01".equals(stType)&&StringUtils.isNotBlank(startDate)&&StringUtils.isNotBlank(endDate)) {
			startDate = startDate.substring(0, 10);
			endDate = endDate.substring(0, 10);
		}
		
		String str = "";
		EasySQL sql = new EasySQL();
		if (isReal(startDate, endDate, stType)) {
			stType = "04";
			sql.append("SELECT substr(T2.ST_DATE,1,10) ST_DATE,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,T2.SCHEDULING_NAME,");
			sql.append("MIN(T1.FIRST_CALL_TIME) FIRST_CALL_TIME,");
			sql.append("sum(T1.CALL_IN_NUM) CALL_IN_NUM,sum(T1.CALLS_IN_LEN) CALLS_IN_LEN,");
			sql.append("sum(T1.CALL_OUT_NUM) CALL_OUT_NUM,sum(T1.TALK_OB_LEN) TALK_OB_LEN,");
			sql.append("sum(T1.CALL_IN_NUM)+sum(T1.CALL_OUT_NUM) TOTAL_NUM,");
			sql.append("sum(T1.CALLS_IN_LEN)+sum(T1.TALK_OB_LEN) TOTAL_LEN,");
			sql.append("case when sum(T1.PLAT_CALL_IN_NUM)=0 then 0 else round(sum(T1.READY_LEN)/sum(T1.PLAT_CALL_IN_NUM),2) end AVG_WAIT_LEN");
			str = "GROUP BY substr(T2.ST_DATE,1,10) ,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,T2.SCHEDULING_NAME";
		} else {
			sql.append("SELECT T2.ST_DATE,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,T2.SCHEDULING_NAME,T1.FIRST_CALL_TIME,");
			sql.append("T1.CALL_IN_NUM, T1.CALLS_IN_LEN, T1.CALL_OUT_NUM, T1.TALK_OB_LEN,");
			sql.append("T1.CALL_IN_NUM+T1.CALL_OUT_NUM TOTAL_NUM,");
			sql.append("T1.CALLS_IN_LEN+T1.TALK_OB_LEN TOTAL_LEN,T1.AVG_WAIT_LEN");
			str = "ORDER BY T2.ST_DATE DESC";
		}
		sql.append("FROM C_ST_AGENT_CALL T1");
		sql.append("LEFT JOIN C_ST_AGENT T2 ON T1.ST_AGENT_ID = T2.ID");
		sql.append("WHERE 1=1");
		sql.append(stType, "AND T2.ST_TYPE =?");
		sql.append(startDate, "AND T2.ST_DATE >= ?");
		sql.append(endDate, "AND T2.ST_DATE <= ?");
		sql.appendLike(userAcc, "AND T2.USER_ACC LIKE ?");
		sql.appendLike(userName, "AND T2.USER_NAME LIKE ?");
		
		if (StringUtils.isNotBlank(areaCode)) {
			areaCode = areaCode.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.AREA_CODE in (").append(areaCode).append(")");
		}
		if(StringUtils.isNotBlank(agentClass)) {
			agentClass = agentClass.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.DEPT_CODE IN (" + agentClass + ")");
		}
		if(StringUtils.isNotBlank(agentBc)) {
			agentBc = agentBc.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.SCHEDULING_ID IN(" + agentBc + ")");
		}
		sql.append(str);
		
		CommLogger.logger.debug("坐席话务量报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 坐席十分钟内未产生话务统计报表
	 * @return
	 */
	public static EasySQL tenNoTrafficQuerySql(String areaCode, String startDate, 
			String endDate, String userAcc, String userName) {
		EasySQL sql = new EasySQL();
		sql.append("SELECT t2.ST_DATE,t2.USER_ACC,t2.USER_NAME,t2.DEPT_NAME,t2.SCHEDULING_NAME,");
		sql.append("t1.BF_10_CALL_TIME,t1.AF_10_CALL_TIME,t1.NO_CALL_10_LEN ");
		sql.append("FROM C_ST_AGENT_NO_CALL t1 LEFT JOIN C_ST_AGENT t2 ON t1.ST_AGENT_ID=t2.ID ");
		sql.append("WHERE t2.ST_TYPE = '01'");
		sql.append(startDate, "AND t2.ST_DATE >= ?");
		sql.append(endDate, "AND t2.ST_DATE <= ?");
		sql.appendLike(userAcc, "AND t2.USER_ACC LIKE ?");
		sql.appendLike(userName, "AND t2.USER_NAME LIKE ?");
		
		if (StringUtils.isNotBlank(areaCode)) {
			areaCode = areaCode.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.AREA_CODE in (").append(areaCode).append(")");
		}
		sql.append("ORDER BY t2.ST_DATE DESC,t2.USER_ACC,t1.AF_10_CALL_TIME");
		
		CommLogger.logger.debug("坐席十分钟内未产生话务统计报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 各区域呼入话务量实时增涨统计报表
	 * @return
	 */
	public static EasySQL areaCallInGrowthRateQuerySql(String areaCode, String lineCode, String stType, 
			String startDate, String endDate, String time) {		
		if ("04".equals(stType) || "03".equals(stType)) {
			startDate = startDate + " " + time;
			endDate = endDate + " " + time;
		}
		
		EasySQL sql = new EasySQL();
		sql.append("select t3.AREA_NAME,t3.LINE_TYPE_NAME,");
		sql.append("t3.ST_DATE1,t3.TOTAL_CALL_IN1,t3.ANSWER_CALL_IN1,t3.ANSWER_RATE1,");
		sql.append("t6.ST_DATE2,t6.TOTAL_CALL_IN2,t6.ANSWER_CALL_IN2,t6.ANSWER_RATE2,");
		sql.append("case when t3.TOTAL_CALL_IN1=0 or t6.TOTAL_CALL_IN2 is null then '0%' else round((t6.TOTAL_CALL_IN2-t3.TOTAL_CALL_IN1)/t3.TOTAL_CALL_IN1 ,4) * 100 || '%' end GROWTH_RATE");
		sql.append("from");
		sql.append("(select t1.ST_DATE ST_DATE1,t2.AREA_NAME,t2.LINE_TYPE_NAME,");
		sql.append("sum(t1.TOTAL_CALL_IN) TOTAL_CALL_IN1,sum(t1.ANSWER_CALL_IN) ANSWER_CALL_IN1,");
		sql.append("case when sum(t1.TOTAL_CALL_IN)=0 then '0%' else round(sum(t1.ANSWER_CALL_IN)/sum(t1.TOTAL_CALL_IN) ,4) * 100 || '%' end ANSWER_RATE1"); 
		sql.append("from C_ST_QUEUE t1 left join C_QUEUE t2 ON t1.QUEUE_ID=t2.ID");
		sql.append("where 1=1");
		sql.append(areaCode, "and t2.AREA_CODE=?");
		sql.append(lineCode, "and t2.LINE_TYPE_CODE=?");
		sql.append(stType, "and t1.ST_TYPE=?");
		sql.append(startDate, "and t1.ST_DATE=?");
		sql.append("group by t1.ST_DATE,t2.AREA_NAME,t2.LINE_TYPE_NAME)t3"); 
		sql.append("left join"); 
		sql.append("(select t4.ST_DATE ST_DATE2,t5.AREA_NAME,t5.LINE_TYPE_NAME,");
		sql.append("sum(t4.TOTAL_CALL_IN) TOTAL_CALL_IN2,sum(t4.ANSWER_CALL_IN) ANSWER_CALL_IN2,");
		sql.append("case when sum(t4.TOTAL_CALL_IN)=0 then '0%' else round(sum(t4.ANSWER_CALL_IN)/sum(t4.TOTAL_CALL_IN) ,4) * 100 || '%' end ANSWER_RATE2"); 
		sql.append("from C_ST_QUEUE t4 left join C_QUEUE t5 ON t4.QUEUE_ID=t5.ID");
		sql.append("where 1=1");
		sql.append(areaCode, "and t5.AREA_CODE=?");
		sql.append(lineCode, "and t5.LINE_TYPE_CODE=?");
		sql.append(stType, "and t4.ST_TYPE=?");
		sql.append(endDate, "and t4.ST_DATE=?");
		sql.append("group by t4.ST_DATE,t5.AREA_NAME,t5.LINE_TYPE_NAME)t6"); 
		sql.append("on t3.AREA_NAME=t6.AREA_NAME and t3.LINE_TYPE_NAME=t6.LINE_TYPE_NAME");
		sql.append("order by t3.AREA_NAME,t3.LINE_TYPE_NAME");
		
		CommLogger.logger.debug("各区域呼入话务量实时增涨统计报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 专项明细报表
	 * @return
	 */
	public static EasySQL specificDetailsQuerySql(String source, String typeName, String modelName, String content,
			String startDate, String endDate) {
		EasySQL sql = new EasySQL("SELECT SOURCE_NAME,TYPE_NAME,MODEL_NAME,CONTENT,")
				.append("CREATE_USER_NAME,WORK_TIME,ZX_LONG,WORK_NAME,").append("WORK_ACC,FK_STATUS,ACTUAL_WORK_LONG,")
				.append("FK_BACKUP,BC_NAME,BACKUP FROM C_ZX_WORK ").append("WHERE 1 = 1")
				.appendLike(source, "AND SOURCE_NAME LIKE ?").appendLike(typeName, "AND TYPE_NAME LIKE ?")
				.appendLike(modelName, "AND MODEL_NAME LIKE ?").appendLike(content, "AND CONTENT LIKE ?")
				.append(startDate, "AND WORK_TIME >= ?").append(endDate, "AND WORK_TIME <= ?")
				.append("ORDER BY CREATE_TIME DESC");
		
		CommLogger.logger.debug("专项明细报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * KPI指标完成报表
	 * @return
	 */
	public static EasySQL kpiCompleteQuerySql(String areaCode, String startDate, String endDate, String stType) {		
		if ("03".equals(stType)) {
			startDate = startDate + " 00:00";
			endDate = endDate + " 23:59";
		}

		EasySQL sql = new EasySQL();
		sql.append("SELECT T2.ST_DATE,T2.AREA_NAME,(T1.UTILIZATION_RATE || '%') UTILIZATION_RATE,");
		sql.append("(T1.NET_UTILIZATION_RATE || '%') NET_UTILIZATION_RATE,");
		sql.append("(T1.SERVICE_IN_20S || '%') SERVICE_IN_20S,");
		sql.append("T1.TRANSFER_NUM,(T1.TRANSFER_RATE || '%') TRANSFER_RATE");
		sql.append("FROM C_ST_AREA_LY T1").append("LEFT JOIN C_ST_AREA T2 ON T1.ST_AREA_ID = T2.ID");
		sql.append("WHERE 1 = 1");
		sql.append(stType, " AND T2.ST_TYPE = ?");
		sql.append(startDate, "AND T2.ST_DATE >= ?");
		sql.append(endDate, "AND T2.ST_DATE <= ?");
		
		if (StringUtils.isNotBlank(areaCode)) {
			areaCode = areaCode.replace("[", "").replace("]", "").replace("\"", "'");
			sql.append("AND T2.AREA_CODE in (").append(areaCode).append(")");
		}
		sql.append(" ORDER BY T2.ST_DATE DESC");
		
		CommLogger.logger.debug("KPI指标完成报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	/**
	 * 全体客服代表绩效考核报表
	 * @return
	 */
	public static EasySQL agentPerformanceQuerySql(String areaCode, String startDate, String endDate, String workNo,
			String userName) {
		EasySQL sql = new EasySQL("SELECT T2.ST_DATE,T2.AREA_NAME,T2.USER_ACC,T2.USER_NAME,T2.DEPT_NAME,")
				.append("T1.WORK_SCORE,T1.QUALITY_SCORE,T1.SATISF_SCORE,T1.TRAINING_SCORE,T1.BASE_SOCRE,T1.AB_SCORE,")
				.append("(T1.QUALITY_SCORE + T1.SATISF_SCORE + T1.TRAINING_SCORE + T1.BASE_SOCRE + T1.AB_SCORE) COMPOSITE_SCORES")
				.append("FROM C_ST_JX_SCORE T1 LEFT JOIN C_ST_AGENT T2 ON T1.ST_AGENT_ID = T2.ID")
				.append("LEFT JOIN C_YG_EMPLOYEE T3 ON T2.USER_ACC = T3.USER_ACC").append("WHERE 1 = 1")
				.append(areaCode, "AND T2.AREA_CODE = ?").append(startDate, "AND T2.ST_DATE >= ?")
				.append(endDate, "AND T2.ST_DATE <= ?").appendLike(workNo, "AND T2.USER_ACC LIKE ?")
				.appendLike(userName, "AND T2.USER_NAME LIKE ?").append(" ORDER BY T2.ST_DATE");
		
		CommLogger.logger.debug("全体客服代表绩效考核报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}
	
	/**
	 * 地区大小电互转统计报表
	 * @return
	 */
	public static EasySQL areaTransformQuerySql(String areaCode, String transformCode, String startDate, String endDate) {
		EasySQL sql = new EasySQL();
		sql.append("select * from(select ST_DATE,AREA_NAME,TRANSFORM_NAME,");
		sql.append("TRANSFORM_NUM from C_ST_QUEUE_TRANSFORM");
		sql.append("where ST_TYPE='01'");
		sql.append(areaCode, "and AREA_CODE=?");
		sql.append(transformCode, "and TRANSFORM_CODE=?");
		sql.append(startDate, "and ST_DATE>=?");
		sql.append(endDate, "and ST_DATE<=?");
		sql.append("order by ST_DATE desc)");
		sql.append("union all");
		sql.append("select '总计' ST_DATE,'所有' AREA_NAME,'所有' TRANSFORM_NAME,");
		sql.append("sum(TRANSFORM_NUM) from C_ST_QUEUE_TRANSFORM");
		sql.append("where ST_TYPE='01'");
		sql.append(areaCode, "and AREA_CODE=?");
		sql.append(transformCode, "and TRANSFORM_CODE=?");
		sql.append(startDate, "and ST_DATE>=?");
		sql.append(endDate, "and ST_DATE<=?");
		
		CommLogger.logger.debug("地区大小电互转统计报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

	
	public static boolean isReal(String startDate, String endDate, String stType) {
		String today = EasyCalendar.newInstance().getDateTime("-").substring(0, 10);
		if(StringUtils.isNotBlank(startDate)){
			startDate = startDate.substring(0, 10);
		}
		if(StringUtils.isNotBlank(endDate)){
			endDate = endDate.substring(0, 10);
		}
		if (today.equals(startDate) && today.equals(endDate) && "01".equals(stType)) {
			return true;
		}
		return false;
	}
	
	/**
	 * 各事业部主体外呼量统计报表
	 * @return
	 */
	public static EasySQL busiDeptCallStatSql(String startDate, String endDate) {
		if(StringUtils.isNotBlank(startDate)){
			startDate= startDate+" 00:00:00";
		}
		if(StringUtils.isNotBlank(endDate)){
			endDate= endDate+" 23:59:59";
		}
		EasySQL sql = new EasySQL();
		sql.append("SELECT B.NAME BUSI_DEPT,");
		sql.append("SUM(CASE WHEN ICC_BUSI_TYPE IS NULL THEN 1 ELSE  0 END) NOT_RETURN_VISIT_COUNT,");
		sql.append("SUM(CASE WHEN ICC_BUSI_TYPE IS NOT NULL THEN 1 ELSE 0 END) RETURN_VISIT_COUNT,");
		sql.append("COUNT(1) S_RETURN_VISIT_COUNT");
		sql.append("FROM C_PF_CALL_RECORD E,");
		sql.append("(SELECT CODE, NAME FROM YWDB.C_CF_DICT  WHERE DICT_GROUP_ID = '84764967055459998988841') B");
		sql.append("WHERE DIRECTION = '02'   AND B.NAME  is not null ");
		sql.append(startDate, "AND E.RINGING_TIME>=?");
		sql.append(endDate, "AND E.RINGING_TIME<=?");
		sql.append("AND E.ICC_DIVISION_ID = B.CODE(+)");
		sql.append("GROUP BY B.NAME");
		sql.append("ORDER BY RETURN_VISIT_COUNT DESC");
		
		CommLogger.logger.debug("各事业部主体外呼量统计报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}
	
	/**
	 * 查询队列的呼入量，排队总时长，平均排队时长
	 * @return
	 */
	public static EasySQL queueWaitSql(String startDate, String endDate,JSONArray hotline,String stType) {
		if(!"01".equals(stType)){
			endDate = endDate+" 23:59";
			startDate = startDate+" 00:00";
		}
		EasySQL sql = new EasySQL();
		sql.append("SELECT * FROM (SELECT T1.ST_DATE,T2.HOTLINE_NAME,T2.QUEUE_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.QUEUE_DURATION) TOTAL_WAIT_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then 0 else round((SUM(T1.QUEUE_DURATION)/SUM(T1.TOTAL_CALL_IN)),2) end AVG_WAIT_TIME ");
		sql.append("FROM C_NO_QUEUE_CALL_NEW T1,C_QUEUE T2");
		sql.append("WHERE t1.QUEUE_ID=T2.QUEUE_CODE ");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append(stType, "AND T1.ST_TYPE=?");
		if(hotline!=null&&hotline.size()>0) {
			sql.append("AND T2.HOTLINE_NAME in ( ");
			for (int i = 0; i < hotline.size(); i++) {
				sql.append(hotline.getString(i), " ? ");
				if(i<hotline.size()-1) {
					sql.append( ",");
				}
			}
			sql.append(" ) ");
		}
		sql.append("GROUP BY T1.ST_DATE,T2.HOTLINE_NAME,T2.QUEUE_NAME");
		sql.append("ORDER BY T1.ST_DATE DESC,T2.HOTLINE_NAME,T2.QUEUE_NAME)");
		sql.append("UNION ALL");
		sql.append("SELECT '总计' ST_DATE,'所有' HOTLINE_NAME,'所有' QUEUE_NAME, ");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.QUEUE_DURATION) TOTAL_WAIT_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then 0 else round((SUM(T1.QUEUE_DURATION)/SUM(T1.TOTAL_CALL_IN)),2) end AVG_WAIT_TIME ");
		sql.append("FROM C_NO_QUEUE_CALL_NEW T1 ");
		sql.append("WHERE 1 = 1 ");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append(stType, "AND T1.ST_TYPE=?");
		if(hotline!=null&&hotline.size()>0) {
			sql.append("AND T2.HOTLINE_NAME in ( ");
			for (int i = 0; i < hotline.size(); i++) {
				sql.append(hotline.getString(i), " ? ");
				if(i<hotline.size()-1) {
					sql.append( ",");
				}
			}
			sql.append(" ) ");
		}
		
		CommLogger.logger.info("语音各队列排队数据报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}
	
	/**
	 * 查询技能组的呼入量，排队总时长，平均排队时长
	 * @return
	 */
	public static EasySQL groupWaitSql(String startDate, String endDate,String stType) {
		if(!"01".equals(stType)){
			endDate = endDate+" 23:59";
			startDate = startDate+" 00:00";
		}
		EasySQL sql = new EasySQL();
		sql.append("SELECT * FROM (SELECT T1.ST_DATE,T2.SKILL_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.QUEUE_DURATION) TOTAL_WAIT_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then 0 else round((SUM(T1.QUEUE_DURATION)/SUM(T1.TOTAL_CALL_IN)),2) end AVG_WAIT_TIME ");
		sql.append("FROM C_NO_QUEUE_CALL_NEW T1,C_QUEUE T2");
		sql.append("WHERE t1.QUEUE_ID=T2.QUEUE_CODE ");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append(stType, "AND T1.ST_TYPE=?");
		sql.append("GROUP BY T1.ST_DATE,T2.SKILL_NAME");
		sql.append("ORDER BY T1.ST_DATE DESC,T2.SKILL_NAME)");
		sql.append("UNION ALL");
		sql.append("SELECT '总计' ST_DATE,'所有' SKILL_NAME, ");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.QUEUE_DURATION) TOTAL_WAIT_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then 0 else round((SUM(T1.QUEUE_DURATION)/SUM(T1.TOTAL_CALL_IN)),2) end AVG_WAIT_TIME ");
		sql.append("FROM C_NO_QUEUE_CALL_NEW T1 ");
		sql.append("WHERE 1 = 1 ");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append(stType, "AND T1.ST_TYPE=?");
		
		CommLogger.logger.info("语音各技能排队数据报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}
	
	/**
	 * 查询队列的呼入量、10S接通量、10S服务水平、20S接通量、20S服务水平、30S接通量、30S服务水平
	 * @return
	 */
	public static EasySQL queueServiceSql(String startDate, String endDate,JSONArray hotline,String stType) {
		if(!"01".equals(stType)){
			endDate = endDate+" 23:59";
			startDate = startDate+" 00:00";
		}
		EasySQL sql = new EasySQL();
		sql.append("SELECT * FROM (SELECT T1.ST_DATE,T2.HOTLINE_NAME,T2.QUEUE_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.TOTAL_ANSW_10_TIME) TOTAL_ANSW_10_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_10_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_10_RATE, ");
		sql.append("SUM(T1.TOTAL_ANSW_20_TIME) TOTAL_ANSW_20_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_20_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_20_RATE, ");
		sql.append("SUM(T1.TOTAL_ANSW_30_TIME) TOTAL_ANSW_30_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_30_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_30_RATE ");
		sql.append("FROM C_NO_QUEUE_CALL_NEW T1,C_QUEUE T2");
		sql.append("WHERE t1.QUEUE_ID=T2.QUEUE_CODE ");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append(stType, "AND T1.ST_TYPE=?");
		
		if(hotline!=null&&hotline.size()>0) {
			sql.append("AND T2.HOTLINE_NAME in ( ");
			for (int i = 0; i < hotline.size(); i++) {
				sql.append(hotline.getString(i), " ? ");
				if(i<hotline.size()-1) {
					sql.append( ",");
				}
			}
			sql.append(" ) ");
		}
		sql.append("GROUP BY T1.ST_DATE,T2.HOTLINE_NAME,T2.QUEUE_NAME");
		sql.append("ORDER BY T1.ST_DATE DESC,T2.HOTLINE_NAME,T2.QUEUE_NAME)");
		sql.append("UNION ALL");
		sql.append("SELECT '总计' ST_DATE,'所有' HOTLINE_NAME,'所有' QUEUE_NAME, ");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.TOTAL_ANSW_10_TIME) TOTAL_ANSW_10_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_10_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_10_RATE, ");
		sql.append("SUM(T1.TOTAL_ANSW_20_TIME) TOTAL_ANSW_20_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_20_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_20_RATE, ");
		sql.append("SUM(T1.TOTAL_ANSW_30_TIME) TOTAL_ANSW_30_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_30_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_30_RATE ");
		sql.append("FROM C_NO_QUEUE_CALL_NEW T1 ");
		sql.append("WHERE 1 = 1 ");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append(stType, "AND T1.ST_TYPE=?");
		if(hotline!=null&&hotline.size()>0) {
			sql.append("AND T2.HOTLINE_NAME in ( ");
			for (int i = 0; i < hotline.size(); i++) {
				sql.append(hotline.getString(i), " ? ");
				if(i<hotline.size()-1) {
					sql.append( ",");
				}
			}
			sql.append(" ) ");
		}
		
		CommLogger.logger.info("语音各队列服务水平数据报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}
	
	/**
	 * 查询技能组的呼入量、10S接通量、10S服务水平、20S接通量、20S服务水平、30S接通量、30S服务水平
	 * @return
	 */
	public static EasySQL groupServiceSql(String startDate, String endDate,String stType) {
		if(!"01".equals(stType)){
			endDate = endDate+" 23:59";
			startDate = startDate+" 00:00";
		}
		EasySQL sql = new EasySQL();
		sql.append("SELECT * FROM (SELECT T1.ST_DATE,T2.SKILL_NAME,");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.TOTAL_ANSW_10_TIME) TOTAL_ANSW_10_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_10_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_10_RATE, ");
		sql.append("SUM(T1.TOTAL_ANSW_20_TIME) TOTAL_ANSW_20_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_20_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_20_RATE, ");
		sql.append("SUM(T1.TOTAL_ANSW_30_TIME) TOTAL_ANSW_30_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_30_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_30_RATE ");
		sql.append("FROM C_NO_QUEUE_CALL_NEW T1,C_QUEUE T2");
		sql.append("WHERE t1.QUEUE_ID=T2.QUEUE_CODE ");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append(stType, "AND T1.ST_TYPE=?");
		sql.append("GROUP BY T1.ST_DATE,T2.SKILL_NAME");
		sql.append("ORDER BY T1.ST_DATE DESC,T2.SKILL_NAME)");
		sql.append("UNION ALL");
		sql.append("SELECT '总计' ST_DATE,'所有' SKILL_NAME, ");
		sql.append("SUM(T1.TOTAL_CALL_IN) TOTAL_CALL_IN,");
		sql.append("SUM(T1.TOTAL_ANSW_10_TIME) TOTAL_ANSW_10_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_10_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_10_RATE, ");
		sql.append("SUM(T1.TOTAL_ANSW_20_TIME) TOTAL_ANSW_20_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_20_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_20_RATE, ");
		sql.append("SUM(T1.TOTAL_ANSW_30_TIME) TOTAL_ANSW_30_TIME,");
		sql.append("case when SUM(T1.TOTAL_CALL_IN)=0 then '0%' else round((SUM(T1.TOTAL_ANSW_30_TIME)/SUM(T1.TOTAL_CALL_IN))*100,2)|| '%' end ANSW_30_RATE ");
		sql.append("FROM C_NO_QUEUE_CALL_NEW T1 ");
		sql.append("WHERE 1 = 1 ");
		sql.append(startDate, "AND T1.ST_DATE>=?");
		sql.append(endDate, "AND T1.ST_DATE<=?");
		sql.append(stType, "AND T1.ST_TYPE=?");
		
		CommLogger.logger.info("语音各技能服务水平数据报表,sql=" + sql.getSQL() + "{" + JSON.toJSONString(sql.getParams()) + "}");
		return sql;
	}

}
