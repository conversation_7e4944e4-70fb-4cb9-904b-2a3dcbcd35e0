<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.yunqu.midea</groupId>
        <artifactId>cc</artifactId>
        <version>2.5.3</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>mixgw</artifactId>
    <packaging>war</packaging>
    <version>${cc-media.version}</version>

    <dependencies>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>sdk-core-java</artifactId>
            <version>1.1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/WebContent/WEB-INF/lib/sdk-core-java-1.1.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>sdk-core-java-sources</artifactId>
            <version>1.1.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/WebContent/WEB-INF/lib/sdk-core-java-1.1.0-sources.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.11.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/WebContent/WEB-INF/lib/java-jwt-3.11.0.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.13.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/WebContent/WEB-INF/lib/jackson-databind-2.13.3.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.13.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/WebContent/WEB-INF/lib/jackson-core-2.13.3.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.13.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/WebContent/WEB-INF/lib/jackson-annotations-2.13.3.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.0.M3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/WebContent/WEB-INF/lib/hutool-all-5.8.0.M3.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.github.kevinsawicki</groupId>
            <artifactId>http-request</artifactId>
            <version>5.6</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/WebContent/WEB-INF/lib/http-request-5.6.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/WebContent/WEB-INF/lib/commons-httpclient-3.0.jar</systemPath>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>src</sourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <webResources>
                        <resource>
                            <directory>WebContent</directory>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.yunqu.mars.plugin</groupId>
                <artifactId>console-upgrade</artifactId>
                <version>1.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-version</goal>
                            <goal>push-mars</goal>
                        </goals>
                        <configuration>
                            <cleanLibDirectory>false</cleanLibDirectory>
                            <enableWarPushMars>false</enableWarPushMars>
                            <primaryVersion>2.5.3</primaryVersion>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>
</project> 