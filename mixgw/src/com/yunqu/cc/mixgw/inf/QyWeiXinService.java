package com.yunqu.cc.mixgw.inf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.kevinsawicki.http.HttpRequest;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.utils.CsUtil;
import com.yunqu.cc.mixgw.utils.McspUtil;
import com.yunqu.cc.mixgw.utils.WeiXinParamesUtil;
import com.yunqu.cc.mixgw.utils.WeiXinUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import org.springframework.util.DigestUtils;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import static com.yq.busi.common.util.IDGenerator.getDefaultNUMID;

/**
 * 处理与企业微信系统通用数据相关对接的接口
 *
 * <AUTHOR>
 */
public class QyWeiXinService extends IService {
    private static Logger longTime = CommonLogger.getCommLogger("longTime");

	public static EasyCache cache = CacheManager.getMemcache();

	private static final Map<String, Function<JSONObject, JSONObject>> COMMAND_FUNC_CACHE = new HashMap<>();
	private static EasyQuery easyQuery =  EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	public static Logger logger = CommonLogger.getCommLogger("wechat");
	public static Logger detailLogger = CommonLogger.getCommLogger("wechatDetail");
	public static final String SUCCESS_CODE = "000000";

	static {
		COMMAND_FUNC_CACHE.put("getuserinfo", QyWeiXinService::getuserinfo);
		COMMAND_FUNC_CACHE.put("getWechatDepartment", QyWeiXinService::getWechatDepartment);
		COMMAND_FUNC_CACHE.put("getWechatUser", QyWeiXinService::getWechatUser);
		COMMAND_FUNC_CACHE.put("getWechatTagGroupList", QyWeiXinService::getWechatTagGroupList);
		COMMAND_FUNC_CACHE.put("getWechatTagList", QyWeiXinService::getWechatTagList);
		COMMAND_FUNC_CACHE.put("addCrowdPack", QyWeiXinService::addCrowdPack);
		COMMAND_FUNC_CACHE.put("pushCrowdPack", QyWeiXinService::pushCrowdPack);
		COMMAND_FUNC_CACHE.put("queryCrowdPack", QyWeiXinService::queryCrowdPack);
		COMMAND_FUNC_CACHE.put("deleteCrowdPack", QyWeiXinService::deleteCrowdPack);
		COMMAND_FUNC_CACHE.put("uploadMaterial", QyWeiXinService::uploadMaterial);
		COMMAND_FUNC_CACHE.put("createService", QyWeiXinService::createService);
		COMMAND_FUNC_CACHE.put("queryUserDetail", QyWeiXinService::queryUserDetail);
		COMMAND_FUNC_CACHE.put("queryCustomerDetail", QyWeiXinService::queryCustomerDetail);
		COMMAND_FUNC_CACHE.put("queryWechatUserDetailInfo", QyWeiXinService::queryWechatUserDetailInfo);
		COMMAND_FUNC_CACHE.put("genExclusiveUrl", QyWeiXinService::genExclusiveUrl);
		COMMAND_FUNC_CACHE.put("agentJSSDKInit", QyWeiXinService::agentJSSDKInit);
		COMMAND_FUNC_CACHE.put("getExtCustInfo", QyWeiXinService::getExtCustInfo);
		COMMAND_FUNC_CACHE.put("getUserIdByCode", QyWeiXinService::getUserIdByCode);
		COMMAND_FUNC_CACHE.put("syncWeComRecord", QyWeiXinService::getUserIdByCode);//勿动
		COMMAND_FUNC_CACHE.put("getWeComOSSFileCode", QyWeiXinService::getWeComOSSFileCertification);
		COMMAND_FUNC_CACHE.put("synGroupState", QyWeiXinService::synGroupState);
		COMMAND_FUNC_CACHE.put("getGroupMasterAcc", QyWeiXinService::getGroupMasterAcc);
		COMMAND_FUNC_CACHE.put("sendWxButtonCardMessage", QyWeiXinService::sendWxButtonCardMessage);
	}


	@Override
	public JSONObject invoke(JSONObject resqJson) throws ServiceException {
		logger.info("QyWeiXinService.invoke====>>>>resqJson:"+resqJson);
		String command = resqJson.getString("command");
		long startTime = System.currentTimeMillis();
        try {
        	Function<JSONObject, JSONObject> fun = COMMAND_FUNC_CACHE.get(command);
    		if (fun == null) {
    			JSONObject result = new JSONObject();
    			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
    			result.put("respDesc", "不存在的command,请检查！");
    			return result;
    		} else {
    			if ("syncWeComRecord".equals(command)) {
    				return syncWeComRecord(resqJson.getJSONArray("params"));
    			}
				if("synGroupState".equals(command)){
					return synGroupState(resqJson.getJSONObject("data"));
				}
    			JSONObject reqParam = resqJson.getJSONObject("params");
    			if ("getuserinfo".equals(command)) {
    				reqParam = resqJson;
    			}
    			return fun.apply(reqParam);
    		}
		} finally {
			long endTime = System.currentTimeMillis();
			long elapsedTime = endTime - startTime;
			longTime.info(command+"接口"+elapsedTime);
		}
	}

	public static JSONObject getuserinfo(JSONObject json) {
		CsUtil.addIndexSize(json);
		String entId = json.getString("entId");
		String code = json.getString("code");
		JSONObject result = new JSONObject();
		JSONObject parseObject = new JSONObject();
		try {
			parseObject = WeiXinUtil.getuserinfo(entId, code);
			CommonLogger.logger.info("获取访问用户身份返回结果：" + parseObject.toJSONString());
			result.put("respData", parseObject);
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
		} catch (Exception e) {
			CommonLogger.logger.info("查询失败原因：" + e.getMessage(), e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}
	
	public static JSONObject getUserIdByCode(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/mobile/getUserIdByCode";
		JSONObject result = new JSONObject();
		try {
			String code = infParam.getString("code");
			String agentId = infParam.getString("agentId");
			JSONObject restParam = new JSONObject();
			restParam.put("code", code);
			restParam.put("agentId", agentId);
			restParam.put("corpId", Constants.QIYE_CORPID);
			//HttpResp resp = sendPost(url,params);
			HttpRequest resp = sendPost(url, restParam);
			String resultStr = resp.body();
			logger.info("[QyWeiXinService.doHttp]======<<<<url=" + url + ",result：" + resultStr);
			result.put("respData", resultStr);
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
		} catch (Exception e) {
			logger.error("[QyWeiXinService.getWechatDepartment]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}

	/**
	 * 请求接口默认系统ID
	 *
	 * @date 2022/4/11 14:16
	 */
	private static final String REQ_SYS_ID = "CSS_INC";

	public static synchronized JSONObject getWechatDepartment(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/enterpriseWechatManage/getWechatDepartment";
		JSONObject result = new JSONObject();
		try {
			String departmentId = infParam.getString("departmentId");
			JSONObject restParam = new JSONObject();
			restParam.put("sysId", REQ_SYS_ID);
			restParam.put("departmentId", departmentId);
			JSONObject params = new JSONObject();
			params.put("restParams", restParam);
			//HttpResp resp = sendPost(url,params);
			String appKey = Constants.getWechatWorkAppKey();
			String appSecret = Constants.getWechatWorkSecret();
			String resultStr = McspUtil.post(url, params.toJSONString(), Constants.MCSP_AK_TYPE_JWT, appKey,appSecret);

			logger.info("[QyWeiXinService.doHttp]======<<<<url=" + url + ",resultStr:" + resultStr);
			if (StringUtils.isNotBlank(resultStr)) {
				JSONObject jo = JSON.parseObject(resultStr);
				if (jo != null && StringUtils.equals(jo.getString("code"), SUCCESS_CODE)) {
					result.put("respData", jo);
				}
			}
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
		} catch (Exception e) {
			logger.error("[QyWeiXinService.getWechatDepartment]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}

	public static synchronized JSONObject getWechatUser(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl()  + "/wechat/enterpriseWechatManage/getWechatUser";
		JSONObject result = new JSONObject();
		try {
			String departmentId = infParam.getString("departmentId");
			JSONObject restParam = new JSONObject();
			restParam.put("sysId", REQ_SYS_ID);
			restParam.put("departmentId", departmentId);
			JSONObject params = new JSONObject();
			params.put("restParams", restParam);
			/*HttpResp resp = sendPost(url,params);
			int code = resp.getCode();
			String resultStr = resp.getResult();*/
			String appKey = Constants.getWechatWorkAppKey();
			String appSecret = Constants.getWechatWorkSecret();
			String resultStr = McspUtil.post(url, params.toJSONString(), Constants.MCSP_AK_TYPE_JWT, appKey,appSecret);
			logger.info("[QyWeiXinService.doHttp]======<<<<url=" + url +",resultStr：" + resultStr);
			if (StringUtils.isNotBlank(resultStr)) {
				JSONObject jo = JSON.parseObject(resultStr);
				if (jo != null && StringUtils.equals(jo.getString("code"), SUCCESS_CODE)) {
					result.put("respData", jo);
				}
			}
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
		} catch (Exception e) {
			logger.error("[QyWeiXinService.getWechatUser]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}

	public static synchronized JSONObject getWechatTagGroupList(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl()  + "/wechat/customerTag/getWechatTagGroupList";
		JSONObject result = new JSONObject();
		try {
			String tagGroupId = infParam.getString("tagGroupId");
			int pageNo = infParam.getInteger("pageNo");
			int pageSize = infParam.getInteger("pageSize");
			if (pageNo <= 0 || pageSize <= 0) {
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "入参异常");
				return result;
			}
			JSONObject restParam = new JSONObject();
			restParam.put("sysId", REQ_SYS_ID);
			restParam.put("tagGroupId", tagGroupId);
			JSONObject pagination = new JSONObject();
			pagination.put("countFlag", true);
			pagination.put("pageNo", pageNo);
			pagination.put("pageSize", pageSize);

			JSONObject params = new JSONObject();
			params.put("restParams", restParam);
			params.put("pagination", pagination);

			/*HttpResp resp = sendPost(url,params);
			int code = resp.getCode();
			String resultStr = resp.getResult();*/
			String appKey = Constants.getWechatWorkAppKey();
			String appSecret = Constants.getWechatWorkSecret();
			String resultStr = McspUtil.post(url, params.toJSONString(), Constants.MCSP_AK_TYPE_JWT, appKey,appSecret);
			logger.info("[QyWeiXinService.doHttp]======<<<<url=" + url + ",result：" + resultStr);
			if (StringUtils.isNotBlank(resultStr)) {
				JSONObject jo = JSON.parseObject(resultStr);
				if (jo != null && StringUtils.equals(jo.getString("code"), SUCCESS_CODE)) {
					result.put("respData", jo);
				}
			}
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
		} catch (Exception e) {
			logger.error("[QyWeiXinService.getWechatTagGroupList]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}

	public static synchronized JSONObject getWechatTagList(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/customerTag/getWechatTagList";
		JSONObject result = new JSONObject();
		try {
			String tagGroupId = infParam.getString("tagGroupId");
			String searchCondition = infParam.getString("searchCondition");
			int pageNo = infParam.getInteger("pageNo");
			int pageSize = infParam.getInteger("pageSize");
			if (pageNo <= 0 || pageSize <= 0) {
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "入参异常");
				return result;
			}

			JSONObject restParam = new JSONObject();
			restParam.put("sysId", REQ_SYS_ID);
			restParam.put("tagGroupId", tagGroupId);
			restParam.put("searchCondition", searchCondition);
			JSONObject pagination = new JSONObject();
			pagination.put("countFlag", true);
			pagination.put("pageNo", pageNo);
			pagination.put("pageSize", pageSize);

			JSONObject params = new JSONObject();
			params.put("restParams", restParam);
			params.put("pagination", pagination);

			/*HttpResp resp = sendPost(url,params);
			int code = resp.getCode();
			String resultStr = resp.getResult();*/
			String appKey = Constants.getWechatWorkAppKey();
			String appSecret = Constants.getWechatWorkSecret();
			String resultStr = McspUtil.post(url, params.toJSONString(), Constants.MCSP_AK_TYPE_JWT, appKey,appSecret);
			logger.info("[QyWeiXinService.doHttp]======<<<<url=" + url + ",result：" + resultStr);
			if (StringUtils.isNotBlank(resultStr)) {
				JSONObject jo = JSON.parseObject(resultStr);
				if (jo != null && StringUtils.equals(jo.getString("code"), SUCCESS_CODE)) {
					result.put("respData", jo);
				}
			}
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
		} catch (Exception e) {
			logger.error("[QyWeiXinService.getWechatTagList]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "查询失败");
		}
		return result;
	}


	public static synchronized JSONObject doHttp(String url, JSONObject infParam) {
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		try {
			JSONObject params = new JSONObject();
			params.put("restParams", infParam);
			/*HttpResp resp = sendPost(url,params);
			int code = resp.getCode();
			String resultStr = resp.getResult();*/
			String appKey = Constants.getWechatWorkAppKey();
			String appSecret = Constants.getWechatWorkSecret();
			String resultStr = McspUtil.post(url, params.toJSONString(), Constants.MCSP_AK_TYPE_JWT, appKey,appSecret);
			logger.info("[QyWeiXinService.doHttp]======<<<<url=" + url +  ",params="+params.toJSONString()+",resultStr：" + resultStr);
			if (StringUtils.isNotBlank(resultStr)) {
				JSONObject jo = JSON.parseObject(resultStr);
				if (jo != null && StringUtils.equals(jo.getString("code"), SUCCESS_CODE)) {
					result.put("respData", jo);
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "操作成功");
					return result;
				}
			}
			logger.info("[QyWeiXinService.doHttp]======<<<<url=" + url +  ",error：" + resultStr);
			result.put("respDesc", "操作失败");
		} catch (Exception e) {
			logger.error("[QyWeiXinService.doHttp]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respDesc", "操作失败：" + e.getMessage());
		}
		return result;
	}


	public static synchronized JSONObject addCrowdPack(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/serviceCrowdPack/addCrowdPack";
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		try {
			String crowdPackId = infParam.getString("crowdPackId");
			String crowdPackName = infParam.getString("crowdPackName");
			String customerCount = infParam.getString("customerCount");
			if (StringUtils.isBlank(crowdPackId) || StringUtils.isBlank(crowdPackName) || StringUtils.isBlank(customerCount)) {
				result.put("respDesc", "必填参数不能为空");
				return result;
			}
			infParam.put("sysId", REQ_SYS_ID);
			infParam.put("crowdPackType", 1);
			//infParam.put("enterpriseWechatId", "100");
			result = doHttp(url, infParam);
		} catch (Exception e) {
			logger.error("[QyWeiXinService.deleteCrowdPack]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respDesc", "操作失败：" + e.getMessage());
		}
		return result;
	}

	public static synchronized JSONObject deleteCrowdPack(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/serviceCrowdPack/deleteCrowdPack";
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		try {
			String crowdPackId = infParam.getString("crowdPackId");
			if (StringUtils.isBlank(crowdPackId)) {
				result.put("respDesc", "必填参数不能为空");
				return result;
			}
			result = doHttp(url, infParam);
		} catch (Exception e) {
			logger.error("[QyWeiXinService.deleteCrowdPack]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respDesc", "操作失败：" + e.getMessage());
		}
		return result;
	}

	public static synchronized JSONObject pushCrowdPack(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/serviceCrowdPack/pushCrowdPack";
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		try {
			String crowdPackId = infParam.getString("crowdPackId");
			String totalPage = infParam.getString("totalPage");
			String pageNo = infParam.getString("pageNo");
			String pageSize = infParam.getString("pageSize");
			JSONArray customerMobiles = infParam.getJSONArray("customerMobiles");
			if (StringUtils.isBlank(crowdPackId) || StringUtils.isBlank(totalPage)
					|| StringUtils.isBlank(pageNo) || StringUtils.isBlank(pageSize)
					|| customerMobiles.size() <= 0) {
				result.put("respDesc", "必填参数不能为空");
				return result;
			}
			result = doHttp(url, infParam);
		} catch (Exception e) {
			logger.error("[QyWeiXinService.pushCrowdPack]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respDesc", "操作失败：" + e.getMessage());
		}
		return result;
	}


	public static synchronized JSONObject queryCrowdPack(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/serviceCrowdPack/queryCrowdPack";
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		try {
			String crowdPackId = infParam.getString("crowdPackId");
			if (StringUtils.isBlank(crowdPackId)) {
				result.put("respDesc", "必填参数不能为空");
				return result;
			}
			result = doHttp(url, infParam);
		} catch (Exception e) {
			logger.error("[QyWeiXinService.queryCrowdPack]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respDesc", "操作失败：" + e.getMessage());
		}
		return result;
	}

	/**
	 * &
	 *
	 * @param infParam
	 * @return
	 */
	public static synchronized JSONObject uploadMaterial(JSONObject infParam) {
		JSONArray arr = infParam.getJSONArray("arr");
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/custGroupSend/uploadMaterial";
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		result.put("respDesc", "操作失败");
		try {
			JSONObject params = new JSONObject();
			params.put("restParams", arr);
			/*HttpResp resp = sendPost(url,params);
			int code = resp.getCode();
			String resultStr = resp.getResult();*/
			String appKey = Constants.getWechatWorkAppKey();
			String appSecret = Constants.getWechatWorkSecret();
			String resultStr = McspUtil.post(url, params.toJSONString(), Constants.MCSP_AK_TYPE_JWT, appKey,appSecret);

			if (StringUtils.isNotBlank(resultStr)) {
				JSONObject jo = JSON.parseObject(resultStr);
				if (jo != null && StringUtils.equals(jo.getString("code"), SUCCESS_CODE)) {
					logger.info("[QyWeiXinService.uploadMaterial]======<<<<url=" + url + ",result:" + jo);
					result.put("respData", jo);
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "查询成功");
					return result;
				}
			}

			logger.info("[QyWeiXinService.uploadMaterial]======<<<<url=" + url + ",error：" + resultStr);
		} catch (Exception e) {
			logger.error("[QyWeiXinService.uploadMaterial]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respDesc", "操作失败：" + e.getMessage());
		}
		return result;
	}

	public static synchronized JSONObject createService(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/custGroupSend/createService";
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		try {
			String content = infParam.getString("content");
			if (StringUtils.isBlank(content)) {
				result.put("respDesc", "content不能为空");
				return result;
			}
			infParam.put("sysId", REQ_SYS_ID);
			infParam.put("userType", 2);
			//infParam.put("sendAllCust", 1);
			infParam.put("groupsendType", 3);
			infParam.put("matchOne", 3);
			//infParam.put("enterpriseWechatId", "100");
			result = doHttp(url, infParam);
		} catch (Exception e) {
			logger.error("[QyWeiXinService.createService]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respDesc", "查询失败");
		}
		return result;
	}

	public static synchronized JSONObject queryUserDetail(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/custGroupSend/queryUserDetail";
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		try {
			String groupsendId = infParam.getString("groupsendId");
			if (StringUtils.isBlank(groupsendId)) {
				result.put("respDesc", "groupsendId不能为空");
				return result;
			}
			result = doHttp(url, infParam);
		} catch (Exception e) {
			logger.error("[QyWeiXinService.queryUserDetail]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respDesc", "查询失败");
		}
		return result;
	}

	public static synchronized JSONObject queryCustomerDetail(JSONObject infParam) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/custGroupSend/queryCustomerDetail";
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
		try {
			String groupsendId = infParam.getString("groupsendId");
			String userId = infParam.getString("userId");
			String msgId = infParam.getString("msgId");
			if (StringUtils.isBlank(groupsendId) || StringUtils.isBlank(userId) || StringUtils.isBlank(msgId)) {
				result.put("respDesc", "必填参数不能为空");
				return result;
			}
			result = doHttp(url, infParam);
		} catch (Exception e) {
			logger.error("[QyWeiXinService.queryCustomerDetail]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			result.put("respDesc", "查询失败");
		}
		return result;
	}


	/**
	 * 发送请求
	 *
	 * @param url
	 * @return
	 */
	@SuppressWarnings("unused")
	private HttpResp sendPost(String url) {
		return HttpUtil.post(url, "", "UTF-8");
	}

	/**
	 * 请求企业微信获取当前客户的好友关系
	 *
	 * @param param
	 * @return com.alibaba.fastjson.JSONObject
	 * <AUTHOR>
	 * @date 2022/4/11 14:13
	 */
	private static synchronized JSONObject queryWechatUserDetailInfo(JSONObject param) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/enterpriseWechatCustomer/getWechatCustomerRelation";
		String appKey = Constants.getWechatWorkAppKey();
		String appSecret = Constants.getWechatWorkSecret();
		String result = null;
		try {
			result = McspUtil.post(url, param.toJSONString(), Constants.MCSP_AK_TYPE_JWT, appKey,appSecret);
			if (StringUtils.isBlank(result)) {
				return null;
			}
			return JSONObject.parseObject(result);
		} catch (Exception e) {
			logger.error("[QyWeiXinService.queryCustomerDetail]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			return null;
		}

	}
	/**
	 * 生成一客一码短链(post)
	 *
	 * @param param
	 * @return com.alibaba.fastjson.JSONObject
	 * <AUTHOR>
	 * @date 2022/4/6 19:27
	 */
	public static synchronized JSONObject genExclusiveUrl(JSONObject param) {
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/ExclusiveCode/genExclusiveUrl";
		String appKey = Constants.getWechatWorkAppKey();
		String appSecret = Constants.getWechatWorkSecret();
		String result = null;
		try {
			result = McspUtil.post(url, param.toJSONString(), Constants.MCSP_AK_TYPE_JWT, appKey,appSecret);
			if (StringUtils.isBlank(result)) {
				return null;
			}
			return JSONObject.parseObject(result);
		} catch (Exception e) {
			logger.error("[QyWeiXinService.queryCustomerDetail]======<<<<url=" + url + ",msg：" + e.getMessage(), e);
			return null;
		}
	}

	private static JSONObject agentJSSDKInit(JSONObject json) {
		JSONObject agentWxConfig = WeiXinUtil.getAgentWxConfig(json.getString("url"));
		return agentWxConfig;
	}

	private static JSONObject getExtCustInfo(JSONObject json) {
		JSONObject extCustInfo = WeiXinUtil.getExtCustInfo(json.getString("userId"));
		return extCustInfo;
	}
	
	private static JSONObject syncWeComRecord(JSONArray reqData) {
		LocalTime currentTime = LocalTime.now();
		int hour = currentTime.getHour();
		CommonLogger.getCommLogger("wechatDetail_" + hour).info("企微会话同步收到入参：" + reqData.toString());
		JSONArray chatRecordArr = reqData;
		EasyResult result = new EasyResult();
		EasyQuery query = easyQuery;
		if (Constants.isOpenSensitiveWord()){
			//提交敏感词监控服务
			JSONObject json = new JSONObject();
			json.put("sender", "mixgw");
			json.put("password", "YQ_85521717");
			json.put("serialId", getDefaultNUMID());
			json.put("command","QyWeiXinGroupDetect");
			json.put("serviceId", "CONSUMER_SENSITIVE_WORD");
			json.put("timestamp", DateUtil.getCurrentDateStr());
			String sign = SecurityUtil.encryptMsgByMD5(json.getString("sender")+json.getString("password")+json.getString("timestamp")+json.getString("serialId"));
			json.put("signature", sign);
			json.put("data",reqData);
			try {
				long start = System.currentTimeMillis();
				String gatewayUrl = getRandomGatewayUrl(Constants.getUnifiedGatewayUrl());
				//默认5s超时机制
				HttpResp resp = HttpUtil.sendPost(gatewayUrl, json.toJSONString(), HttpUtil.TYPE_JSON, GWConstants.ENCODE_UTF8, 5);
				long end = System.currentTimeMillis();
				CommonLogger.getCommLogger("wechatDetail_" + hour).info("敏感词监控服务调用结果: " + resp.getResult());
				CommonLogger.getCommLogger("wechatDetail_" + hour).info("提交敏感词监控服务耗时:"+(end-start));
			} catch (Exception e) {
				CommonLogger.getCommLogger("wechatDetail_" + hour).error("敏感词监控SENSITIVE-WORD-IService请求失败,原因: "+e.getMessage());
			}
		}
        try {
			EasyRecord data = new EasyRecord("C_NO_WECOM_RECORD","ID");
			EasyRecord groupData = new EasyRecord("C_NO_WECOM_GROUP_RECORD","ID");
			for(int i=0;i<chatRecordArr.size();i++) {
				JSONObject obj = chatRecordArr.getJSONObject(i);
				//切换企业日志类型过滤
				String msgAction = obj.getString("action");//消息动作，目前有send(发送消息)/recall(撤回消息)/switch(切换企业日志)三种类型。String类型
				if("switch".equals(msgAction)) continue;
				//群聊与普通数据分开入库
				String roomId = obj.getString("roomid");
				if(StringUtils.isNotBlank(roomId)){ //同步群聊聊天数据
					addWeComRecord(query, groupData, obj, roomId);
				}
				else{ //同步普通聊天数据
					addWeComRecord(query, data, obj, roomId);
				}
			}
		} catch (Exception e) {
			detailLogger.info("企微聊天记录同步失败:" + e.getMessage(),e);
//			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
//			result.put("code", "999");
//			result.put("respDesc", "同步失败");
		} finally { //异常捕获 状态码返回优化
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("code", "000000");
			result.put("respDesc", "同步成功");
		}
		return result;
	}

	/**
	 * 	同步群聊记录
	 */
	private static JSONObject synGroupState(JSONObject params)  {
		JSONObject result = new JSONObject();
		String roomId = params.getString("roomId");
        try {
            easyQuery.execute("update ywdb.C_NO_GROUP_ORDERS set GROUP_STATE = '3' where CHAT_ID = ?", new Object[]{roomId});
			result.put("respCode",GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "群聊状态同步成功");
        } catch (Exception e) {
			logger.error("群聊状态同步失败:" + e.getMessage());
			result.put("respCode",GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "群聊状态同步出现异常");
        }
        return result;
	}

	/**
	 * 企微聊天记录入库
	 * roomId 不为空则为群聊记录
	 * @param query
	 * @param data
	 * @param obj
	 * @throws SQLException
	 */
	private static void addWeComRecord(EasyQuery query, EasyRecord data, JSONObject obj,String roomId) {
		try{
			String msgType = obj.getString("msgtype");//消息类型
			String msgTimestamp = obj.getString("msgtime");//消息发送时间戳
			String msgTime = DateUtil.getDateStrByTimeStamp(Long.valueOf(msgTimestamp));
			String sender = obj.getString("from");//发送方id
			JSONArray receiver = obj.getJSONArray("tolist");//接收方 可能有多个
			String content = getMsgContent(msgType, obj);//消息内容
			String msgAction = obj.getString("action"); //消息动作，目前有send(发送消息)/recall(撤回消息)/switch(切换企业日志)三种类型。String类型
			data.put("ID", RandomKit.uniqueStr());
			data.put("MSG_ID", obj.getString("msgid"));
			data.put("ACTION", msgAction);
			data.put("SENDER", sender);
			data.put("RECEIVER", receiver.getString(0));
			data.put("MSG_TIME", msgTime);
			data.put("MSG_TYPE", msgType);
			data.put("MSG_CONTENT", content);
			data.put("MSG_JSON", obj.getString(obj.getString("msgtype")));
			data.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			if(StringUtils.isNotBlank(roomId)){
				data.put("ROOM_ID",roomId);
			}
			query.save(data);
			if (receiver.size() > 0) {//如果有多个接收方，入库多条
				for (int j = 1; j < receiver.size(); j++) {
					data.put("ID", RandomKit.uniqueStr());
					data.put("RECEIVER", receiver.getString(j));
					query.save(data);
				}
			}
		}catch (Exception e){
			detailLogger.info("企微聊天记录入库异常:" + e.getMessage(),e);
			detailLogger.info("消息:" + getMsgContent(obj.getString("msgtype"), obj) + "入库异常!");
		}
	}

	/**
	 * 获取消息内容
	 * @param msgType 消息类型
	 * @param obj 消息体
	 * @return
	 */
	private static String getMsgContent(String msgType,JSONObject obj) {
		String content = "";
		if(StringUtils.isBlank(msgType)) {
			return "";
		}
		try {
			switch (msgType) {
			case "text":
				content = obj.getJSONObject("text").getString("content");//文本
				break;
			case "image":
				content = obj.getJSONObject("image").getString("fileUrl");//图片
				break;
			case "revoke":
				content = "【该消息已被撤回】";//撤回消息
				break;
			case "agree":
				content = "【对方同意存档会话内容，可以继续提供服务】";//同意会话聊天内容
				break;
			case "disagree":
				content = "【对方不同意存档会话内容，将无法继续提供服务】";//不同意会话聊天内容
				break;
			case "voice":
				content = obj.getJSONObject("voice").getString("fileUrl");//语音
				break;
			case "video":
				content = obj.getJSONObject("video").getString("fileUrl");//视频
				break;
			case "card":
				content = "【这是一条名片消息】";//名片消息
				break;
			case "location":
				content = "【这是一条位置消息】经度：" +obj.getJSONObject("location").getString("longitude")
				+"，纬度："+obj.getJSONObject("location").getString("latitude")+"，地址信息："+obj.getJSONObject("location").getString("address");//位置
				break;
			case "emotion":
				content = obj.getJSONObject("emotion").getString("fileUrl");//表情
				break;
			case "file":
				content = obj.getJSONObject("file").getString("fileUrl");//文件
				break;
			case "link":
				content = "【这是一条链接消息】标题："+obj.getJSONObject("link").getString("title")+"，描述："+obj.getJSONObject("link").getString("description")+"，链接地址："
				+obj.getJSONObject("link").getString("link_url");//链接
				break;
			case "weapp":
				content = "【这是一条小程序消息】标题："+obj.getJSONObject("weapp").getString("title")+"，描述："+obj.getJSONObject("weapp").getString("description")+"，小程序名称："
				+obj.getJSONObject("weapp").getString("displayname");//小程序消息
				break;
			case "chatrecord":
				content = "【这是一条会话记录消息】标题："+obj.getJSONObject("chatrecord").getString("title");//会话记录消息
				break;
			case "vote":
				content = "【这是一条投票消息】投票主题："+obj.getJSONObject("vote").getString("title");//投票消息
				break;
			case "redpacket":
				content = "【这是一条红包消息】红包祝福语："+obj.getJSONObject("redpacket").getString("wish")+"，红包总个数"+obj.getJSONObject("redpacket").getString("totalcnt")+"，红包总金额："
				+obj.getJSONObject("redpacket").getString("totalamount")+"";//红包消息
				break;
			case "docmsg":
				content = "【这是一条在线文档消息】文档名称："+obj.getJSONObject("docmsg").getString("title")+"，文档链接："+obj.getJSONObject("docmsg").getString("link_url");//在线文档消息
				break;
			case "qydiskfile":
				content = obj.getJSONObject("qydiskfile").getString("fileUrl");//微盘文件
				break;
			case "voiptext":
				content =  "【这是一条音视频通话消息】通话时长："+obj.getJSONObject("voiptext").getString("callduration");//音视频通话
				break;
			default:
				break;
			}
			return content;
		} catch (Exception e) {
			detailLogger.info("企微聊天内容转换失败:" + e.getMessage());
			return content;
		}
	}

	/**
	 * 获取企微聊天记录OSS附件的访问凭证
	 * @return
	 */
	private static JSONObject getWeComOSSFileCertification(JSONObject param) {
		JSONObject result = new JSONObject();
		String cacheStr = cache.get("WeComOSSFileCertification");
		if(StringUtils.isNotBlank(cacheStr)) {
			JSONObject json = JSONObject.parseObject(cacheStr);
			logger.info("缓存中获取到企微OSS访问凭证：" + json);
			return json;
		}
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/chat/getCertification";
		String resp = "";
		try {
			resp = McspUtil.get(url, 2, Constants.getWechatWorkAppKey(), Constants.getWechatWorkSecret());
		} catch (Exception e) {
			logger.error("error:" + e.getMessage(),e);
		}
		logger.info("请求企微OSS访问凭证，url：" + url +"，结果：" + resp);
		if(StringUtils.isNotBlank(resp)) {
			result = JSONObject.parseObject(resp);
			if("000000".equals(result.getString("code"))) {
				JSONObject data = result.getJSONObject("data");
				cache.put("WeComOSSFileCertification",data.toJSONString(),3600);
				return data;
			}
		}
		return result;
	}

	/**
	 * 通过群ID获取群主账号ID
	 * @param param
	 * @return
	 */
	private static JSONObject getGroupMasterAcc(JSONObject param){
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		result.put("respDesc", "获取群主ID成功");
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/external/customerGroup/getBaseInfo";
		String appKey = Constants.getWechatWorkAppKey();
		String appSecret = Constants.getWechatWorkSecret();
		try {
			JSONObject requestJson = new JSONObject();
			JSONObject restParams = new JSONObject();
			int enterpriseWechatId = Integer.parseInt(Constants.getWechatEnterprise());
			restParams.put("wechatGroupId",param.getString("roomId"));
			restParams.put("enterpriseWechatId",enterpriseWechatId);
			requestJson.put("restParams",restParams);
			logger.info("获取群主ID参数：" + requestJson);
			String response = McspUtil.postTimeOut(url, requestJson.toJSONString(), Constants.MCSP_AK_TYPE_JWT, appKey,appSecret,5000);
			logger.info("获取群主ID返回结果：" + response);
			if (StringUtils.isBlank(response)) {
				result.put("respCode",GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "获取群主ID失败!");
				return result;
			}
			JSONObject respJson = JSONObject.parseObject(response);
			if("000000".equals(respJson.getString("code"))){
				JSONObject data = respJson.getJSONObject("data");
				result.put("respData",data);
			}
			return result;
		} catch (Exception e) {
			logger.error("[QyWeiXinService.getGroupMasterAcc]======<<<<url=" + url + ",appKey="+ appKey+",appSecret="+appSecret+",msg=" + e.getMessage(), e);
			result.put("respCode",GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "获取群主ID出现异常,msg="+e.getMessage());
			return result;
		}
	}

	/**
	 * 企微发送按钮卡片
	 * @param param
	 * @return
	 */
	private static JSONObject sendWxButtonCardMessage(JSONObject param){
		JSONObject result = new JSONObject();
		result.put("respCode",GWConstants.RET_CODE_OTHER_EXCEPTION);
		result.put("respDesc", "发送按钮卡片失败!");
		String url = Constants.getWechatWorkBaseUrl() + "/wechat/external/agentMessage/send";
		String appKey = Constants.getWechatWorkAppKey();
		String appSecret = Constants.getWechatWorkSecret();
		try {
			JSONObject requestJson = new JSONObject();
			JSONObject restParams = new JSONObject();
			String userId = param.getString("userId");
			String sensitiveWord = param.getString("sensitiveWord");
			String groupName = param.getString("groupName");
			String triggerAccount = param.getString("triggerAccount");
			String triggerTime = param.getString("triggerTime");
			String roomId = param.getString("roomId");
			String jumpUrl = Constants.getWechatGroupJumpUrl() + "&chatGroupId=" + roomId;
			int enterpriseWechatId = Integer.parseInt(Constants.getWechatEnterprise());
			String taskId = RandomKit.randomStr();
			//组装消息格式
			String agentMessageTemplate = WeiXinParamesUtil.getAgentMessageTemplate(userId, sensitiveWord, groupName, triggerAccount, triggerTime, jumpUrl,taskId);
			JSONObject messageContent = JSON.parseObject(agentMessageTemplate);
			restParams.put("enterpriseWechatId",enterpriseWechatId);
			restParams.put("sysId","CSS_GROUP");
			restParams.put("messageContent",messageContent);
			requestJson.put("restParams",restParams);
			logger.info("发送按钮卡片参数：" + requestJson);
			String response = McspUtil.postTimeOut(url, requestJson.toJSONString(), Constants.MCSP_AK_TYPE_JWT, appKey,appSecret,5000);
			logger.info("发送按钮卡片返回结果：" + response);
			if (StringUtils.isNotBlank(response)) {
				JSONObject respJson = JSONObject.parseObject(response);
				if("000000".equals(respJson.getString("code"))){
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "发送按钮卡片成功");
				}
			}
			return result;
		} catch (Exception e) {
			logger.error("[QyWeiXinService.sendWxAgentMessage]======<<<<url=" + url + ",appKey="+ appKey+",appSecret="+appSecret+",msg=" + e.getMessage(), e);
			return result;
		}
	}

	
	private static HttpRequest sendPost(String url, JSONObject param) {
		String appKey = Constants.getWechatWorkAppKey();
		String secret = Constants.getWechatWorkSecret();
		long timeStamp = System.currentTimeMillis();
		String signString = appKey + timeStamp + secret;
		HttpRequest request = HttpRequest.post(url)
				.header("appKey", appKey)
				.header("timeStamp", String.valueOf(timeStamp))
				.header("sign", DigestUtils.md5DigestAsHex(signString.getBytes(StandardCharsets.UTF_8)))
				.trustAllCerts()
				.trustAllHosts()
				.contentType("application/json", "utf-8")
				.send(param.toJSONString());
		System.out.println(String.valueOf(timeStamp));
		System.out.println(DigestUtils.md5DigestAsHex(signString.getBytes(StandardCharsets.UTF_8)));
		return request;
	}
//	
	

	public static void main(String[] args) {
		
		String appKey = "VlsPQoosfFX7";
		String secret = "a402dcd52bde1f4b07c3be36435da57e85cef59d";
		long timeStamp = System.currentTimeMillis();
		String signString = appKey + timeStamp + secret;
		String sign = DigestUtils.md5DigestAsHex(signString.getBytes(StandardCharsets.UTF_8));
		System.out.println(timeStamp);
		System.out.println(sign);

		HttpResp sendGet = HttpUtil.sendGet("https://mcsp-api-sit.midea.com/api/rms_api/wec-backstage/wechat/chat/getCertification", null, "utf-8");
		System.out.println(sendGet.getResult());
	}
	
	/**
	 * 从分号分隔的URL列表中随机选择一个URL
	 * @param urlsStr 分号分隔的URL字符串
	 * @return 随机选择的单个URL
	 */
	private static String getRandomGatewayUrl(String urlsStr) {
		if(StringUtils.isBlank(urlsStr)) {
			return "";
		}
		
		// 如果不包含分号，则直接返回原URL
		if(!urlsStr.contains(";")) {
			return urlsStr;
		}
		
		// 分割URL字符串并随机选择一个
		String[] urls = urlsStr.split(";");
		if(urls.length == 0) {
			return urlsStr;
		}
		
		int randomIndex = (int)(Math.random() * urls.length);
		return urls[randomIndex];
	}
}
