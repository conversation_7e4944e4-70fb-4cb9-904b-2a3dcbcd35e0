package com.yunqu.cc.mixgw.utils;

import org.apache.log4j.Logger;

import com.yunqu.cc.mixgw.base.CommonQiYeWeiXinLogger;
import com.yunqu.cc.mixgw.model.Weixin;

/**
 * 微信参数
 * <AUTHOR>
 *
 */
public class WeiXinParamesUtil {
	
	
    private static Logger logger = CommonQiYeWeiXinLogger.logger;


	//1.微信参数
	//token
	public final static String token = "YQL";
	// encodingAESKey
	public final static String encodingAESKey = "RgLv9I3kw4g3VSD9CR9CQhqwZXiIePCC1eAAGOAwKQg";
	//企业ID
	public final static String corpId = "wwc56202ba0a29b2fb";

	//应用的凭证密钥
	public final static String agentSecret = "sY3WfCLO-XIc2MNHbHAs5SnkFt7r3HtJzA-rOJg-ox0";
	//通讯录秘钥
	public final static String contactsSecret = "1m_9XP62YrXjSiYtL5ThbexiLVWBThukiK5sH7wm1TM";
	//打卡的凭证密钥
	public final static String checkInSecret = "LLTMcHo5otbgXMF8a5HY0ThTrQLqfkVmU0F6wX_gRIc";
	//审批的凭证密钥
	public final static String approveSecret = "6X7Ft0hIZXY6Q2IfbWGLBFvLmNfJkzBZ6k3efWZE0-8";
	
	
	//企业应用的id，整型。可在应用的设置页面查看
//	public final static int agentId = 1000002;

	public  static Weixin getWeiXinInfo(String channelKey){
		String cacheId="cc-qiye-weixin-"+channelKey;	
		logger.info("cacheId:"+cacheId);

		Weixin weixin =new Weixin();
		weixin.setAgentId("");
		weixin.setChannelKey("");
		weixin.setToken(token);
		weixin.setAgentSecret(agentSecret);
		weixin.setEncodingAESKey(encodingAESKey);
		weixin.setCorpId(corpId);
		return weixin;
				
		
	}

	public static String getAgentMessageTemplate(
			String userId,
			String sensitiveWord,
			String groupName,
			String triggerAccount,
			String triggerTime,
			String jumpUrl,
			String taskId
	){
		// 使用String.format进行参数替换
		String horizontalContent = String.format(
				"\"[%s]中[%s]在[%s]发送【敏感词】\"",
				groupName, triggerAccount, triggerTime);

		// 构建horizontal_content_list部分
		String horizontalContentList = String.format(
				"[{\"keyName\": \"触发详情\", \"value\": %s}]",
				horizontalContent);

		// 构建完整的JSON结构
		return String.format(
						"{\n" +
						"    \"toUser\": \"%s\",\n" +
						"    \"msgType\": \"template_card\",\n" +
						"    \"templateCard\": {\n" +
						"        \"cardType\": \"text_notice\",\n" +
						"        \"taskId\": \"%s\",\n" +
						"        \"mainTitle\": {\n" +
						"            \"title\": \"敏感词触发通知\",\n" +
						"            \"desc\": \"\"\n" +
						"        },\n" +
						"        \"emphasisContent\":  {\n" +
						"            \"title\": \"%s\",\n" +
						"            \"desc\": \"\"\n" +
						"        },\n" +
						"        \"horizontalContentList\": %s,\n" +
						"        \"jumpList\": [\n" +
						"            {\n" +
						"                \"title\": \"点此跳转到相关群聊查看\",\n" +
						"                \"type\": 1,\n" +
						"                \"url\": \"%s\"\n" +
						"            }\n" +
						"        ],\n" +
						"        \"cardAction\": {\n" +
						"            \"type\": 1,\n" +
						"            \"url\": \"%s\"\n" +
						"        }\n" +
						"    }\n" +
						"}",
				userId,
				taskId,
				sensitiveWord,
				horizontalContentList,
				jumpUrl,
				jumpUrl);
	}
	
	
	

}
