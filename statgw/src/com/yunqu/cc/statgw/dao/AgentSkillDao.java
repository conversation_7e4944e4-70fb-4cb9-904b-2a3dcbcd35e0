package com.yunqu.cc.statgw.dao;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.yq.busi.common.util.CommonUtil;


public class AgentSkillDao extends BaseDao{	
	/**
	 * 接通量（语音）
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentSkillCallVoice(String beginTime, String endTime) {
		try {		
			EasySQL sql = new EasySQL("SELECT AGENT_ACC,SESSION_TYPE,CHANNEL_ID,SKILL_CODE,COUNT(1) TOTAL");
			sql.append("FROM C_PF_V_CALL_RECORD WHERE 1=1");
			sql.append(beginTime,"AND ANSWER_TIME >= ?");
			sql.append(endTime,"AND ANSWER_TIME < ?");
			sql.append("GROUP BY AGENT_ACC,SESSION_TYPE,CHANNEL_ID,SKILL_CODE");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "接通量（语音）:"+ sql.toString());
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "接通量（语音）失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 接通量、客户量（全媒体）
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentSkillCallMedia(String beginTime, String endTime) {
		try {		
			EasySQL sql = new EasySQL("SELECT AGENT_ACC,SESSION_TYPE,CHANNEL_ID,GROUP_ID,");
			sql.append("COUNT(1) TOTAL,COUNT(DISTINCT CUSTOMER_ACC) CUST_NUM");
			sql.append("FROM V_PF_SESSION_RECORD WHERE 1=1");
			sql.append(beginTime,"AND ANSWER_TIME >= ?");
			sql.append(endTime,"AND ANSWER_TIME < ?");
			sql.append("GROUP BY AGENT_ACC,SESSION_TYPE,CHANNEL_ID,GROUP_ID");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "接通量、客户量（全媒体）:"+ sql.toString());
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "接通量、客户量（全媒体）失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 人工考评量、人工考评分
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentSkillManualQc(String beginTime, String endTime) {
		try {		
			EasySQL sql = new EasySQL("SELECT T2.AGENT_ACC,T2.SESSION_TYPE,T2.CHANNEL_ID,T2.SKILL_CODE,");
			sql.append("COUNT(1) MANUAL_QC_NUM,");
			sql.append("SUM(CASE WHEN T1.QC_SCORE=0 THEN 1 ELSE 0 END) MANUAL_ZERO_NUM,");
			sql.append("SUM(T1.QC_SCORE) MANUAL_QC_SCORE");
			sql.append("FROM C_PF_QC_RECORD T1 LEFT JOIN C_PF_V_ALL_RECORED T2");
			sql.append("ON T1.SESSION_RECORD_ID=T2.ID WHERE 1=1");
			sql.append(beginTime,"AND T1.MANUAL_QC_TIME >= ?");
			sql.append(endTime,"AND T1.MANUAL_QC_TIME < ?");
			sql.append("GROUP BY T2.AGENT_ACC,T2.SESSION_TYPE,T2.CHANNEL_ID,T2.SKILL_CODE");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "人工考评量、人工考评分:"+ sql.toString());
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "人工考评量、人工考评分失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 自动考评量、自动考评分
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentSkillAutoQc(String beginTime, String endTime) {	
		try {
			EasySQL sql = new EasySQL("SELECT T2.AGENT_ACC,T2.SESSION_TYPE,T2.CHANNEL_ID,T2.SKILL_CODE,");
			sql.append("COUNT(1) AUTO_QC_NUM,SUM(T1.AUTO_QC_SCORE) AUTO_QC_SCORE,");
			sql.append("ROUND(AVG(T1.AUTO_QC_SCORE),2) AUTO_QC_AVG_SCORE FROM C_PF_QC_RECORD T1");
			sql.append("LEFT JOIN C_PF_V_ALL_RECORED T2 ON T1.SESSION_RECORD_ID = T2.ID WHERE 1=1");
			sql.append(beginTime,"AND T1.AUTO_QC_TIME>= ?");
			sql.append(endTime,"AND T1.AUTO_QC_TIME< ?");
			sql.append("GROUP BY T2.AGENT_ACC,T2.SESSION_TYPE,T2.CHANNEL_ID,T2.SKILL_CODE");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "自动考评量、自动考评分:" + sql.toString());
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "自动考评量、自动考评分失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 扩展属性（全媒体）
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentSkillExtend(String beginTime, String endTime) {	
		try {
			EasySQL sql = new EasySQL();
			sql.append("select t1.AGENT_ACC,t1.SESSION_TYPE,t1.CHANNEL_ID,t1.GROUP_ID,");
			sql.append("sum(case when t1.HANGUP_TYPE='04' then 1 else 0 end) TRANS_OUT_NUM,");
			sql.append("sum(case when t1.IN_TYPE='03' then 1 else 0 end) TRANS_IN_NUM,");
			sql.append("sum(t2.WAIT_SECONDS) WAIT_LEN,");
			sql.append("round(avg(t2.WAIT_SECONDS),2) AVG_WAIT_LEN,");
			sql.append("max(t2.WAIT_SECONDS) MAX_WAIT_LEN,");
			sql.append("sum(t1.LENS) SERV_LEN,");
			sql.append("round(avg(t1.LENS),2) AVG_SERV_LEN,");
			sql.append("max(t1.LENS) MAX_SERV_LEN,");
			sql.append("sum(t2.FIRST_REPLY) FIRST_REPLY_LEN,");
			sql.append("round(avg(t2.FIRST_REPLY),2) AVG_FIRST_REPLY_LEN,");
			sql.append("sum(t2.AVG_REPLY) REPLY_NUM,");
			sql.append("round(avg(t2.AVG_REPLY),2) AVG_REPLY_NUM,");
			sql.append("sum(t2.SH_ORDER_NUM) SH_ORDER_NUM,");
			sql.append("sum(t2.CONSULT_ORDER_NUM) ZX_ORDER_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='非常满意' then 1 else 0 end) VERY_SATIS_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='较满意' or t1.SATISF_NAME='满意' then 1 else 0 end) SATIS_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='一般' then 1 else 0 end) USUAL_NUM,");
			sql.append("sum(case when t1.SATISF_NAME is not null then 1 else 0 end) EVAL_NUM,");
			sql.append("sum(t2.CUST_MSG_NUM) CUST_MSG_NUM,");
			sql.append("sum(t2.AGENT_MSG_NUM) AGENT_MSG_NUM,");
			sql.append("sum(t2.THIRTY_SECONDS_REPLY_NUM) THIRTY_REPLY_NUM,");
			sql.append("sum(t2.REPLY_NUM) TOTAL_REPLY,");
			sql.append("sum(case when SLOW_REPLY_NUM>=3 then 1 else 0 end) SLOW_REPLY_NUM ");
			sql.append("from V_PF_SESSION_RECORD t1 left join C_PF_QC_SESSION t2 ");
			sql.append("on t1.ID=t2.SESSION_RECORD_ID ");
			sql.append(beginTime,"where t1.ANSWER_TIME>= ?");
			sql.append(endTime,"and t1.ANSWER_TIME< ?");
			sql.append("group by t1.AGENT_ACC,t1.SESSION_TYPE,t1.CHANNEL_ID,t1.GROUP_ID");
			
			 logger.info(CommonUtil.getClassNameAndMethod(this) + "扩展属性（全媒体）:" + sql.toString());
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "扩展属性（全媒体）失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 处理留言数（全媒体）
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public  List<EasyRow> stAgentSkillMess(String beginTime, String endTime) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("select USER_ID,'02' SESSION_TYPE,CHANNEL_KEY,GROUP_ID,count(1) HAND_LMSG_NUM ");
			sql.append("from YCBUSI.CC_MDEIA_WORD ");
			sql.append(1,"where STATE = ?");
			sql.append(beginTime,"and HANDLE_TIME>= ?");
			sql.append(endTime,"and HANDLE_TIME< ?");
			sql.append("group by USER_ID,CHANNEL_KEY,GROUP_ID");
			
			// logger.debug(CommonUtil.getClassNameAndMethod(this) + "处理留言数（全媒体）:" + sql.toString());
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "处理留言数（全媒体）失败.", e);
		}
		return null;
	} 
	
	/**
	 * 超三分钟未回复量（全媒体）
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public  List<EasyRow> stAgentSkillOver(String beginTime, String endTime) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("select t2.AGENT_ACC,t2.SESSION_TYPE,t2.CHANNEL_ID,t2.GROUP_ID,");
			sql.append("count(1) OVER_MINUTES_NUM ");
			sql.append("from V_PF_SESSION_DETAIL t1 ");
			sql.append("left join V_PF_SESSION_RECORD t2 on t1.SESSION_RECORD_ID=t2.ID ");
			sql.append(180,"where t1.REPLY_SECONDS> ?");
			sql.append(beginTime,"and t2.ANSWER_TIME>= ?");
			sql.append(endTime,"and t2.ANSWER_TIME< ?");
			sql.append("group by t2.AGENT_ACC,t2.SESSION_TYPE,t2.CHANNEL_ID,t2.GROUP_ID");
			
			// logger.debug(CommonUtil.getClassNameAndMethod(this) + "超三分钟未回复量（全媒体）:" + sql.toString());
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "超三分钟未回复量（全媒体）失败.", e);
		}
		return null;
	}
	
	/**
	 * 视频客服服务时长（全媒体）
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentSkillVideo(String beginTime,String endTime){		
		try {
			EasySQL sql = new EasySQL("");
			sql.append("select t1.AGENT_ACC,t1.SESSION_TYPE,t1.CHANNEL_ID,t1.GROUP_ID,");
			sql.append("sum(t2.TOTAL_TIME) VIDEO_SERV_LEN,");
			sql.append("round(sum(t2.TOTAL_TIME)/count(1),0) AVG_VIDEO_SERV_LEN,");
			sql.append("max(t2.TOTAL_TIME) MAX_VIDEO_SERV_LEN");  
			sql.append("from V_PF_SESSION_RECORD t1 inner join (");
			sql.append("select M1.ID,sum(M2.TOTAL_TIME) TOTAL_TIME from V_PF_SESSION_RECORD M1");
			sql.append("left join ycbusi.CC_MEDIA_VIDEO_RECORD M2");
			sql.append("on M1.ID = M2.CHAT_SESSION_ID");
			sql.append(beginTime,"where M1.ANSWER_TIME >= ?");
			sql.append(endTime,"and M1.ANSWER_TIME <= ?");
			sql.append("group by M1.ID ) t2 on t1.ID = t2.ID");

			sql.append(beginTime,"where t1.ANSWER_TIME >= ?");
			sql.append(endTime,"and t1.ANSWER_TIME <= ?");
			sql.append("group by t1.AGENT_ACC,t1.SESSION_TYPE,t1.CHANNEL_ID,t1.GROUP_ID");
			
			return queryHelper.queryForList(sql.getSQL(),sql.getParams());
			
		} catch (SQLException ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "视频客服服务时长（全媒体）失败.", ex);
		}
		return null;
	}		
	/**
	 * 视频客服满意度
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentSkillVideoSati(String beginTime,String endTime){		
		try {
			EasySQL sql = new EasySQL("select t1.AGENT_ACC,t1.SESSION_TYPE,t1.CHANNEL_ID,t1.GROUP_ID,");
			sql.append("count(1) as SESSION_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='非常满意' then 1 else 0 end) VERY_SATIS_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='满意' then 1 else 0 end) SATIS_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='一般' then 1 else 0 end) USUAL_NUM,");
			sql.append("sum(case when t1.SATISF_NAME is not null then 1 else 0 end) EVAL_NUM");
			sql.append(" from V_PF_SESSION_RECORD t1 ");
			sql.append("where exists(select 1 from ycbusi.CC_MEDIA_VIDEO_RECORD t2");
			sql.append("where t1.ID = t2.CHAT_SESSION_ID)");
			sql.append(beginTime,"and t1.ANSWER_TIME >= ?");
			sql.append(endTime,"and t1.ANSWER_TIME <= ?");
			sql.append("and t1.SESSION_TYPE = '02'");
			sql.append("group by t1.AGENT_ACC,t1.SESSION_TYPE,t1.CHANNEL_ID,t1.GROUP_ID");
			return queryHelper.queryForList(sql.getSQL(),sql.getParams());
		} catch (SQLException ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "视频客服满意度失败.", ex);
		}
		return null;
	}
	
	/**
	 * 满意度细项评价汇总
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentSatisDetail(String beginTime,String endTime){
		try {
			EasySQL sql = new EasySQL("select t1.AGENT_ACC,t1.SESSION_TYPE,t1.CHANNEL_ID,t1.GROUP_ID,t2.SATISF_NAME,");
			sql.append("sum(case when SATISF_VALUE in('1','2','3','4','5') then 1 else 0 end) TOTAL,");
			sql.append("sum(case when SATISF_VALUE = '1' then 1 else 0 end) SATIS_STAR_01_NUM,");
			sql.append("sum(case when SATISF_VALUE = '2' then 1 else 0 end) SATIS_STAR_02_NUM,");
			sql.append("sum(case when SATISF_VALUE = '3' then 1 else 0 end) SATIS_STAR_03_NUM,");
			sql.append("sum(case when SATISF_VALUE = '4' then 1 else 0 end) SATIS_STAR_04_NUM,");
			sql.append("sum(case when SATISF_VALUE = '5' then 1 else 0 end) SATIS_STAR_05_NUM");
			sql.append(" from V_PF_SESSION_RECORD t1");
			sql.append("left join ycbusi.C_STF_VIDEO_SATISF_COLLECTION t2 on t1.ID=t2.RECORD_ID where 1=1 ");
			sql.append(beginTime,"and t2.CREATE_TIME >=?");
			sql.append(endTime,"and t2.CREATE_TIME <=?");
			sql.append("group by t1.AGENT_ACC,t1.SESSION_TYPE,t1.CHANNEL_ID,t1.GROUP_ID,t2.SATISF_NAME");			
			return queryHelper.queryForList(sql.getSQL(),sql.getParams());
		} catch (SQLException ex) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "满意度细项评价汇总失败.", ex);
		}
		return null;
     }	
}
