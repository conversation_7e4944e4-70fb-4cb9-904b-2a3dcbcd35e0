package com.yunqu.cc.statgw.dao;

import java.util.List;

import org.easitline.common.db.EasyRow;

import com.yq.busi.common.util.CommonUtil;


public class AgentQdDao extends BaseDao{
	
	/**
	 * 	通话总量、满意量、服务时长记录(语音)
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentVoiceSatis(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select AGENT_ACC,SESSION_TYPE,CHANNEL_ID,DIRECTION,count(1) TOTAL,");
			sql.append("sum(case when SATISFACTION_CODE='1' then 1 else 0 end) VERY_SATIS_NUM,");
			sql.append("sum(case when SATISFACTION_CODE='2' then 1 else 0 end) SATIS_NUM,");
			sql.append("sum(case when SATISFACTION_CODE='3' then 1 else 0 end) USUAL_NUM,");
			sql.append("sum(case when SATISFACTION_CODE='4' then 1 else 0 end) UNSATIS_SERVICE_NUM,");
			sql.append("sum(case when SATISFACTION_CODE='5' then 1 else 0 end) UNSATIS_RESULT_NUM,");
			sql.append("sum(LENS) SERVICE_SECONDS,round(avg(LENS),2) AVG_SERVICE_SECONDS ");
			sql.append("from C_PF_V_CALL_RECORD ");
			sql.append("where t1.ANSWER_TIME >='").append(stDate).append(" 00:00:00").append("' ");
			sql.append("and t1.ANSWER_TIME <='").append(stDate).append(" 23:59:59").append("' ");
			sql.append("group by AGENT_ACC,SESSION_TYPE,CHANNEL_ID,DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "通话总量、满意量、服务时长记录(语音)：" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "通话总量、满意量、服务时长记录(语音)失败.", e);
		}
		return null;
	}
	
	
	/**
	 * 	通话总量、满意量、响应时长、首次响应时长、服务时长记录(全媒体)
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentMediaSatis(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t1.AGENT_ACC,t1.SESSION_TYPE,t1.CHANNEL_ID,t1.DIRECTION,count(1) TOTAL,");
			sql.append("sum(case when t1.SATISF_NAME='非常满意' then 1 else 0 end) VERY_SATIS_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='满意' then 1 else 0 end) SATIS_NUM,");
			sql.append("sum(case when t1.SATISF_NAME='一般' then 1 else 0 end) USUAL_NUM,");
			sql.append("sum(case when t1.SATISF_NAME is not null then 1 else 0 end) SATIS_TOTAL,");
			sql.append("sum(t2.AVG_REPLY) REPLY_SECONDS,");
			sql.append("round(avg(t2.AVG_REPLY),2) AVG_REPLY_SECONDS,");
			sql.append("sum(t2.FIRST_REPLY) FIRST_REPLY_SECONDS,");
			sql.append("round(avg(t2.FIRST_REPLY),2) AVG_FIRST_REPLY_SECONDS,");
			sql.append("sum(t1.LENS) SERVICE_SECONDS,");
			sql.append("round(avg(t1.LENS),2) AVG_SERVICE_SECONDS ");
			sql.append("from V_PF_SESSION_RECORD t1 ");
			sql.append("left join C_PF_QC_SESSION t2 on t2.SESSION_RECORD_ID=t1.ID ");
			sql.append("where t1.ANSWER_TIME >='").append(stDate).append(" 00:00:00").append("' ");
			sql.append("and t1.ANSWER_TIME <='").append(stDate).append(" 23:59:59").append("' ");
			sql.append("group by t1.AGENT_ACC,t1.SESSION_TYPE,t1.CHANNEL_ID,t1.DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "通话总量、满意量、响应时长、首次响应时长、服务时长记录(全媒体)：" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "通话总量、满意量、响应时长、首次响应时长、服务时长记录(全媒体)失败.", e);
		}
		return null;
	}
	
	
	/**
	 * 人工质检记录
	 * @param stDate
	 */
	public List<EasyRow> stAgentQdManualQc(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t2.AGENT_ACC,t2.SESSION_TYPE,t2.CHANNEL_ID,t2.DIRECTION,");
			sql.append("count(1) MANUAL_QC_NUM,");
			sql.append("sum(case when t1.QC_SCORE=0 then 1 else 0 end) MANUAL_ZERO_NUM,");
			sql.append("sum(case when t1.QC_SCORE>=80 then 1 else 0 end) MANUAL_QC_QUAL_NUM,");
			sql.append("sum(t1.QC_SCORE) MANUAL_QC_TOTAL_SCORE,");
			sql.append("sum(case when t3.KEY_TYPE!='00' then 1 else 0 end) KEY_ERROR_NUM,");
			sql.append("sum(case when t3.KEY_TYPE='01' then 1 else 0 end) SERVICE_ERROR_NUM ");
			sql.append("from C_PF_QC_RECORD t1 ");
			sql.append("left join C_PF_V_ALL_RECORED t2 on t1.SESSION_RECORD_ID=t2.ID ");
			sql.append("left join C_PF_QC_SCORE t3 on t1.QC_SCORE_ID=t3.ID ");
			sql.append("where t1.MANUAL_QC_TIME >='").append(stDate).append(" 00:00:00").append("' ");
			sql.append("and t1.MANUAL_QC_TIME <='").append(stDate).append(" 23:59:59").append("' ");
			sql.append("group by t2.AGENT_ACC,t2.SESSION_TYPE,t2.CHANNEL_ID,t2.DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "人工质检记录："+ sql.toString());		
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "人工质检记录失败.", e);
		}
		return null;
	}

	
	/**
	 * 自动质检记录
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentQdAutoQc(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t2.AGENT_ACC,t2.SESSION_TYPE,t2.CHANNEL_ID,t2.DIRECTION,count(1) AUTO_QC_NUM,");
			sql.append("sum(t1.AUTO_QC_SCORE) AUTO_QC_TOTAL_SCORE,");
			sql.append("round(avg(t1.AUTO_QC_SCORE),2) AUTO_QC_AVG_SCORE ");
			sql.append("from C_PF_QC_RECORD t1 left join C_PF_V_ALL_RECORED t2 ");
			sql.append("on t1.SESSION_RECORD_ID=t2.ID ");
			sql.append("where t1.AUTO_QC_TIME >='").append(stDate).append(" 00:00:00").append("' ");
			sql.append("and t1.AUTO_QC_TIME <='").append(stDate).append(" 23:59:59").append("' ");
			sql.append("group by t2.AGENT_ACC,t2.SESSION_TYPE,t2.CHANNEL_ID,t2.DIRECTION");		
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "自动质检记录：" + sql.toString());		
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "自动质检记录失败.", e);
		}
		return null;
	}

	
	/**
	 * VOC提交量、VOC通过量
	 * @param stDate
	 */
	public List<EasyRow> stAgentQdVoc(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t2.AGENT_ACC,t2.SESSION_TYPE,t2.CHANNEL_ID,t2.DIRECTION,");
			sql.append("count(1) VOC_APPLY_NUM,");
			sql.append("sum(case when t1.AUDIT_RESULT = '02' then 1 else 0 end) VOC_AUDIT_PASS_NUM ");
			sql.append("from C_PF_VOC_APPLY t1 left join C_PF_V_ALL_RECORED t2 ");
			sql.append("on t1.SESSION_RECORD_ID=t2.ID ");
			sql.append("where t1.CREATE_TIME >='").append(stDate).append(" 00:00:00").append("' ");
			sql.append("and t1.CREATE_TIME <='").append(stDate).append(" 23:59:59").append("' ");
			sql.append("group by t2.AGENT_ACC,t2.SESSION_TYPE,t2.CHANNEL_ID,t2.DIRECTION");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "VOC提交量、VOC通过量："+ sql.toString());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "VOC提交量、VOC通过量失败.", e);
		}
		return null;
	}
	
	
	/**
	 *  处理留言数（语音）
	 * @param stDate
	 */
	public List<EasyRow> stAgentQdVoiceMess(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select HAND_USER_ACC,'01' SESSION_TYPE,'99' CHANNEL_ID,'01' DIRECTION,");
			sql.append("count(1) LEAVE_MSG_NUM ");
			sql.append("from C_LM_LEAVE_MSG ");
			sql.append("where STATUS='02' and HAND_TIME >='").append(stDate).append(" 00:00:00").append("' ");
			sql.append("and HAND_TIME <='").append(stDate).append(" 23:59:59").append("' ");
			sql.append("group by HAND_USER_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "处理留言数（语音）:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "处理留言数（语音）失败.", e);
		}
		return null;	
	}
	
	
	/**
	 *  处理留言数（全媒体）
	 * @param stDate
	 */
	public List<EasyRow> stAgentQdMediaMess(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select USER_ID,'02' SESSION_TYPE,CHANNEL_KEY,'01' DIRECTION,");
			sql.append("count(1) LEAVE_MSG_NUM ");
			sql.append("from YCBUSI.CC_MDEIA_WORD ");
			sql.append("where STATE=1 and HANDLE_TIME >='").append(stDate).append(" 00:00:00").append("' ");
			sql.append("and HANDLE_TIME <='").append(stDate).append(" 23:59:59").append("' ");
			sql.append("group by USER_ID,CHANNEL_KEY");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "处理留言数（全媒体）:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "处理留言数（全媒体）失败.", e);
		}
		return null;
	}
	
}
