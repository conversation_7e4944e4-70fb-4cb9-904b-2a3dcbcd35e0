package com.yunqu.cc.statgw.dao;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.camel.util.StreamUtils;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.statgw.base.Constants;


public class AgentDao extends BaseDao {
	EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	/**
	 * 查找所有的坐席，包括全媒体坐席和语音坐席
	 * @return
	 */
	public List<EasyRow> findAllAgent(String stDate) {
		try {
			List<EasyRow> easyRows = new ArrayList<>();
			StringBuilder sql = new StringBuilder();
			sql.append("select t1.USER_ACC,t1.WORK_NO,t1.USER_NAME,t1.DEPT_CODE,t1.DEPT_NAME,");
			sql.append("t1.EP_CODE,t1.AREACODE,t1.AREA_NAME,nvl(t1.ENTRY_MONTHS,1) ENTRY_MONTHS,");
			sql.append("t2.SCHEDULING_ID,t2.SCHEDULING_NAME,t2.TIMELONG,t2.STARTTIME,t2.ENDTIME ");
			sql.append(",t1.ENTRY_TIME ");//入职时间
			sql.append("from C_YG_EMPLOYEE t1 left join C_PB_SCHEDULING_PERSON t2 ");
			sql.append("on t1.USER_ACC=t2.USER_ACC and t2.PLAN_DATE='").append(stDate).append("' ");
			sql.append("where t1.LEAVE_TIME is null ");
			int count = query.queryForInt("select count(1) from (" + sql + ")",null);
			int pageSize = 3000;
			int pageNum = (int)Math.ceil(count/(double)pageSize);
			for (int i = 1; i <= pageNum ; i++) {
				List<EasyRow> tempRows = query.queryForList(sql.toString(), null,i,pageSize);
				easyRows.addAll(tempRows);
			}
			logger.info("找出所有的坐席,size = " + easyRows.size());
			return easyRows;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "找出所有的坐席失败.", e);
		}
		return null;
	}
		
	
	/**
	 * 删除坐席的汇总记录
	 * @param tableName
	 * @param stDate
	 * @param stType
	 */
	public void delAgentRecord(String tableName, String stDate, String stType) {
		delAgentRecord(queryHelper,tableName, stDate, stType);
	}

	/**
	 * 删除坐席的汇总记录
	 * @param tableName
	 * @param stDate
	 * @param stType
	 */
	public void delAgentRecord(EasyQuery query,String tableName, String stDate, String stType) {
		try {
			String sql = "delete from " + tableName +
					" where EXISTS( SELECT 1 FROM C_ST_AGENT WHERE "+tableName+".ST_AGENT_ID = C_ST_AGENT.ID AND C_ST_AGENT.ST_DATE='" + stDate + "' and C_ST_AGENT.ST_TYPE='" + stType+ "')";
			logger.info("【"+Thread.currentThread().getName()+"】" + "删除坐席的汇总记录:" + sql);
			int n = query.executeUpdate(sql, new Object[]{});
//			if(ServerContext.isDebug()){
			logger.info("【"+Thread.currentThread().getName()+"】" + "删除坐席的汇总记录数:"+n);
//			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "删除坐席的汇总记录失败.", e);
		}
	}
	/**
	 * 获取坐席统汇总记录
	 * @param userId
	 * @param stDate
	 * @param stType
	 * @return
	 */
	public String getAgentRecord(String userId, String stDate, String stType) {
		String id = "";	
		try {

			EasySQL sql = new EasySQL("select ID from C_ST_AGENT where 1=1");
			sql.append(stType,"AND ST_TYPE= ?");
			sql.append(stDate,"AND ST_DATE= ?");
			sql.append(userId,"AND USER_ACC= ?");
			id = queryHelper.queryForString(sql.getSQL(), sql.getParams());					
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "获取坐席统汇总记录失败", e);
		}
		return id;
	}
	
	
	/**
	 * 根据会话类型统计坐席的通话量
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> countCallByAgent(String qcBeginDate, String qcEndDate) {		
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select AGENT_ACC,");
			sql.append("sum(case when SESSION_TYPE='01' and DIRECTION='01' then 1 else 0 end) VOICE_CALL_IN_NUM,");
			sql.append("sum(case when SESSION_TYPE='01' and DIRECTION='02' then 1 else 0 end) VOICE_CALL_OUT_NUM,");
			sql.append("sum(case when SESSION_TYPE='01' then 1 else 0 end) VOICE_CALL_NUM,");
			sql.append("sum(case when SESSION_TYPE='02' and DIRECTION='01' then 1 else 0 end) MEDIA_CALL_IN_NUM,");
			sql.append("sum(case when SESSION_TYPE='02' and DIRECTION='02' then 1 else 0 end) MEDIA_CALL_OUT_NUM,");
			sql.append("sum(case when SESSION_TYPE='02' then 1 else 0 end) MEDIA_CALL_NUM ");
			sql.append("from C_PF_V_ALL_RECORED ");
			sql.append("where substr(ANSWER_TIME,1,10)>='").append(qcBeginDate).append("' ");
			sql.append("and substr(ANSWER_TIME,1,10)<'").append(qcEndDate).append("' ");
			sql.append("group by AGENT_ACC");
			//logger.debug(CommonUtil.getClassNameAndMethod(this)+"根据会话类型统计坐席的通话量:" + sql);
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+"根据会话类型统计坐席的通话量失败.", e);
		}
		return null;
	}

	
	/**
	 * 根据会话类型统计坐席的质检量
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> countQcByAgent(String qcBeginDate, String qcEndDate) {		
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t2.AGENT_ACC,");
			sql.append("sum(case when t2.SESSION_TYPE='01' and t2.DIRECTION='01' then 1 else 0 end) VOICE_QC_IN_NUM,");
			sql.append("sum(case when t2.SESSION_TYPE='01' and t2.DIRECTION='02' then 1 else 0 end) VOICE_QC_OUT_NUM,");
			sql.append("sum(case when t2.SESSION_TYPE='01' then 1 else 0 end) VOICE_QC_NUM,");
			sql.append("sum(case when t2.SESSION_TYPE='02' and t2.DIRECTION='01' then 1 else 0 end) MEDIA_QC_IN_NUM,");
			sql.append("sum(case when t2.SESSION_TYPE='02' and t2.DIRECTION='02' then 1 else 0 end) MEDIA_QC_OUT_NUM,");
			sql.append("sum(case when t2.SESSION_TYPE='02' then 1 else 0 end) MEDIA_QC_NUM ");
			sql.append("from C_PF_QC_RECORD t1 left join C_PF_V_ALL_RECORED t2 ");
		    sql.append("on t1.SESSION_RECORD_ID=t2.ID ");
			sql.append("where substr(t1.MANUAL_QC_TIME,1,10)>='").append(qcBeginDate).append("' ");
			sql.append("and substr(t1.MANUAL_QC_TIME,1,10)<'").append(qcEndDate).append("' ");
			sql.append("group by t2.AGENT_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "根据会话类型统计坐席的质检量:" + sql);
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据会话类型统计坐席的质检量失败.", e);
		}
		return null;
	}
	
	
	/**
	 * 根据会话类型、渠道、呼叫方向获取坐席的话务量明细
	 * @param stBeginTime
	 * @param stEndTime
	 */
	public List<EasyRow> stAgentTimeByCall(String stBeginTime, String stEndTime) {
		try{
			StringBuffer sql = new StringBuffer();
			sql.append("select AGENT_ACC,SESSION_TYPE,CHANNEL_ID,DIRECTION,");
		    sql.append("sum(case when LENS<60 then 1 else 0 end) LESS_ONE_MINUTE ,");
		    sql.append("sum(case when LENS>=60 and LENS<120 then 1 else 0 end) ONE_MINUTE ,");
		    sql.append("sum(case when LENS>=120 and LENS<180 then 1 else 0 end) TWO_MINUTE ,");
		    sql.append("sum(case when LENS>=180 and LENS<240 then 1 else 0 end) THREE_MINUTE ,");
		    sql.append("sum(case when LENS>=240 then 1 else 0 end) FOUR_MINUTE ");
		    sql.append("from C_PF_V_ALL_RECORED ");
		    sql.append("where ANSWER_TIME>='").append(stBeginTime).append("' ");
		    sql.append("and ANSWER_TIME<='").append(stEndTime).append("' "); 
		    sql.append("group by AGENT_ACC,SESSION_TYPE,CHANNEL_ID,DIRECTION");
			
		    //logger.debug(CommonUtil.getClassNameAndMethod(this) + "根据会话类型、渠道、呼叫方向获取坐席的话务量明细:" + sql.toString());
		    return queryHelper.queryForList(sql.toString(), null);

		}catch(Exception e){
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据会话类型、渠道、呼叫方向获取坐席的话务量明细失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 根据会话类型、渠道、呼叫方向获取坐席的被监听明细
	 * @param stBeginTime
	 * @param stEndTime
	 */
	public List<EasyRow> stAgentTimeByQc(String stBeginTime, String stEndTime) {
		try{
			StringBuffer sql=new StringBuffer();
			sql.append("select t2.AGENT_ACC,t2.SESSION_TYPE,t2.CHANNEL_ID,t2.DIRECTION,");
		    sql.append("sum(case when t2.LENS<60 then 1 else 0 end) LESS_ONE_MINUTE ,");
		    sql.append("sum(case when t2.LENS>=60 and t2.LENS<120 then 1 else 0 end) ONE_MINUTE ,");
		    sql.append("sum(case when t2.LENS>=120 and t2.LENS<180 then 1 else 0 end) TWO_MINUTE ,");
		    sql.append("sum(case when t2.LENS>=180 and t2.LENS<240 then 1 else 0 end) THREE_MINUTE ,");
		    sql.append("sum(case when t2.LENS>=240 then 1 else 0 end) FOUR_MINUTE ");
		    sql.append("from C_PF_QC_RECORD t1 left join C_PF_V_ALL_RECORED t2 ");
		    sql.append("on t1.SESSION_RECORD_ID=t2.ID ");
		    sql.append("where t1.MANUAL_QC_TIME>='").append(stBeginTime).append("' ");
		    sql.append("and t1.MANUAL_QC_TIME<='").append(stEndTime).append("' "); 
		    sql.append("group by t2.AGENT_ACC,t2.SESSION_TYPE,t2.CHANNEL_ID,t2.DIRECTION");
			
		    //logger.debug(CommonUtil.getClassNameAndMethod(this) + "根据会话类型、渠道、呼叫方向获取坐席的被监听明细:" + sql.toString());
		    return queryHelper.queryForList(sql.toString(), null);

		}catch(Exception e){
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据会话类型、渠道、呼叫方向获取坐席的被监听明细失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席人工质检量
	 * @param stDate
	 */
	public List<EasyRow> stAgentQc(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t2.AGENT_ACC,t2.SESSION_TYPE,count(1) QC_NUM ");
			sql.append("from C_PF_QC_RECORD t1 left join C_PF_V_ALL_RECORED t2 ");
			sql.append("on t1.SESSION_RECORD_ID = t2.ID ");
			sql.append("where substr(t1.MANUAL_QC_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by t2.AGENT_ACC,t2.SESSION_TYPE");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席人工质检量:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席人工质检量失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席申诉量
	 * @param stDate
	 */
	public List<EasyRow> stAgentComplaint(String stDate) {			 		
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t3.AGENT_ACC,t3.SESSION_TYPE,count(1) COMPLAINT_NUM,");
			sql.append("sum(case when t1.COMPLAINT_TIMES=1 then 1 else 0 end) FIRST_COMPLAINT_NUM,");
			sql.append("sum(case when t1.COMPLAINT_TIMES=2 then 1 else 0 end) SECOND_COMPLAINT_NUM,");
			sql.append("sum(case when t1.COMPLAINT_TIMES=1 and t1.AUDIT_RESULT='01' then 1 else 0 end) FIRST_WAIT_NUM,");
			sql.append("sum(case when t1.COMPLAINT_TIMES=2 and t1.AUDIT_RESULT='01' then 1 else 0 end) SECOND_WAIT_NUM ");
			sql.append("from C_PF_COMPLAINT t1 left join C_PF_QC_RECORD t2 on t1.QC_RECORD_ID=t2.ID ");
			sql.append("left join C_PF_V_ALL_RECORED t3 on t2.SESSION_RECORD_ID=t3.ID ");
			sql.append("where substr(t1.COMPLAINT_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by t3.AGENT_ACC,t3.SESSION_TYPE");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席申诉量记录:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席申诉量记录异常：", e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席申诉结果
	 * @param stDate
	 */
	public List<EasyRow> stAgentComResult(String stDate) {			 		
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t3.AGENT_ACC,t3.SESSION_TYPE,");
			sql.append("sum(case when t1.COMPLAINT_TIMES=1 and t1.AUDIT_RESULT='02' then 1 else 0 end) FIRST_APPROVED_NUM,");
			sql.append("sum(case when t1.COMPLAINT_TIMES=1 and t1.AUDIT_RESULT='03' then 1 else 0 end) FIRST_REJECTED_NUM,");
			sql.append("sum(case when t1.COMPLAINT_TIMES=2 and t1.AUDIT_RESULT='02' then 1 else 0 end) SECOND_APPROVED_NUM,");
			sql.append("sum(case when t1.COMPLAINT_TIMES=2 and t1.AUDIT_RESULT='03' then 1 else 0 end) SECOND_REJECTED_NUM ");
			sql.append("from C_PF_COMPLAINT t1 left join C_PF_QC_RECORD t2 on t1.QC_RECORD_ID=t2.ID ");
			sql.append("left join C_PF_V_ALL_RECORED t3 on t2.SESSION_RECORD_ID=t3.ID ");
			sql.append("where substr(t1.AUDIT_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by t3.AGENT_ACC,t3.SESSION_TYPE");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席申诉结果记录:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席申诉结果记录异常：", e);
		}
		return null;
	}
	
	
	/**
	 * 统计员工专项汇总记录
	 * @param stDate
	 */
	public List<EasyRow> stAgentZx(String stDate) {
		StringBuffer sql = new StringBuffer();
		sql.append("select WORK_ACC,count(1) SEND_NUM,");		
		sql.append("sum(case when FK_STATUS='1' then 1 else 0 end) WAIT_RECEIVE_NUM,");
		sql.append("sum(case when FK_STATUS not in ('1','7') then 1 else 0 end) RECEIVED_NUM,");
		sql.append("sum(case when FK_STATUS='3' then 1 else 0 end) DONE_NUM,");
		sql.append("sum(case when FK_STATUS='4' then 1 else 0 end) NOT_DONE_NUM,");
		sql.append("sum(case when FK_STATUS in ('3','4','5') then 1 else 0 end) FEEDBACK_NUM,");
		sql.append("sum(case when TYPE_ID not in (").append(Constants.ZX_TYPE_OF_TRAIN).append(") then to_number(ACTUAL_WORK_LONG) else 0 end ) ZX_LONG,");
		sql.append("sum(case when TYPE_ID in (").append(Constants.ZX_TYPE_OF_TRAIN).append(") then to_number(ACTUAL_WORK_LONG) else 0 end ) ZX_TRAIN_LEN,");
		sql.append("listagg(CONTENT,';') WITHIN GROUP(order by WORK_TIME) ZX_DETAIL,");
		sql.append("listagg(BACKUP,';') WITHIN GROUP(order by WORK_TIME) ZX_BACKUP ");
		sql.append("from C_ZX_WORK where WORK_TIME='").append(stDate).append("' ");
		sql.append("group by WORK_ACC");

		try {
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计员工专项汇总记录:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
		
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计员工专项汇总记录失败.", e);
		}
		return null;
	}
	/**
	 * 统计命令单折算量
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentComOrdNum(String stDate) {
		try {
			queryHelper.setLogger(logger);
			String type = "ML";
			EasySQL sql = new EasySQL("SELECT b.CREATE_ACC AS USER_ACC");
			sql.append(" ,SUM( CASE ")
					.append(" WHEN b.contact_order_serv_type_code = 'ML'  AND b.contact_order_ser_item2_name IN('工艺无法修复，可快速办理换机') THEN 8 ")
					.append(" WHEN b.contact_order_serv_type_code = 'ML'  AND b.contact_order_ser_item2_name IN('快速送回','符合三包换机政策，快速办理换机','符合以换代修，快速办理换机','待件时间长（超7天），快速办理换机','保内机器近一年维修两次仍有故障换机','符合换机政策，商家与售后推诿，售后兜底办理换机') THEN 6 ")
					.append(" WHEN b.contact_order_serv_type_code = 'ML'  AND b.contact_order_ser_item2_name IN('套装一次性安装','更换网点/工程师','快速拉修','送修转寄修','送修转上门','时间未协商好，用户要求更换网点/工程师','其他原因，用户要求更换网点/工程师','服务技术问题，用户要求更换网点/工程师','服务态度问题，用户要求更换网点/工程师','服务时效问题，用户要求更换网点/工程师','服务规范性问题，用户要求更换网点/工程师','收费不认可，用户要求更换网点/工程师','处理方案不满意，用户要求更换网点/工程师','网点/工程师拒绝上门服务，用户要求更换网点/工程师','网点距离我家太远，用户要求更换网点') THEN 4 ")
					.append(" WHEN b.contact_order_serv_type_code = 'ML'  AND b.contact_order_ser_item2_name IN('符合退换机的免运费政策，快速按政策免除运费') THEN 3 ")
					.append(" WHEN b.contact_order_serv_type_code = 'ML'  AND b.contact_order_ser_item2_name IN('4小时内上门','按用户要求时间上门','特殊用户3小时内上门','反馈缺件详情') THEN 2 ")
					.append(" WHEN b.contact_order_serv_type_code = 'ML'  AND b.contact_order_ser_item2_name IN('2小时内回电','按用户要求时间回电','特殊用户1小时内回电') THEN 1 ")
					.append(" ELSE 0 ")
					.append(" END) AS COM_ORD_NUM ");
			sql.append("FROM C_NO_APPEALLIST b");
			sql.append(" WHERE b.CONTACT_ORDER_SERV_TYPE_CODE = 'ML' ");
			sql.append(stDate+" 00:00:00"," AND b.REQUIRE_CREATE_TIME >= ?");
			sql.append(stDate+" 23:59:59"," AND b.REQUIRE_CREATE_TIME <= ?");
			sql.append(" GROUP BY b.CREATE_ACC ");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计命令单折算量：" + sql.getSQL()+" ;param："+ Arrays.toString(sql.getParams()));
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计命令单折算量异常：", e);
		} finally {
			queryHelper.setLogger(null);
		}
		return new ArrayList<>();
	}
	/**
	 * 统计特殊工作量
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentSpZsNum(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t1.USER_ACC,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=1 then t1.DECLARE_NUMBER else 0 end) SP_ZS_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 then t2.CALL_RATIO*t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 then t1.DECLARE_NUMBER else 0 end) MANUAL_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='0.5' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_05_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='0.75' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_075_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='0.8' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_08_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='0.85' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_085_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='0.9' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_09_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='0.95' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_095_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='1' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_10_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='1.1' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_11_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='1.2' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_12_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='1.3' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_13_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='1.4' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_14_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='1.5' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_15_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='1.6' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_16_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='1.7' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_17_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='1.8' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_18_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='2' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_20_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='2.2' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_22_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='2.3' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_23_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='2.5' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_25_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='2.7' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_27_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='3' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_30_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='3.5' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_35_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='4' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_40_NUM,");
			sql.append("sum(case when t2.TYPE_CLASSIFY=2 and t2.CALL_RATIO='4.5' then t1.DECLARE_NUMBER else 0 end) MANUAL_ZS_45_NUM ");
			sql.append("from C_JX_SPECIAL_WORKLAOD t1 ");
			sql.append("left join C_JX_SPECIAL_WORKLOAD t2 on t1.TYPE_ID=t2.ID ");
			sql.append("where t1.STATUS!='03' and t1.DECLARE_DATE='").append(stDate).append("' ");
			sql.append("group by t1.USER_ACC");
			
			// logger.info(CommonUtil.getClassNameAndMethod(this) + "统计特殊工作量：" + sql.toStirng());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计特殊工作量异常：", e);
		}
		return null;
	}
	
	
	/**
	 * 按号码统计坐席外呼数量
	 * @param stDate
	 */
	public List<EasyRow> stAgentCallOutPhoneNum(String stDate) {
		try{			
			StringBuffer sql=new StringBuffer();
			sql.append("select AGENT_ACC,CALLED,count(1) CALL_NUM ");
			sql.append("from C_PF_V_CALL_RECORD ");
			sql.append("where DIRECTION='02' and substr(ANSWER_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by AGENT_ACC,CALLED");
				
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "按号码统计坐席外呼数量:" + sql.toString());
			queryHelper.setMaxRow(100000);
			return queryHelper.queryForList(sql.toString(), null);

		}catch(Exception e){
			logger.error(CommonUtil.getClassNameAndMethod(this) + "按号码统计坐席外呼数量失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计工单量
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentOrderCheck(String stDate) {
		try {		
			StringBuffer sql = new StringBuffer();
			sql.append("select AGENT_ACC,count(*) ORDER_CHECK_NUM ");
			sql.append("from C_NO_ORDER_CHECK ");			
			sql.append("where CHECK_TIME='").append(stDate).append("' ");
			sql.append("group by AGENT_ACC");
			
			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计工单普查问题量：" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);	
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计工单普查问题量异常：", e);
		}
		return null;
	}
	
	
	/**
	 * 统计个人加扣分
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentPersonScore(String stDate) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select USER_ACC,");
			sql.append("sum(case when PLUS>0 then PLUS else 0 end) PLUS_POINTS,");
			sql.append("sum(case when PLUS<0 then PLUS else 0 end) DEDUCT_POINTS ");
			sql.append("from C_JX_PLUS_POINTS_RECORD ");
			sql.append("where PLUS!=0 and substr(CREATE_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by USER_ACC");
			
			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计个人加扣分：" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
	
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计个人加扣分异常：", e);
		}
		return null;
	}
	
	
	/**
	 * 统计团队加扣分
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentTeamScore(String stDate) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select MONITOR_ACC,");
			sql.append("sum(case when IS_TEAM>0 then IS_TEAM else 0 end) PLUS_POINTS,");
			sql.append("sum(case when IS_TEAM<0 then IS_TEAM else 0 end) DEDUCT_POINTS ");
			sql.append("from C_JX_PLUS_POINTS_RECORD ");
			sql.append("where IS_TEAM!=0 and substr(CREATE_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by MONITOR_ACC");
			
			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计团队加扣分：" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
		
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计团队加扣分异常：", e);
		}
		return null;
	}
	
	
	/**
	 * 统计个人正能量指数
	 * @return
	 */
	public List<EasyRow> stAgentPostive() {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select USER_ACC,POSITIVE_INDEX from C_YG_POSITIVE_INDEX");
			
			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计个人正能量指数：" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计个人正能量指数异常：", e);
		}
		return null;
	}
	
	
	/**
	 * 统计质量明星上榜次数
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentQcStar(String stDate) {
		try {	
			EasySQL sql = new EasySQL();
			sql.append("select USER_ACC,count(*) QC_STAR_NUM");
			sql.append("from C_YG_WIN_RECORD where WIN_TYPE = '01'");
			sql.append(stDate, "and WIN_TIME=?");
			sql.append("group by USER_ACC");
			
			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计质量明星上榜次数：" + sql.getSQL());
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计质量明星上榜次数异常：", e);
		}
		return null;
	}
	
	
	/**
	 * 统计培训考试成绩
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentTestScore(String stDate) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select USER_ACC,round(avg(TEST_SCORE),2) AVG_TEST_SCORE ");
			sql.append("from C_YGHX_TEST_SCORE_RECORD ");
			sql.append("where substr(TEST_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by USER_ACC");

			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计培训考试成绩：" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计培训考试成绩异常：", e);
		}
		return null;
	}
	
	
	/**
	 * 统计培训时长
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentTrainLen(String stDate) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select USER_ACC,sum(TRAIN_DURATION) TRAIN_DURATION ");
			sql.append("from C_YGHX_TRAINING_RECORD ");
			sql.append("where TRAIN_DURATION is not null ");
			sql.append("and substr(TRAINING_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by USER_ACC");

			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计培训时长：" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计培训时长异常：", e);
		}
		return null;
	}
	
	
	/**
	 * 统计绩效排名
	 * @param userAcc
	 * @param stDate
	 * @return
	 */
	public int stAgentRank(String userAcc, String stDate) {
		int rank = 9999;
		
		try {
			EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME,Constants.MARS_DS_NAME);
			String sql = "select USER_ID from EASI_USER_LOGIN where USER_ACCT = ?";
			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计绩效排名：" + sql);
			String id = query.queryForString(sql, new String[]{userAcc});
					
			sql = "select * from C_JX_OBJECT_REF where BUSI_ID = '" + id + "'";
			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计绩效排名：" + sql);
			List<EasyRow> list = queryHelper.queryForList(sql, null);
			for(EasyRow row : list) {
				String objectId = row.getColumnValue("OBJECT_ID");
				sql = "select CODE from C_JX_MODEL where OBJECT_ID = '" + objectId + "'";
				//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计绩效排名：" + sql);
				String tableName = queryHelper.queryForString(sql, null);
				if(StringUtils.isBlank(tableName))
					continue;
				
				sql = "select RANK from " + tableName + " where EXAMER_CODE = '" + id 
						+ "' and substr(SCORE_TIME,1,10) = '" + stDate + "'";
				//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计绩效排名：" + sql);
				String temp = queryHelper.queryForString(sql, null);				
				if(StringUtils.isBlank(temp))
					temp = "9999";
						
				if(rank > CommonUtil.parseInt(temp))
					rank = CommonUtil.parseInt(temp);
			}			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计绩效排名异常：", e);
		}	
		return rank;	
	}


	public List<EasyRow> stAgentJxHistory(String stStartDate, String stEndDate) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("SELECT WORK_NO, WORK_DATE, nvl(SUM(REST_TIME),0) REST_TIME, nvl(SUM(LEAVE_TIME),0) LEAVE_TIME, nvl(SUM(ZX_LONG),0) ZX_LONG ,ST_AGENT_ID FROM (");
				sql.append("SELECT T1.USER_ACC AS WORK_NO, T1.CREATE_TIME AS WORK_DATE, STA.ID AS ST_AGENT_ID");
				sql.append(",SUM(T1.KX_LONG) REST_TIME, 0 AS LEAVE_TIME, 0 AS ZX_LONG ");
				sql.append("FROM C_PB_KX_LONG_LOG T1 ");
				sql.append("LEFT JOIN C_PB_LONG_TYPE T2 ON T1.KX_LONG_TYPE_ID = T2.ID ");
				sql.append("LEFT JOIN C_ST_AGENT STA ON STA.USER_ACC = T1.USER_ACC AND STA.ST_TYPE = '01' AND STA.ST_DATE = T1.CREATE_TIME ");
				sql.append("where T2.ID IN ('"+StringUtils.join(Constants.getBxType().split(","),"','")+"') ");
				sql.append(stStartDate," and T1.CREATE_TIME>=?");
				sql.append(stEndDate," and T1.CREATE_TIME<=?");
				sql.append("GROUP BY T1.USER_ACC, T1.CREATE_TIME, STA.ID ");
			sql.append("UNION ALL ");
				sql.append("SELECT T2.APPLY_ACC AS WORK_NO, substr(T2.LEAVE_START_TIME, 1, 10) AS WORK_DATE, STA.ID AS ST_AGENT_ID");
				sql.append(",0 AS REST_TIME, SUM(T2.LEAVE_LONG) LEAVE_TIME, 0 AS ZX_LONG ");
				sql.append("FROM C_PB_LEAVE T2 ");
				sql.append("LEFT JOIN C_ST_AGENT STA ON STA.USER_ACC = T2.APPLY_ACC AND STA.ST_TYPE = '01' AND STA.ST_DATE = substr(T2.LEAVE_START_TIME, 1, 10) ");
				sql.append("WHERE t2.BZ_CHECK_RESULT = '2' AND t2.BUSI_TYPE = 2 ");
				sql.append(stStartDate +" 00:00:00"," AND T2.LEAVE_START_TIME >=?");
				sql.append(stEndDate +" 23:59:59"," AND T2.LEAVE_START_TIME <=?");
				sql.append("GROUP BY T2.APPLY_ACC, substr(T2.LEAVE_START_TIME, 1, 10) , STA.ID");
//			sql.append("UNION ALL ");
//				sql.append("SELECT T3.WORK_ACC AS WORK_NO, T3.WORK_TIME AS WORK_DATE, STA.ID AS ST_AGENT_ID");
//				sql.append(",0 AS REST_TIME, 0 AS LEAVE_TIME, SUM(T3.ACTUAL_WORK_LONG) ZX_LONG ");
//				sql.append("FROM C_ZX_WORK T3 ");
//				sql.append("LEFT JOIN C_ST_AGENT STA ON STA.USER_ACC = T3.WORK_ACC AND STA.ST_TYPE = '1' AND STA.ST_DATE = T3.WORK_TIME ");
//				sql.append("WHERE 1=1 ");
//				sql.append(stStartDate," AND T3.WORK_TIME >= ?");
//				sql.append(stEndDate," AND T3.WORK_TIME <= ?");
//				sql.append("GROUP BY T3.WORK_ACC, T3.WORK_TIME");
			sql.append(") stat ");
			sql.append(" GROUP BY WORK_NO, WORK_DATE ,ST_AGENT_ID ");
			sql.append(" ORDER BY WORK_NO, WORK_DATE ,ST_AGENT_ID ");

			logger.info("【" + Thread.currentThread().getName() + "】" + "统计坐席时间范围内每天的补休、请假, SQL:" + sql.getSQL() + "参数：" + JSONObject.toJSONString(sql.getParams()));
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席时间范围内每天的补休、请假失败.", e);
		}
		return null;
	}
	public List<EasyRow> stAgentZxHistory(String stStartDate, String stEndDate) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("SELECT WORK_NO, WORK_DATE, nvl(SUM(REST_TIME),0) REST_TIME, nvl(SUM(LEAVE_TIME),0) LEAVE_TIME, nvl(SUM(ZX_LONG),0) ZX_LONG ,ST_AGENT_ID FROM (");
				sql.append("SELECT T3.WORK_ACC AS WORK_NO, T3.WORK_TIME AS WORK_DATE, STA.ID AS ST_AGENT_ID");
				sql.append(",0 AS REST_TIME, 0 AS LEAVE_TIME ");
				sql.append(",sum(case when TYPE_ID not in (").append(Constants.ZX_TYPE_OF_TRAIN).append(") then to_number(ACTUAL_WORK_LONG) else 0 end ) ZX_LONG");
				sql.append("FROM C_ZX_WORK T3 ");
				sql.append("LEFT JOIN C_ST_AGENT STA ON STA.USER_ACC = T3.WORK_ACC AND STA.ST_TYPE = '01' AND STA.ST_DATE = T3.WORK_TIME ");
				sql.append("WHERE 1=1 ");
				sql.append(stStartDate," AND T3.WORK_TIME >= ?");
				sql.append(stEndDate," AND T3.WORK_TIME <= ?");
				sql.append("GROUP BY T3.WORK_ACC, T3.WORK_TIME , STA.ID");
			sql.append(") stat ");
			sql.append(" GROUP BY WORK_NO, WORK_DATE ,ST_AGENT_ID ");
			sql.append(" ORDER BY WORK_NO, WORK_DATE ,ST_AGENT_ID ");

			logger.info("【" + Thread.currentThread().getName() + "】" + "统计坐席时间范围内每天的专项时长, SQL:" + sql.getSQL() + "参数：" + JSONObject.toJSONString(sql.getParams()));
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席时间范围内每天的专项时长失败.", e);
		}
		return new ArrayList<>();
	}
	/**
	 * 统计某时段坐席的补休时长 = 培训补休 + 考试补休
	 * @param dsName
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentRestTimeLen(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select T1.USER_ACC AS WORK_NO,sum(T1.KX_LONG) REST_TIME ");
			sql.append("FROM C_PB_KX_LONG_LOG T1 ");
			sql.append("LEFT JOIN C_PB_LONG_TYPE T2 ON T1.KX_LONG_TYPE_ID = T2.ID ");
			sql.append("where T2.ID IN ('"+StringUtils.join(Constants.getBxType().split(","),"','")+"') ");
			sql.append("and T1.CREATE_TIME='").append(stDate).append("' ");
			sql.append("group by T1.USER_ACC");

			logger.info("【"+Thread.currentThread().getName()+"】" + "统计补休时长：" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计补休时长异常：", e);
		}
		return null;
	}
	/**
	 * 统计某时段坐席的请假时长
	 * @param dsName
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentLeaveTimeLen(String stDate) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" select T1.APPLY_ACC AS WORK_NO ");
			sql.append(" ,sum(t1.LEAVE_LONG) LEAVE_LONG ");
			sql.append(" from C_PB_LEAVE t1 ");
			sql.append(" where t1.BZ_CHECK_RESULT = '2' AND t1.BUSI_TYPE = 2 ");
			sql.append(stDate.substring(0,10)," AND substr(t1.LEAVE_START_TIME,0,10) = ? ");
//			sql.append(stDate.substring(0,10)+" 00:00:00"," AND t1.LEAVE_START_TIME >= ? "); //有可能跨天
//			sql.append(stDate.substring(0,10)+" 23:59:59"," AND t1.LEAVE_START_TIME <= ? ");
			sql.append(" group by T1.APPLY_ACC");

			logger.info("【"+Thread.currentThread().getName()+"】" + "统计某时段坐席的请假时长,SQL:" + sql.getSQL() + "参数：" + JSONObject.toJSONString(sql.getParams()));
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计某时段坐席的请假时长失败.", e);
		}
		return null;
	}
	/**
	 * 统计某时段坐席的提前下班时长（扣休时长）
	 * @param dsName
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentKxTimeLen(String stDate) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("select t1.USER_ACC AS WORK_NO");
			sql.append(",sum(t1.KX_LONG) KX_LONG");
			sql.append("from C_PB_TEMP_AFTERWORK t1");
			sql.append(stDate.substring(0,10),"where STATUS = '02' AND t1.APPLY_DATE = ?");
			sql.append("group by t1.USER_ACC");

			logger.info("【"+Thread.currentThread().getName()+"】" + "统计某时段坐席的提前下班时长,SQL:" + sql.getSQL() + "参数：" + JSONObject.toJSONString(sql.getParams()));
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计某时段坐席的提前下班时长失败.", e);
		}
		return null;
	}

	/**
	 * 统计某时段坐席的达标值和哺乳假
	 * @param dsName
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentStandard(String stDate) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("select t1.USER_ACC AS WORK_NO");
			sql.append(",t3.WORK_TIME");//上班时间
			//计算达标值
			sql.append(",case when '"+stDate+"' between T2.STANDARD_BEGIN_TIME and T2.STANDARD_END_TIME then T2.STANDARD_VALUE else 0 end STANDARD_VALUE ");
			//判断今天的日期是否在哺乳假的有效日期范围内
			sql.append(",case when '"+stDate+"' between T2.BREASTFEED_LEAVE_BEGIN_TIME and T2.BREASTFEED_LEAVE_END_TIME then 1 else 0 end BREASTFEED_LEAVE ");
			sql.append("from C_PB_SCHEDULING_PERSON t1");
			sql.append("LEFT JOIN C_YG_EMPLOYEE_STANDARD t2 ON T2.USER_ACC = T1.USER_ACC");
			sql.append("left join C_PB_SCHEDULING t3 on T1.SCHEDULING_ID=T3.ID ");
			sql.append(stDate,"WHERE t1.PLAN_DATE= ?",false);
			sql.append("ORDER by T1.USER_ACC");

			logger.info("【"+Thread.currentThread().getName()+"】" + "统计某时段坐席的达标值和哺乳假,SQL:" + sql.getSQL() + "参数：" + JSONObject.toJSONString(sql.getParams()));
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计某时段坐席的达标值和哺乳假失败.", e);
		}
		return null;
	}
	/**
	 * 统计某时段坐席的话务量
	 * @param dsName
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentCallNum(String dsName, String beginTime, String endTime) {
		EasyQuery queryHelper = EasyQuery.getQuery(Constants.APP_NAME, dsName);
		try {
			EasySQL sql = new EasySQL();
			sql.append("select t2.OBJECT_NAME,sum(t1.T_LOGIN) T_LOGIN,sum(t1.N_CALL_IB) N_CALL_IB,");
			sql.append("sum(t1.N_CALLS_C_OBTA) N_CALLS_C_OBTA,sum(t1.T_CALLS_IN) T_CALLS_IN,");
			sql.append("sum(t1.T_TALK_OB) T_TALK_OB,sum(t1.T_RADEY) T_RADEY,");
			sql.append("sum(t1.T_NOT_READY) T_NOT_READY,sum(t1.T_ACW) T_ACW,");
			sql.append("sum(t1.T_RING_OB) T_RING_OB,sum(t1.T_ANSWER_IN) T_ANSWER_IN,");
			sql.append("sum(t1.N_Talk_IN_10) N_Talk_IN_10,sum(t1.N_Talk_OB_10) N_Talk_OB_10,");
			sql.append("sum(t1.N_TALK_LN_600) N_TALK_LN_600 ");
			sql.append("from R_AGENT_TEMP_NO_AGG t1 left join O_AGENT_TEMP_NO_AGG t2 ");
			sql.append("on t1.OBJECT_ID=t2.OBJECT_ID ");
			sql.append(beginTime.substring(0,12),"where t1.TIME_KEY >= ?");
			sql.append(endTime.substring(0,12),"and t1.TIME_KEY < ?");
			sql.append("group by t2.OBJECT_NAME");
			queryHelper.setMaxRow(6000);
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计某时段坐席的话务量,SQL:" + sql.getSQL() + "参数：" + JSONObject.toJSONString(sql.getParams()));
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
		
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计某时段坐席的话务量失败.", e);
		}
		return null;		
	}

	
	/**
	 * 统计某时段坐席的话务折算量
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgentCallZs(String beginTime, String endTime) {
		try {
			EasySQL sql = new EasySQL();	
			sql.append("select CP.AGENT_ACC,");
			sql.append("sum(case when CP.DIRECTION='01' then 1 else 0 end) CALL_IN_NUM,");
			sql.append("sum(case when CP.DIRECTION='01' and CP.LENS<=10 then 1 else 0 end) TALK_IN_10,");
			sql.append("sum(case when CP.DIRECTION='01' and CP.LENS>=600 then 1 else 0 end) MORE_600_NUM,");
			sql.append("sum(case when CP.DIRECTION='01' and CP.LENS>=480 then floor(CP.LENS/240) else 0 end) CALL_IN_ZS,");
			sql.append("sum(case when CP.DIRECTION='02' then 1 else 0 end) CALL_OUT_NUM,"); 
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS<=10 then 1 else 0 end) TALK_OB_10,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 then CP.ICC_COEFFICIENT else 0 end) CALL_OUT_ZS,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=0.5 then 1 else 0 end) CALL_OUT_ZS_05_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=0.75 then 1 else 0 end) CALL_OUT_ZS_075_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=0.8 then 1 else 0 end) CALL_OUT_ZS_08_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=0.85 then 1 else 0 end) CALL_OUT_ZS_085_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=0.9 then 1 else 0 end) CALL_OUT_ZS_09_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=0.95 then 1 else 0 end) CALL_OUT_ZS_095_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=1 then 1 else 0 end) CALL_OUT_ZS_10_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=1.1 then 1 else 0 end) CALL_OUT_ZS_11_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=1.2 then 1 else 0 end) CALL_OUT_ZS_12_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=1.3 then 1 else 0 end) CALL_OUT_ZS_13_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=1.4 then 1 else 0 end) CALL_OUT_ZS_14_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=1.5 then 1 else 0 end) CALL_OUT_ZS_15_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=1.6 then 1 else 0 end) CALL_OUT_ZS_16_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=1.7 then 1 else 0 end) CALL_OUT_ZS_17_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=1.8 then 1 else 0 end) CALL_OUT_ZS_18_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=2 then 1 else 0 end) CALL_OUT_ZS_20_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=2.2 then 1 else 0 end) CALL_OUT_ZS_22_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=2.3 then 1 else 0 end) CALL_OUT_ZS_23_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=2.5 then 1 else 0 end) CALL_OUT_ZS_25_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=2.7 then 1 else 0 end) CALL_OUT_ZS_27_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=3 then 1 else 0 end) CALL_OUT_ZS_30_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=3.5 then 1 else 0 end) CALL_OUT_ZS_35_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=4 then 1 else 0 end) CALL_OUT_ZS_40_NUM,");
			sql.append("sum(case when CP.DIRECTION='02' and CP.LENS>10 and CP.ICC_COEFFICIENT=4.5 then 1 else 0 end) CALL_OUT_ZS_45_NUM,");
			sql.append("SUM(CASE WHEN CV.IS_VIDEO = '1' THEN 1 ELSE 0 END) AS VIDEO_NUM ");
			sql.append("from C_PF_CALL_RECORD CP ");
			sql.append("LEFT JOIN CC_VIDEO_RECORD CV ON CV.SESSION_ID = CP.SESSION_ID AND CV.AGENT_ACC = CP.AGENT_ACC ");
			sql.append(beginTime,"where CP.ANSWER_TIME>= ?");
			sql.append(endTime,"and CP.ANSWER_TIME< ?");
			sql.append("group by CP.AGENT_ACC");
			queryHelper.setMaxRow(6000);
			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计某时段坐席的话务折算量,SQL:" + sql.getSQL());
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计某时段坐席的话务折算量失败.", e);
		}
		return null;			
	}
	
	
	/**
	 * 统计坐席当天第一通话务时间
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentFirstCall(String stDate) {
		try {
			EasySQL sql = new EasySQL();	
			sql.append("select AGENT_ACC,min(ANSWER_TIME) ANSWER_TIME ");
			sql.append("from C_PF_V_CALL_RECORD ");
			sql.append(stDate.substring(0,10),"where ANSWER_TIME = ?");
			sql.append("group by AGENT_ACC");
			
			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席当天第一通话务时间,SQL:" + sql.toString());
			queryHelper.setMaxRow(6000);
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席当天第一通话务时间失败.", e);
		}
		return null;
	}
	
		
	/**
	 * 统计坐席当天10分钟话务状况
	 * @param userAcc
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	public List<EasyRow> stAgent10Call(String userAcc, String beginTime, String endTime) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select * from C_PF_V_CALL_RECORD where AGENT_ACC='").append(userAcc).append("'");
			sql.append("and ANSWER_TIME>='").append(beginTime).append("' ");
			sql.append("and ANSWER_TIME<'").append(endTime).append("' ");
			sql.append("and END_TIME is not null ");
			sql.append("order by ANSWER_TIME");
			
			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席当天10分钟话务状况,SQL:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席当天10分钟话务状况失败.", e);
		}
		return null;		
	}
	
	
	/**
	 * 统计坐席值班情况（全媒体）
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentMediaDuty(String stDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select AGENT_ID,FIRST_LOGIN_TIME,UPDATE_TIME,LOGIN_COUNT,ONLINE_TIME,");
			sql.append("SERVICE_TIME,NOTREADY1_TIME,NOTREADY1_COUNT,INBOUND_READY_TIME,INBOUND_NOTREADY_TIME,FIRST_SERVER_TIME, ");
			sql.append("NEW_NOTREADY_TIME,NEW_READY_TIME,");
			sql.append("LAST_SERVER_END_TIME,FIRST_FREE_TIME,LAST_REST_TIME ");
			sql.append("from STAT.CC_RPT_AGENT_INDEX ");
			sql.append("where DATE_ID='").append(stDate.replace("-", "")).append("'");
			
//			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席值班情况（全媒体）,SQL:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席值班情况（全媒体）失败.", e);
		}
		return new ArrayList<>();
	}
	
	
	/**
	 * 统计坐席的接通量、转出量、转入量（全媒体）
	 * @param stDate
	 * @return
	 */
	public List<EasyRow> stAgentMediaExtend(String stDate) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select AGENT_ACC,count(1) TOTAL,");
			sql.append("sum(case when HANGUP_TYPE='04' then 1 else 0 end) TRANS_OUT_NUM,");
			sql.append("sum(case when IN_TYPE='03' then 1 else 0 end) TRANS_IN_NUM ");
			sql.append("from V_PF_SESSION_RECORD ");
			sql.append("where substr(ANSWER_TIME,1,10)='").append(stDate).append("' ");
			sql.append("group by AGENT_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席的接通量、转出量、转入量（全媒体）:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席的接通量、转出量、转入量（全媒体）失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计班组平均接待量（全媒体）
	 * @param stDate
	 * @param deptCode
	 * @param schedulingId
	 * @return
	 */
	public EasyRow stDeptMediaAvgReception(String stDate, String deptCode, String schedulingId) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select count(1) RECEPTION_NUM,count(distinct t1.AGENT_ACC) AGENT_NUM ");
			sql.append("from V_PF_SESSION_RECORD t1 left join C_PB_SCHEDULING_PERSON t2 "); 
			sql.append("on t1.AGENT_ACC=t2.USER_ACC and substr(t1.ANSWER_TIME,1,10)=t2.PLAN_DATE ");
			sql.append("where substr(t1.ANSWER_TIME,1,10)='").append(stDate).append("' ");
			sql.append("and t1.AGENT_DEPT='").append(deptCode).append("' ");
			sql.append("and t2.SCHEDULING_ID='").append(schedulingId).append("'");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计班组平均接待量（全媒体）:" + sql.toString());
			return queryHelper.queryForRow(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计班组平均接待量（全媒体）失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席值班天数
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> stAgentDuty(String qcBeginDate, String qcEndDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select USER_ACC,count(1) DUTY_DAYS,sum(TIMELONG) ACT_DUTY_DAYS ");		
			sql.append("from C_PB_SCHEDULING_PERSON ");
			sql.append("where TIMELONG>0 and PLAN_DATE>='").append(qcBeginDate).append("' ");
			sql.append("and PLAN_DATE<'").append(qcEndDate).append("' ");
			sql.append("group by USER_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席值班天数:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席值班天数失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席全媒体接待量、接待天数、满意量
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> stAgentReception(String qcBeginDate, String qcEndDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select AGENT_ACC,count(1) RECEPTION_NUM,");
			sql.append("count(distinct substr(ANSWER_TIME,1,10)) RECEPTION_DAYS,");
			sql.append("sum(case when SATISF_NAME is not null then 1 else 0 end) SATIS_TOTAL,");
			sql.append("sum(case when SATISF_NAME in ('非常满意','满意') then 1 else 0 end) SATIS_NUM ");
			sql.append("from V_PF_SESSION_RECORD ");
			sql.append("where substr(ANSWER_TIME,1,10)>='").append(qcBeginDate).append("' ");
			sql.append("and substr(ANSWER_TIME,1,10)<'").append(qcEndDate).append("' ");
			sql.append("group by AGENT_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席全媒体接待量、接待天数、满意量:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席全媒体接待量、接待天数、满意量失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席专项时长
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> stAgentZxLen(String qcBeginDate, String qcEndDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select WORK_ACC,sum(ACTUAL_WORK_LONG) ZX_HOURS ");
			sql.append("from C_ZX_WORK ");
			sql.append("where WORK_TIME>='").append(qcBeginDate).append("' ");
			sql.append("and WORK_TIME<'").append(qcEndDate).append("' ");
			sql.append("group by WORK_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席专项时长:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席专项时长失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席全媒体售后工单量
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> stAgentMediaSh(String qcBeginDate, String qcEndDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select AGENT_ACC,count(1) SH_ORDER_NUM ");
			sql.append("from C_NO_CONTACT ");
			sql.append("where substr(CREATE_TIME,1,10)>='").append(qcBeginDate).append("' ");
			sql.append("and substr(CREATE_TIME,1,10)<'").append(qcEndDate).append("' ");
			sql.append("group by AGENT_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席全媒体售后工单量:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席全媒体售后工单量失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席全媒体首次平均响应时长、平均响应时长
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> stAgentMediaExtend(String qcBeginDate, String qcEndDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select t2.AGENT_ACC,");
			sql.append("round(avg(t1.FIRST_REPLY),2) AVG_FIRST_REPLY,");
			sql.append("round(avg(t1.AVG_REPLY),2) AVG_REPLY ");
			sql.append("from C_PF_QC_SESSION t1 ");
			sql.append("left join V_PF_SESSION_RECORD t2 on t1.SESSION_RECORD_ID=t2.ID ");
			sql.append("where substr(ANSWER_TIME,1,10)>='").append(qcBeginDate).append("' ");
			sql.append("and substr(ANSWER_TIME,1,10)<'").append(qcEndDate).append("' ");
			sql.append("group by t2.AGENT_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席全媒体首次平均响应时长、平均响应时长:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席全媒体首次平均响应时长、平均响应时长失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席全媒体质检平均分
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> stAgentAvgQcScore(String qcBeginDate, String qcEndDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select AGENT_ACC,");
			sql.append("round(avg(case when QC_SCORE=0 then null else QC_SCORE end),2) AVG_MANUAL_QC_SCORE ");
			sql.append("from C_PF_QC_RECORD ");
			sql.append("where SESSION_TYPE='02' ");
			sql.append("and substr(MANUAL_QC_TIME,1,10)>='").append(qcBeginDate).append("' ");
			sql.append("and substr(MANUAL_QC_TIME,1,10)<'").append(qcEndDate).append("' ");
			sql.append("group by AGENT_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席全媒体质检平均分:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席全媒体质检平均分失败.",e);
		}
		return null;
	}	
	
	
	/**
	 * 统计坐席个人日常加扣分
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> stAgentPersonScore(String qcBeginDate, String qcEndDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select USER_ACC,sum(PLUS) DAILY_ADD_SCORE ");
			sql.append("from C_JX_PLUS_POINTS_RECORD ");
			sql.append("where PLUS!=0 and substr(CREATE_TIME,1,10)>='").append(qcBeginDate).append("' ");
			sql.append("and substr(CREATE_TIME,1,10)<='").append(qcEndDate).append("' ");
			sql.append("group by USER_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席个人日常加扣分:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席个人日常加扣分失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席团队日常加扣分
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> stAgentTeamScore(String qcBeginDate, String qcEndDate) {
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select MONITOR_ACC,sum(IS_TEAM) DAILY_ADD_SCORE ");
			sql.append("from C_JX_PLUS_POINTS_RECORD ");
			sql.append("where IS_TEAM!=0 and substr(CREATE_TIME,1,10)>='").append(qcBeginDate).append("' ");
			sql.append("and substr(CREATE_TIME,1,10)<='").append(qcEndDate).append("' ");
			sql.append("group by MONITOR_ACC");
			
			//logger.debug(CommonUtil.getClassNameAndMethod(this) + "统计坐席个人日常加扣分:" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席个人日常加扣分失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 统计坐席培训考试成绩
	 * @param qcBeginDate
	 * @param qcEndDate
	 * @return
	 */
	public List<EasyRow> stAgentTestScore(String qcBeginDate, String qcEndDate) {	
		try {
			StringBuffer sql = new StringBuffer();
			sql.append("select USER_ACC,round(avg(TEST_SCORE),2) AVG_TEST_SCORE ");
			sql.append("from C_YGHX_TEST_SCORE_RECORD ");
			sql.append("where substr(TEST_TIME,1,10)>='").append(qcBeginDate).append("' ");
			sql.append("and substr(TEST_TIME,1,10)<'").append(qcEndDate).append("' ");
			sql.append("group by USER_ACC");

			//logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席培训考试成绩：" + sql.toString());
			return queryHelper.queryForList(sql.toString(), null);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席培训考试成绩失败.", e);
		}
		return null;
	}	
}
