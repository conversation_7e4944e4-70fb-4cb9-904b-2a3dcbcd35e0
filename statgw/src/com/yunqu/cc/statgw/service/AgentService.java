package com.yunqu.cc.statgw.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.yunqu.cc.statgw.base.CommonLogger;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.statgw.base.CommonTestLogger;
import com.yunqu.cc.statgw.base.Constants;
import com.yunqu.cc.statgw.dao.AgentDao;
import com.yunqu.cc.statgw.dao.AgentQdDao;
import com.yunqu.cc.statgw.dao.AgentSkillDao;
import com.yunqu.cc.statgw.model.Agent;


public class AgentService extends BaseService {
	private AgentDao agentDao = new AgentDao();
	private Map<String, Agent> agentsAcc = new HashMap<String, Agent>();
	private Map<String, Agent> agentsNo = new HashMap<String, Agent>();
	private Map<String, String> stIds = new HashMap<String, String>();
	public Logger stAgentCallMonitor = CommonLogger.getLogger("stAgentCallMonitor");


	public AgentService(String stDate) {
		List<EasyRow> list = Optional.ofNullable(agentDao.findAllAgent(stDate)).orElse(new ArrayList<>());
		for (int i = 0; i<list.size(); i++) {
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("USER_ACC");
			String workNo = row.getColumnValue("WORK_NO");
			
			Agent agent = new Agent();
			agent.setUserAcc(userAcc);
			agent.setWorkNo(workNo);
			agent.setUserName(row.getColumnValue("USER_NAME"));
			agent.setDeptCode(row.getColumnValue("DEPT_CODE"));
			agent.setDeptName(row.getColumnValue("DEPT_NAME"));
			agent.setEpCode(row.getColumnValue("EP_CODE"));
			agent.setAreaCode(row.getColumnValue("AREACODE"));
			agent.setAreaName(row.getColumnValue("AREA_NAME"));
			agent.setWorkMonths(row.getColumnValue("ENTRY_MONTHS"));
			agent.setSchedulingId(row.getColumnValue("SCHEDULING_ID"));
			agent.setSchedulingName(row.getColumnValue("SCHEDULING_NAME"));
			agent.setStartTime(stDate + " " + row.getColumnValue("STARTTIME"));
			agent.setEndTime(stDate + " " + row.getColumnValue("ENDTIME"));
			agent.setAttendanceLen(CommonUtil.parseDouble(row.getColumnValue("TIMELONG")));
			agent.setEntryTime(row.getColumnValue("ENTRY_TIME"));

			agentsAcc.put(userAcc, agent);
			agentsNo.put(workNo, agent);
		}	
	}
	
	
	/**
	 * 清理保存id的集合，由于部分统计任务存在时间记录差异
	 */
	public void cleanStIds() {
		stIds.clear();
	}
	
	
	
	/**
	 * 统计坐席的监听率，按月生成一条记录，每天更新
	 * @param stDate
	 */
	public void stAgentListenRate(String stDate) {
		// 质检月开始、结束时间
		String qcBeginDate = stDate.substring(0, 8) + Constants.QC_DATE_OF_MONTH;					
		if(Integer.parseInt(stDate.substring(8, 10)) < Constants.QC_DATE_OF_MONTH)
			qcBeginDate = DateUtil.addMonth(DateUtil.TIME_FORMAT_YMD, qcBeginDate, -1);
		String qcEndDate = DateUtil.addMonth(DateUtil.TIME_FORMAT_YMD, qcBeginDate, 1);
		
		// 本周开始、结束时间
		String qcBeginWeek = getFirstDayOfWeek(DateUtil.TIME_FORMAT_YMD, stDate);		
		String qcEndWeek = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, qcBeginWeek, 6);
		
		// 删除坐席话务的汇总记录
		agentDao.delAgentRecord("C_ST_AGENT_LISTEN_RATE", qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
		
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		
		// 通话量（质检月）
		List<EasyRow> list = agentDao.countCallByAgent(qcBeginDate, qcEndDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到坐席的通话量（质检月）记录数:" + (list == null ? 0 :list.size()));	
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");		
			CommonTestLogger.logger.info(userAcc+"查询到坐席的通话量（质检月）记录数");
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					CommonTestLogger.logger.info(userAcc+"的stAgentId:"+stAgentId);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null){
						CommonTestLogger.logger.info(userAcc+"的agentc无法查到");
						CommonTestLogger.logger.info("agentsAcc长度"+agentsAcc.size()+"");
						continue;
					}
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			param.put("VOICE_CALL_IN_NUM", CommonUtil.parseInt(row.getColumnValue("VOICE_CALL_IN_NUM")));
			param.put("VOICE_CALL_OUT_NUM", CommonUtil.parseInt(row.getColumnValue("VOICE_CALL_OUT_NUM")));			
			param.put("VOICE_CALL_NUM", CommonUtil.parseInt(row.getColumnValue("VOICE_CALL_NUM")));		
			param.put("MEDIA_CALL_IN_NUM", CommonUtil.parseInt(row.getColumnValue("MEDIA_CALL_IN_NUM")));
			param.put("MEDIA_CALL_OUT_NUM", CommonUtil.parseInt(row.getColumnValue("MEDIA_CALL_OUT_NUM")));		
			param.put("MEDIA_CALL_NUM", CommonUtil.parseInt(row.getColumnValue("MEDIA_CALL_NUM")));
			map.put(userAcc, param);
		}
		
		// 质检量（质检月）
		list = agentDao.countQcByAgent(qcBeginDate, qcEndDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到坐席的质检量（质检月）记录数:" + (list == null ? 0 :list.size()));		
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}				
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			int voiceCallInNum = param.get("VOICE_CALL_IN_NUM") == null ? 0 : (int)param.get("VOICE_CALL_IN_NUM");
			int voiceQcInNum = CommonUtil.parseInt(row.getColumnValue("VOICE_QC_IN_NUM"));
			double voiceListenInRate = calcRate(voiceCallInNum, voiceQcInNum);
			
			int voiceCallOutNum = param.get("VOICE_CALL_OUT_NUM") == null ? 0 : (int)param.get("VOICE_CALL_OUT_NUM");
			int voiceQcOutNum = CommonUtil.parseInt(row.getColumnValue("VOICE_QC_OUT_NUM"));
			double voiceListenOutRate = calcRate(voiceCallOutNum, voiceQcOutNum);
			
			int voiceCallNum = param.get("VOICE_CALL_NUM") == null ? 0 : (int)param.get("VOICE_CALL_NUM");
			int voiceQcNum = CommonUtil.parseInt(row.getColumnValue("VOICE_QC_NUM"));
			double voiceListenRate = calcRate(voiceCallNum, voiceQcNum);
			
			int mediaCallInNum = param.get("MEDIA_CALL_IN_NUM") == null ? 0 : (int)param.get("MEDIA_CALL_IN_NUM");
			int mediaQcInNum = CommonUtil.parseInt(row.getColumnValue("MEDIA_QC_IN_NUM"));
			double mediaListenInRate = calcRate(mediaCallInNum, mediaQcInNum);
			
			int mediaCallOutNum = param.get("MEDIA_CALL_OUT_NUM") == null ? 0 : (int)param.get("MEDIA_CALL_OUT_NUM");
			int mediaQcOutNum = CommonUtil.parseInt(row.getColumnValue("MEDIA_QC_OUT_NUM"));
			double mediaListenOutRate = calcRate(mediaCallOutNum, mediaQcOutNum);
						
			int mediaCallNum = param.get("MEDIA_CALL_NUM") == null ? 0 : (int)param.get("MEDIA_CALL_NUM");
			int mediaQcNum = CommonUtil.parseInt(row.getColumnValue("MEDIA_QC_NUM"));
			double mediaListenRate = calcRate(mediaCallNum, mediaQcNum);
			double listenRate = calcRate(voiceCallNum + mediaCallNum, voiceQcNum + mediaQcNum);
				
			param.put("VOICE_LISTEN_IN_NUM", voiceQcInNum);
			param.put("VOICE_LISTEN_IN_RATE", voiceListenInRate);
			param.put("VOICE_LISTEN_OUT_NUM", voiceQcOutNum);
			param.put("VOICE_LISTEN_OUT_RATE", voiceListenOutRate);			
			param.put("VOICE_LISTEN_NUM", voiceQcNum);
			param.put("VOICE_LISTEN_RATE", voiceListenRate);		
			param.put("MEDIA_LISTEN_IN_NUM", mediaQcInNum);
			param.put("MEDIA_LISTEN_IN_RATE", mediaListenInRate);
			param.put("MEDIA_LISTEN_OUT_NUM", mediaQcOutNum);
			param.put("MEDIA_LISTEN_OUT_RATE", mediaListenOutRate);					
			param.put("MEDIA_LISTEN_NUM", mediaQcNum);
			param.put("MEDIA_LISTEN_RATE", mediaListenRate);
			param.put("LISTEN_RATE", listenRate);
			map.put(userAcc, param);
		}

		//2021-1-27新增，质检绩效月统计
		String qcPerBeginDate = stDate.substring(0, 8) + Constants.PER_DATE_OF_MONTH;					
		if(Integer.parseInt(stDate.substring(8, 10)) < Constants.PER_DATE_OF_MONTH) {
			qcPerBeginDate = DateUtil.addMonth(DateUtil.TIME_FORMAT_YMD, qcPerBeginDate, -1);
		}
		String qcPerEndDate = DateUtil.addMonth(DateUtil.TIME_FORMAT_YMD, qcPerBeginDate, 1);
		//qcPerEndDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, qcPerEndDate, -1);
		list = Optional.ofNullable(agentDao.countQcByAgent(qcPerBeginDate, qcPerEndDate)).orElse(new ArrayList<>());
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcPerBeginDate + "~" + qcPerEndDate + "，查询到坐席的质检量（绩效月）记录数:" + (list == null ? 0 :list.size()));		
		for(EasyRow row : list) {
			String userAcc = row.getColumnValue("AGENT_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}				
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			int voiceCallNum = param.get("VOICE_CALL_NUM") == null ? 0 : (int)param.get("VOICE_CALL_NUM");
			int voiceQcNum = CommonUtil.parseInt(row.getColumnValue("VOICE_QC_NUM"));
			double voiceListenRate = calcRate(voiceCallNum, voiceQcNum);
			
			int voiceCallInNum = param.get("VOICE_CALL_IN_NUM") == null ? 0 : (int)param.get("VOICE_CALL_IN_NUM");
			int voiceQcInNum = CommonUtil.parseInt(row.getColumnValue("VOICE_QC_IN_NUM"));
			double voiceListenInRate = calcRate(voiceCallInNum, voiceQcInNum);
			
			int voiceCallOutNum = param.get("VOICE_CALL_OUT_NUM") == null ? 0 : (int)param.get("VOICE_CALL_OUT_NUM");
			int voiceQcOutNum = CommonUtil.parseInt(row.getColumnValue("VOICE_QC_OUT_NUM"));
			double voiceListenOutRate = calcRate(voiceCallOutNum, voiceQcOutNum);
			
			int mediaCallNum = param.get("MEDIA_CALL_NUM") == null ? 0 : (int)param.get("MEDIA_CALL_NUM");
			int mediaQcNum = CommonUtil.parseInt(row.getColumnValue("MEDIA_QC_NUM"));
			double mediaListenRate = calcRate(mediaCallNum, mediaQcNum);

			param.put("VOICE_LISTEN_PER_RATE", voiceListenRate);
			param.put("VOICE_LISTEN_PER_IN_RATE", voiceListenInRate);
			param.put("VOICE_LISTEN_PER_OUT_RATE", voiceListenOutRate);
			param.put("MEDIA_LISTEN_PER_RATE", mediaListenRate);

			map.put(userAcc, param);
		}
		
		// 通话量（本周）
		list = agentDao.countCallByAgent(qcBeginWeek, qcEndWeek);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginWeek + "~" + qcEndWeek + "，查询到坐席的通话量（本周）记录数:" + (list == null ? 0 :list.size()));		
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			param.put("WEEK_VOICE_CALL_IN_NUM", CommonUtil.parseInt(row.getColumnValue("VOICE_CALL_IN_NUM")));
			param.put("WEEK_VOICE_CALL_OUT_NUM", CommonUtil.parseInt(row.getColumnValue("VOICE_CALL_OUT_NUM")));			
			param.put("WEEK_VOICE_CALL_NUM", CommonUtil.parseInt(row.getColumnValue("VOICE_CALL_NUM")));		
			param.put("WEEK_MEDIA_CALL_IN_NUM", CommonUtil.parseInt(row.getColumnValue("MEDIA_CALL_IN_NUM")));
			param.put("WEEK_MEDIA_CALL_OUT_NUM", CommonUtil.parseInt(row.getColumnValue("MEDIA_CALL_OUT_NUM")));		
			param.put("WEEK_MEDIA_CALL_NUM", CommonUtil.parseInt(row.getColumnValue("MEDIA_CALL_NUM")));
			map.put(userAcc, param);
		}
		
		// 质检量（本周）
		list = agentDao.countQcByAgent(qcBeginWeek, qcEndWeek);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginWeek + "~" + qcEndWeek + "，查询到坐席的质检量（本周）记录数:" + (list == null ? 0 :list.size()));		
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			int voiceCallInNum = param.get("WEEK_VOICE_CALL_IN_NUM") == null ? 0 : (int)param.get("WEEK_VOICE_CALL_IN_NUM");
			int voiceQcInNum = CommonUtil.parseInt(row.getColumnValue("VOICE_QC_IN_NUM"));
			double voiceListenInRate = calcRate(voiceCallInNum, voiceQcInNum);
			
			int voiceCallOutNum = param.get("WEEK_VOICE_CALL_OUT_NUM") == null ? 0 : (int)param.get("WEEK_VOICE_CALL_OUT_NUM");
			int voiceQcOutNum = CommonUtil.parseInt(row.getColumnValue("VOICE_QC_OUT_NUM"));
			double voiceListenOutRate = calcRate(voiceCallOutNum, voiceQcOutNum);
			
			int voiceCallNum = param.get("WEEK_VOICE_CALL_NUM") == null ? 0 : (int)param.get("WEEK_VOICE_CALL_NUM");
			int voiceQcNum = CommonUtil.parseInt(row.getColumnValue("VOICE_QC_NUM"));
			double voiceListenRate = calcRate(voiceCallNum, voiceQcNum);
			
			int mediaCallInNum = param.get("WEEK_MEDIA_CALL_IN_NUM") == null ? 0 : (int)param.get("WEEK_MEDIA_CALL_IN_NUM");
			int mediaQcInNum = CommonUtil.parseInt(row.getColumnValue("MEDIA_QC_IN_NUM"));
			double mediaListenInRate = calcRate(mediaCallInNum, mediaQcInNum);
			
			int mediaCallOutNum = param.get("WEEK_MEDIA_CALL_OUT_NUM") == null ? 0 : (int)param.get("WEEK_MEDIA_CALL_OUT_NUM");
			int mediaQcOutNum = CommonUtil.parseInt(row.getColumnValue("MEDIA_QC_OUT_NUM"));
			double mediaListenOutRate = calcRate(mediaCallOutNum, mediaQcOutNum);
			
			int mediaCallNum = param.get("WEEK_MEDIA_CALL_NUM") == null ? 0 : (int)param.get("WEEK_MEDIA_CALL_NUM");
			int mediaQcNum = CommonUtil.parseInt(row.getColumnValue("MEDIA_QC_NUM"));
			double mediaListenRate = calcRate(mediaCallNum, mediaQcNum);
			double listenRate = calcRate(voiceCallNum + mediaCallNum, voiceQcNum + mediaQcNum);
					
			param.put("WEEK_VOICE_LISTEN_IN_NUM", voiceQcInNum);
			param.put("WEEK_VOICE_LISTEN_IN_RATE", voiceListenInRate);
			param.put("WEEK_VOICE_LISTEN_OUT_NUM", voiceQcOutNum);
			param.put("WEEK_VOICE_LISTEN_OUT_RATE", voiceListenOutRate);			
			param.put("WEEK_VOICE_LISTEN_NUM", voiceQcNum);
			param.put("WEEK_VOICE_LISTEN_RATE", voiceListenRate);		
			param.put("WEEK_MEDIA_LISTEN_IN_NUM", mediaQcInNum);
			param.put("WEEK_MEDIA_LISTEN_IN_RATE", mediaListenInRate);
			param.put("WEEK_MEDIA_LISTEN_OUT_NUM", mediaQcOutNum);
			param.put("WEEK_MEDIA_LISTEN_OUT_RATE", mediaListenOutRate);					
			param.put("WEEK_MEDIA_LISTEN_NUM", mediaQcNum);
			param.put("WEEK_MEDIA_LISTEN_RATE", mediaListenRate);
			param.put("WEEK_LISTEN_RATE", listenRate);
			map.put(userAcc, param);
		}
		try {	
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_LISTEN_RATE");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席的监听率,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席的监听率,入库时失败", e);
		}
	}
	
	
	/**
	 * 统计全媒体坐席月度绩效，按月生成一条记录，每天更新
	 * @param stDate
	 */
	public void stAgentMediaJX(String stDate) {
		// 质检月开始、结束时间
		String qcBeginDate = stDate.substring(0, 8) + Constants.QC_DATE_OF_MONTH;					
		if(Integer.parseInt(stDate.substring(8, 10)) < Constants.QC_DATE_OF_MONTH)
			qcBeginDate = DateUtil.addMonth(DateUtil.TIME_FORMAT_YMD, qcBeginDate, -1);
		String qcEndDate = DateUtil.addMonth(DateUtil.TIME_FORMAT_YMD, qcBeginDate, 1);
		
		// 删除坐席的汇总记录
		agentDao.delAgentRecord("C_ST_MEDIA_AGENT_MONTH_JX", qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		
		// 值班天数
		List<EasyRow> list = agentDao.stAgentDuty(qcBeginDate, qcEndDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到值班记录数:" + (list == null ? 0 :list.size()));		
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("USER_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			param.put("DUTY_DAYS", CommonUtil.parseInt(row.getColumnValue("DUTY_DAYS")));
			param.put("ACT_DUTY_DAYS", CommonUtil.parseDouble(row.getColumnValue("ACT_DUTY_DAYS")));
			map.put(userAcc, param);
		}
		
		// 全媒体接待量、接待天数、满意量
		list = Optional.ofNullable(agentDao.stAgentReception(qcBeginDate, qcEndDate)).orElse(new ArrayList<>());
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到全媒体接待量、接待天数、满意量记录数:" + list.size());
		for(int i = 0; i < list.size(); i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("AGENT_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			int receptionNum = CommonUtil.parseInt(row.getColumnValue("RECEPTION_NUM"));
			int receptionDays = CommonUtil.parseInt(row.getColumnValue("RECEPTION_DAYS"));
			int satisTotal = CommonUtil.parseInt(row.getColumnValue("SATIS_TOTAL"));
			int satisNum = CommonUtil.parseInt(row.getColumnValue("SATIS_NUM"));
			double avgReceptionNum = calcAvg(receptionDays, receptionNum);
			double receptionAddScore = 0;
			if(receptionNum > receptionDays * 200)
				receptionAddScore = (receptionNum - receptionDays * 200) * 0.01;
			double satisRate = calcRate(satisTotal, satisNum);
				
			param.put("IN_NUM", receptionNum);			
			param.put("RECEPTION_NUM", receptionNum);
			param.put("RECEPTION_DAYS", receptionDays);
			param.put("AVG_RECEPTION_NUM", avgReceptionNum);
			param.put("RECEPTION_ADD_SCORE", receptionAddScore);
			param.put("SATISFACTION", satisRate);		
			map.put(userAcc, param);
		}
		
		// 专项时长
		list = agentDao.stAgentZxLen(qcBeginDate, qcEndDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到专项时长记录数:" + (list == null ? 0 :list.size()));		
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("WORK_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			param.put("ZX_HOURS", CommonUtil.parseDouble(row.getColumnValue("ZX_HOURS")));
			map.put(userAcc, param);
		}
		
		// 全媒体售后工单量
		list = Optional.ofNullable(agentDao.stAgentMediaSh(qcBeginDate, qcEndDate)).orElse(new ArrayList<>());
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到全媒体售后工单记录数:" + list.size());
		for(int i = 0; i < list.size(); i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("AGENT_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			param.put("SH_ORDER_NUM", CommonUtil.parseInt(row.getColumnValue("SH_ORDER_NUM")));				
			map.put(userAcc, param);
		}
		
		// 首次平均响应时长、平均响应时长
		list = Optional.ofNullable(agentDao.stAgentMediaExtend(qcBeginDate, qcEndDate)).orElse(new ArrayList<>());
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到全媒体首次平均响应时长、平均响应时长记录数:" + list.size());
		for(int i = 0; i < list.size(); i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("AGENT_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}	
			param.put("AVG_FIRST_REPLY_LEN", CommonUtil.parseDouble(row.getColumnValue("AVG_FIRST_REPLY")));		
			param.put("AVG_REPLY_LEN", CommonUtil.parseDouble(row.getColumnValue("AVG_REPLY")));		
			map.put(userAcc, param);
		}
		
		// 全媒体质检平均分
		list = Optional.ofNullable(agentDao.stAgentAvgQcScore(qcBeginDate, qcEndDate)).orElse(new ArrayList<>());
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到全媒体质检平均分记录数:" + (list == null ? 0 :list.size()));		
		for(int i = 0; i < list.size(); i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("AGENT_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			param.put("AVG_MANUAL_QC_SCORE", CommonUtil.parseDouble(row.getColumnValue("AVG_MANUAL_QC_SCORE")));			
			map.put(userAcc, param);
		}
		
		// 个人日常加扣分
		list = Optional.ofNullable(agentDao.stAgentPersonScore(qcBeginDate, qcEndDate)).orElse(new ArrayList<>());
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到个人日常加扣分记录数:" + list.size());
		for(int i = 0; i < list.size(); i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("USER_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			param.put("DAILY_ADD_SCORE", CommonUtil.parseDouble(row.getColumnValue("DAILY_ADD_SCORE")));			
			map.put(userAcc, param);
		}
		
		// 团队日常加扣分
		list = Optional.ofNullable(agentDao.stAgentTeamScore(qcBeginDate, qcEndDate)).orElse(new ArrayList<>());
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到团队日常加扣分记录数:" + list.size());
		for(int i = 0; i < list.size(); i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("MONITOR_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			double dailyAddScore = param.get("DAILY_ADD_SCORE") == null ? 0 : (double)param.get("DAILY_ADD_SCORE");
			dailyAddScore += CommonUtil.parseDouble(row.getColumnValue("DAILY_ADD_SCORE"));
			param.put("DAILY_ADD_SCORE", dailyAddScore);			
			map.put(userAcc, param);
		}
		
		// 培训考试成绩
		list = agentDao.stAgentTestScore(qcBeginDate, qcEndDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ qcBeginDate + "~" + qcEndDate + "，查询到培训考试成绩记录数:" + (list == null ? 0 :list.size()));		
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("USER_ACC");		
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, qcEndDate.substring(0, 7), Constants.ST_TYPE_MONTH);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			param.put("AVG_TEST_SCORE", CommonUtil.parseDouble(row.getColumnValue("AVG_TEST_SCORE")));
			map.put(userAcc, param);
		}	
		try {	
			agentDao.map2Save(sqlList, map, "C_ST_MEDIA_AGENT_MONTH_JX");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计全媒体坐席月度绩效,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计全媒体坐席月度绩效,入库时失败", e);
		}
	}
	
	
	/**
	 * 根据会话类型、渠道、呼叫方向统计坐席工作量
	 * @param stDate
	 */	
	public void stAgentQd(String stDate) {
		// 删除坐席按渠道的汇总记录
		agentDao.delAgentRecord("C_ST_AGENT_QD", stDate, Constants.ST_TYPE_DAY );
				
		AgentQdDao agentQdDao = new AgentQdDao();
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		
		// 通话总量、满意度评价、服务时长（语音）
		List<EasyRow> list = agentQdDao.stAgentVoiceSatis(stDate);		
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到通话总量、满意度评价、服务时长（语音）的记录数:" + (list == null ? 0 : list.size()));		
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String direction = row.getColumnValue("DIRECTION");
			String key = userAcc + sessionType + channelId + direction;
						
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}						
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}				
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("DIRECTION", direction);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			int verySatisNum = CommonUtil.parseInt(row.getColumnValue("VERY_SATIS_NUM"));
			int satisNum = CommonUtil.parseInt(row.getColumnValue("SATIS_NUM"));
			int usualNum = CommonUtil.parseInt(row.getColumnValue("USUAL_NUM"));
			int unServiceNum = CommonUtil.parseInt(row.getColumnValue("UNSATIS_SERVICE_NUM"));
			int unResultNum = CommonUtil.parseInt(row.getColumnValue("UNSATIS_RESULT_NUM"));
			//不满意量			
			int unsatisNum = unServiceNum + unResultNum ; 		
		    //满意度
			int satisTotal = verySatisNum + satisNum + usualNum + unsatisNum ;
			double satisRate = calcRate(satisTotal, verySatisNum + satisNum);
			
			param.put("TOTAL", CommonUtil.parseInt(row.getColumnValue("TOTAL")));
			param.put("VERY_SATIS_NUM", verySatisNum);
			param.put("SATIS_NUM", satisNum);
			param.put("USUAL_NUM",usualNum);
			param.put("UNSATIS_SERVICE_NUM",unServiceNum);
			param.put("UNSATIS_RESULT_NUM", unResultNum);
			param.put("UNSATIS_NUM", unsatisNum);
			param.put("SATISFACTION", satisRate);		
			param.put("SERVICE_SECONDS", CommonUtil.parseInt(row.getColumnValue("SERVICE_SECONDS")));
			param.put("AVG_SERVICE_SECONDS", CommonUtil.parseDouble(row.getColumnValue("AVG_SERVICE_SECONDS")));
			map.put(key, param);
		}
		
		// 通话总量、满意量、响应时长、首次响应时长、服务时长记录(全媒体)
		list = agentQdDao.stAgentMediaSatis(stDate);		
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到通话总量、满意量、响应时长、首次响应时长、服务时长记录(全媒体)的记录数:" + (list == null ? 0 : list.size()));	
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String direction = row.getColumnValue("DIRECTION");
			String key = userAcc + sessionType + channelId + direction;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}						
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("DIRECTION", direction);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			int verySatisNum = CommonUtil.parseInt(row.getColumnValue("VERY_SATIS_NUM"));
			int satisNum = CommonUtil.parseInt(row.getColumnValue("SATIS_NUM"));
			int usualNum = CommonUtil.parseInt(row.getColumnValue("USUAL_NUM"));
			int satisTotal = CommonUtil.parseInt(row.getColumnValue("SATIS_TOTAL"));
			//不满意量			
			int unsatisNum = satisTotal - verySatisNum - satisNum - usualNum; 		
		    //满意度
			double satisRate = calcRate(satisTotal, verySatisNum + satisNum);
			
			param.put("TOTAL", CommonUtil.parseInt(row.getColumnValue("TOTAL")));
			param.put("VERY_SATIS_NUM", verySatisNum);
			param.put("SATIS_NUM", satisNum);
			param.put("USUAL_NUM",usualNum);
			param.put("UNSATIS_NUM", unsatisNum);
			param.put("SATISFACTION", satisRate);			
			param.put("REPLY_SECONDS", CommonUtil.parseDouble(row.getColumnValue("REPLY_SECONDS")));
			param.put("AVG_REPLY_SECONDS", CommonUtil.parseDouble(row.getColumnValue("AVG_REPLY_SECONDS")));
			param.put("FIRST_REPLY_SECONDS", CommonUtil.parseInt(row.getColumnValue("FIRST_REPLY_SECONDS")));
			param.put("AVG_FIRST_REPLY_SECONDS", CommonUtil.parseDouble(row.getColumnValue("AVG_FIRST_REPLY_SECONDS")));
			param.put("SERVICE_SECONDS", CommonUtil.parseInt(row.getColumnValue("SERVICE_SECONDS")));
			param.put("AVG_SERVICE_SECONDS", CommonUtil.parseDouble(row.getColumnValue("AVG_SERVICE_SECONDS")));		
			map.put(key, param);
		}
				
		// 人工质检记录
		list = agentQdDao.stAgentQdManualQc(stDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的人工质检记录数:" + (list == null ? 0 : list.size()));	
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String direction = row.getColumnValue("DIRECTION");
			String key = userAcc + sessionType + channelId + direction;
					
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("DIRECTION", direction);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}			
			int manualQcNum = CommonUtil.parseInt(row.getColumnValue("MANUAL_QC_NUM"));
			int manualZeroNum = CommonUtil.parseInt(row.getColumnValue("MANUAL_ZERO_NUM"));
			double manualQcScore = CommonUtil.parseDouble(row.getColumnValue("MANUAL_QC_TOTAL_SCORE"));
			double manualQcAvgScore = calcAvg(manualQcNum - manualZeroNum, manualQcScore);		
			int keyErrorNum = CommonUtil.parseInt(row.getColumnValue("KEY_ERROR_NUM"));
			int serErrorNum = CommonUtil.parseInt(row.getColumnValue("SERVICE_ERROR_NUM"));
			int notSerErrorNum = keyErrorNum - serErrorNum;
			
			param.put("MANUAL_QC_NUM", manualQcNum);
			param.put("MANUAL_ZERO_NUM", manualZeroNum);
			param.put("MANUAL_QC_QUAL_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_QC_QUAL_NUM")));
			param.put("MANUAL_QC_TOTAL_SCORE", manualQcScore);
			param.put("MANUAL_QC_AVG_SCORE", manualQcAvgScore);
			param.put("KEY_ERROR_NUM", keyErrorNum);
			param.put("SERVICE_ERROR_NUM", serErrorNum);
			param.put("NOTSERVICE_ERROR_NUM", notSerErrorNum);
			map.put(key, param);
		}
		
		// 自动质检记录
		list = agentQdDao.stAgentQdAutoQc(stDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的自动质检记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String direction = row.getColumnValue("DIRECTION");
			String key = userAcc + sessionType + channelId + direction;
					
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("DIRECTION", direction);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}	
			param.put("AUTO_QC_NUM", CommonUtil.parseInt(row.getColumnValue("AUTO_QC_NUM")));
			param.put("AUTO_QC_TOTAL_SCORE", CommonUtil.parseDouble(row.getColumnValue("AUTO_QC_TOTAL_SCORE")));
			param.put("AUTO_QC_AVG_SCORE", CommonUtil.parseDouble(row.getColumnValue("AUTO_QC_AVG_SCORE")));
			map.put(key, param);
		}
		
		// VOC提交量、VOC通过量
		list = agentQdDao.stAgentQdVoc(stDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到VOC提交量、VOC通过量的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String direction = row.getColumnValue("DIRECTION");
			String key = userAcc + sessionType + channelId + direction;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("DIRECTION", direction);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}				
			param.put("VOC_APPLY_NUM", CommonUtil.parseInt(row.getColumnValue("VOC_APPLY_NUM")));
			param.put("VOC_AUDIT_PASS_NUM", CommonUtil.parseInt(row.getColumnValue("VOC_AUDIT_PASS_NUM")));
			map.put(key, param);
		}
		
		// 处理留言数（语音）
		list = agentQdDao.stAgentQdVoiceMess(stDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到处理留言数（语音）的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("HAND_USER_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String direction = row.getColumnValue("DIRECTION");
			String key = userAcc + sessionType + channelId + direction;
			
			int leaveNum = CommonUtil.parseInt(row.getColumnValue("LEAVE_MSG_NUM"));
			if(leaveNum == 0)
				continue;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("DIRECTION", direction);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}	
			param.put("LEAVE_MSG_NUM", leaveNum);
			map.put(key, param);
		}
		
		// 处理留言数（全媒体）
		list = agentQdDao.stAgentQdMediaMess(stDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到处理留言数（全媒体）的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("USER_ID");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String direction = row.getColumnValue("DIRECTION");
			String key = userAcc + sessionType + channelId + direction;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("DIRECTION", direction);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}	
			param.put("LEAVE_MSG_NUM", CommonUtil.parseInt(row.getColumnValue("LEAVE_MSG_NUM")));
			map.put(key, param);
		}
		try {	
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_QD");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "根据会话类型、渠道、呼叫方向统计坐席工作量,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据会话类型、渠道、呼叫方向统计坐席工作量,入库时失败", e);
		}
	}
    
	/**
	 * 根据统计时间类型统计坐席满意度细项评价
	 * @param stType
	 * @param beginTime
	 * @param endTime
	 */
	public void stAgentSatisDetail(String stType, String beginTime, String endTime){
		String tempTime = beginTime.substring(0, 16);
		if(Constants.ST_TYPE_DAY.equals(stType))
			tempTime = beginTime.substring(0, 10);
		// 删除坐席满意度细项评价的汇总记录
        agentDao.delAgentRecord("C_ST_AGENT_SATIS_DETAIL", tempTime, stType);
		AgentSkillDao agentSkillDao = new AgentSkillDao();
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		//坐席满意度细项评价
		List<EasyRow> list = agentSkillDao.stAgentSatisDetail(beginTime, endTime);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + beginTime + "~" + endTime 
				+ "，查询坐席满意度细项评价的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String skillCode = row.getColumnValue("GROUP_ID");
			String satisfName = row.getColumnValue("SATISF_NAME");
			String key = userAcc + sessionType + channelId + skillCode + satisfName;
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}else{
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("SKILL_NO", skillCode);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());	
				param.put("SATIS_DETAIL_NAME",satisfName);
			
			}
			param.put("TOTAL", CommonUtil.parseInt(row.getColumnValue("TOTAL")));
			param.put("SATIS_STAR_01_NUM", CommonUtil.parseInt(row.getColumnValue("SATIS_STAR_01_NUM")));
			param.put("SATIS_STAR_02_NUM", CommonUtil.parseInt(row.getColumnValue("SATIS_STAR_02_NUM")));
			param.put("SATIS_STAR_03_NUM", CommonUtil.parseInt(row.getColumnValue("SATIS_STAR_03_NUM")));
			param.put("SATIS_STAR_04_NUM", CommonUtil.parseInt(row.getColumnValue("SATIS_STAR_04_NUM")));
			param.put("SATIS_STAR_05_NUM", CommonUtil.parseInt(row.getColumnValue("SATIS_STAR_05_NUM")));
			map.put(key, param);
		}
		try {	
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_SATIS_DETAIL");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席满意度细项评价记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席满意度细项评价记录数,入库时失败", e);
		}
	}
	
	/**
	 * 根据会话类型、渠道、技能组统计坐席工作量
	 * @param stType
	 * @param beginTime
	 * @param endTime
	 */
	public void stAgentSkill(String stType, String beginTime, String endTime) {
		String tempTime = beginTime.substring(0, 16);
		if(Constants.ST_TYPE_DAY.equals(stType))
			tempTime = beginTime.substring(0, 10);
		
		// 删除坐席按技能组的汇总记录
		agentDao.delAgentRecord("C_ST_AGENT_SKILL", tempTime, stType);
		
		AgentSkillDao agentSkillDao = new AgentSkillDao();
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		
		// 接通量（语音）
		List<EasyRow> list = agentSkillDao.stAgentSkillCallVoice(beginTime, endTime);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + beginTime + "~" + endTime 
				+ "，查询到接通量（语音）的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String skillCode = row.getColumnValue("SKILL_CODE");
			String key = userAcc + sessionType + channelId + skillCode;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("SKILL_NO", skillCode);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}			
			param.put("TOTAL", CommonUtil.parseInt(row.getColumnValue("TOTAL")));
			map.put(key, param);
		}
		
		// 接通量、客户量（全媒体）
		list = agentSkillDao.stAgentSkillCallMedia(beginTime, endTime);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + beginTime + "~" + endTime 
				+ "，查询到接通量、客户量（全媒体）的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String skillCode = row.getColumnValue("GROUP_ID");
			String key = userAcc + sessionType + channelId + skillCode;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("SKILL_NO", skillCode);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			int total = CommonUtil.parseInt(row.getColumnValue("TOTAL"));
			int custNum = CommonUtil.parseInt(row.getColumnValue("CUST_NUM"));
			int repeatNum = total - custNum;
			
			param.put("TOTAL", total);
			param.put("REPEAT_NUM", repeatNum);
			map.put(key, param);
		}				
		
		// 人工考评量、人工考评平均分
		list = agentSkillDao.stAgentSkillManualQc(beginTime, endTime);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + beginTime + "~" + endTime 
				+ "，查询到人工考评量、人工考评平均分的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String skillCode = row.getColumnValue("SKILL_CODE");
			String key = userAcc + sessionType + channelId + skillCode;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("SKILL_NO", skillCode);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			int manualQcNum = CommonUtil.parseInt(row.getColumnValue("MANUAL_QC_NUM"));
			int manualZeroNum = CommonUtil.parseInt(row.getColumnValue("MANUAL_ZERO_NUM"));
			double manualQcScore = CommonUtil.parseDouble(row.getColumnValue("MANUAL_QC_SCORE"));
			double manualQcAvgScore = calcAvg(manualQcNum - manualZeroNum, manualQcScore);		
			
			param.put("MANUAL_QC_NUM", manualQcNum);
			param.put("MANUAL_ZERO_NUM", manualZeroNum);
			param.put("MANUAL_QC_SCORE", manualQcScore);
			param.put("MANUAL_QC_AVG_SCORE", manualQcAvgScore);
			map.put(key, param);
		}	
		
		// 自动考评量、自动考评分
		list = agentSkillDao.stAgentSkillAutoQc(beginTime, endTime);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + beginTime + "~" + endTime 
				+ "，查询到自动考评量、自动考评分的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String skillCode = row.getColumnValue("SKILL_CODE");
			String key = userAcc + sessionType + channelId + skillCode;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("SKILL_NO", skillCode);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			param.put("AUTO_QC_NUM", CommonUtil.parseInt(row.getColumnValue("AUTO_QC_NUM")));
			param.put("AUTO_QC_SCORE", CommonUtil.parseDouble(row.getColumnValue("AUTO_QC_SCORE")));
			param.put("AUTO_QC_AVG_SCORE", CommonUtil.parseDouble(row.getColumnValue("AUTO_QC_AVG_SCORE")));
			map.put(key, param);
		}
				
		// 全媒体的扩展属性
		list = agentSkillDao.stAgentSkillExtend(beginTime, endTime);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + beginTime + "~" + endTime 
				+ "，查询到全媒体的扩展属性的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String skillCode = row.getColumnValue("GROUP_ID");
			String key = userAcc + sessionType + channelId + skillCode;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("SKILL_NO", skillCode);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			int verySatisNum = CommonUtil.parseInt(row.getColumnValue("VERY_SATIS_NUM"));
			int satisNum = CommonUtil.parseInt(row.getColumnValue("SATIS_NUM"));
			int usualNum = CommonUtil.parseInt(row.getColumnValue("USUAL_NUM"));
			int evalNum = CommonUtil.parseInt(row.getColumnValue("EVAL_NUM"));			
			int unsatisNum = evalNum - verySatisNum - satisNum - usualNum; 		
			
			int custMsgNum = CommonUtil.parseInt(row.getColumnValue("CUST_MSG_NUM"));
			int agentMsgNum = CommonUtil.parseInt(row.getColumnValue("AGENT_MSG_NUM"));
			double msgRate = calcRate(custMsgNum, agentMsgNum);
			
			int thirtyReplyNum = CommonUtil.parseInt(row.getColumnValue("THIRTY_REPLY_NUM"));
			int totalReply = CommonUtil.parseInt(row.getColumnValue("TOTAL_REPLY"));
			double thirtyReplyRate = calcRate(totalReply, thirtyReplyNum);
			
			int slowReplyNum = CommonUtil.parseInt(row.getColumnValue("SLOW_REPLY_NUM"));
			int total = param.get("TOTAL") == null ? 0 : (int)param.get("TOTAL");
			double slowReplyRate = calcRate(total, slowReplyNum);
			
			param.put("TRANS_OUT_NUM", CommonUtil.parseInt(row.getColumnValue("TRANS_OUT_NUM")));
			param.put("TRANS_IN_NUM", CommonUtil.parseInt(row.getColumnValue("TRANS_IN_NUM")));
			param.put("WAIT_LEN", CommonUtil.parseInt(row.getColumnValue("WAIT_LEN")));
			param.put("AVG_WAIT_LEN", CommonUtil.parseDouble(row.getColumnValue("AVG_WAIT_LEN")));
			param.put("MAX_WAIT_LEN", CommonUtil.parseInt(row.getColumnValue("MAX_WAIT_LEN")));
			param.put("SERV_LEN", CommonUtil.parseInt(row.getColumnValue("SERV_LEN")));
			param.put("AVG_SERV_LEN", CommonUtil.parseDouble(row.getColumnValue("AVG_SERV_LEN")));
			param.put("MAX_SERV_LEN", CommonUtil.parseInt(row.getColumnValue("MAX_SERV_LEN")));			
			param.put("FIRST_REPLY_LEN", CommonUtil.parseInt(row.getColumnValue("FIRST_REPLY_LEN")));
			param.put("AVG_FIRST_REPLY_LEN", CommonUtil.parseDouble(row.getColumnValue("AVG_FIRST_REPLY_LEN")));		
			param.put("REPLY_NUM", CommonUtil.parseDouble(row.getColumnValue("REPLY_NUM")));
			param.put("AVG_REPLY_NUM", CommonUtil.parseDouble(row.getColumnValue("AVG_REPLY_NUM")));
			param.put("SH_ORDER_NUM", CommonUtil.parseInt(row.getColumnValue("SH_ORDER_NUM")));
			param.put("ZX_ORDER_NUM", CommonUtil.parseInt(row.getColumnValue("ZX_ORDER_NUM")));
			param.put("VERY_SATIS_NUM", verySatisNum);
			param.put("SATIS_NUM", satisNum);
			param.put("USUAL_NUM", usualNum);		
			param.put("UNSATIS_NUM", unsatisNum);
			param.put("EVAL_NUM", evalNum);
			param.put("CUST_MSG_NUM", custMsgNum);
			param.put("AGENT_MSG_NUM", agentMsgNum);
			param.put("MSG_PERCENT", msgRate);				
			param.put("THIRTY_REPLY_NUM", thirtyReplyNum);
			param.put("TOTAL_REPLY", totalReply);
			param.put("THIRTY_REPLY_RATE", thirtyReplyRate);
			param.put("SLOW_REPLY_NUM", slowReplyNum);
			param.put("SLOW_REPLY_RATE", slowReplyRate);		
			map.put(key, param);
		}	
		
		// 处理留言数（全媒体）
		list = agentSkillDao.stAgentSkillMess(beginTime, endTime);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + beginTime + "~" + endTime 
				+ "，查询到处理留言数（全媒体）的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("USER_ID");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_KEY");	
			String skillCode = row.getColumnValue("GROUP_ID");
			String key = userAcc + sessionType + channelId + skillCode;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("SKILL_NO", skillCode);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			param.put("HAND_LMSG_NUM", CommonUtil.parseInt(row.getColumnValue("HAND_LMSG_NUM")));
			map.put(key, param);
		}	
		
		// 超三分钟未回复量（全媒体）
		list = agentSkillDao.stAgentSkillOver(beginTime, endTime);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + beginTime + "~" + endTime 
				+ "，查询到超三分钟未回复量（全媒体）的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String skillCode = row.getColumnValue("GROUP_ID");
			String key = userAcc + sessionType + channelId + skillCode;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("SKILL_NO", skillCode);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			param.put("OVER_MINUTES_NUM", CommonUtil.parseInt(row.getColumnValue("OVER_MINUTES_NUM")));
			map.put(key, param);
		}	
		
		//视频客服服务时长（全媒体）
		list = agentSkillDao.stAgentSkillVideo(beginTime, endTime);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + beginTime + "~" + endTime 
				+ "，视频客服服务时长（全媒体）的记录数:" + (list == null ? 0 : list.size()));

		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String skillCode = row.getColumnValue("GROUP_ID");
			String key = userAcc + sessionType + channelId + skillCode;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("SKILL_NO", skillCode);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			param.put("VIDEO_SERV_LEN", CommonUtil.parseInt(row.getColumnValue("VIDEO_SERV_LEN")));  //视频服务总时长
			param.put("AVG_VIDEO_SERV_LEN", CommonUtil.parseInt(row.getColumnValue("AVG_VIDEO_SERV_LEN")));  //平均视频服务时长
			param.put("MAX_VIDEO_SERV_LEN", CommonUtil.parseInt(row.getColumnValue("MAX_VIDEO_SERV_LEN")));  //最大视频服务时长
			map.put(key, param);
		}	
		//视频客服满意度
		list = agentSkillDao.stAgentSkillVideoSati(beginTime, endTime);
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String channelId = row.getColumnValue("CHANNEL_ID");	
			String skillCode = row.getColumnValue("GROUP_ID");
			String key = userAcc + sessionType + channelId + skillCode;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, tempTime, stType);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("SESSION_TYPE", sessionType);
				param.put("CHANNEL_NO", channelId);
				param.put("SKILL_NO", skillCode);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			int verySatisNum = CommonUtil.parseInt(row.getColumnValue("VERY_SATIS_NUM"));
			int satisNum = CommonUtil.parseInt(row.getColumnValue("SATIS_NUM"));
			int usualNum = CommonUtil.parseInt(row.getColumnValue("USUAL_NUM"));
			int evalNum = CommonUtil.parseInt(row.getColumnValue("EVAL_NUM"));
			int sessionNum = CommonUtil.parseInt(row.getColumnValue("SESSION_NUM"));
			int unsatisNum = evalNum - verySatisNum - satisNum - usualNum; 
			param.put("VIDEO_VERY_SATIS_NUM", verySatisNum);
			param.put("VIDEO_SATIS_NUM", satisNum);
			param.put("VIDEO_USUAL_NUM", usualNum);		
			param.put("VIDEO_UNSATIS_NUM", unsatisNum);
			param.put("VIDEO_EVAL_NUM", evalNum);
			param.put("VIDEO_SESSION_NUM", sessionNum);
			map.put(key, param);
		}
		try {	
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_SKILL");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "根据会话类型、渠道、技能组统计坐席工作量,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据会话类型、渠道、技能组统计坐席工作量,入库时失败", e);
		}
	}

	
	/**
	 * 根据会话类型、渠道、时间段统计坐席工作量
	 * @param stDate
	 */
	public void stAgentTime(String stDate) {
		// 删除坐席按时间段的汇总记录
		agentDao.delAgentRecord("C_ST_AGENT_TIME", stDate, Constants.ST_TYPE_DAY );
		
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
				
		HashMap<String, String> timeMap = new HashMap<String, String>();
		timeMap.put("01", "22:00-07:29");
		timeMap.put("02", "07:30-08:59");
		timeMap.put("03", "09:00-10:59");
		timeMap.put("04", "11:00-12:59");
		timeMap.put("05", "13:00-14:59");
		timeMap.put("06", "15:00-16:59");
		timeMap.put("07", "17:00-18:59");
		timeMap.put("08", "19:00-21:59");
		
		String stBeginTime = "", stEndTime = "";
		for (String num : timeMap.keySet()) {
			String[] timeStr = timeMap.get(num).split("-");
			if (num.equals("01")) {
				stBeginTime = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, stDate, -1) + " " + timeStr[0] + ":00";
			}
			else {
				stBeginTime = stDate + " " + timeStr[0] + ":00";
			}		
			stEndTime = stDate + " " + timeStr[1] + ":59";
			
			String timeRange = timeMap.get(num);
			// 话务量明细
			List<EasyRow> list =  agentDao.stAgentTimeByCall(stBeginTime, stEndTime);	
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
					+ "，查询到话务量明细的记录数:" + (list == null ? 0 : list.size()));
			for (int i=0;list!=null&&i<list.size();i++) {	
				EasyRow row = list.get(i);		
		    	String userAcc = row.getColumnValue("AGENT_ACC");
		    	String sessionType = row.getColumnValue("SESSION_TYPE");
		    	String channelId = row.getColumnValue("CHANNEL_ID");	
				String direction = row.getColumnValue("DIRECTION");
				String type = "01";
		    	String key = userAcc + sessionType + channelId + direction + timeRange + type;
		    	
				Map<String, Object> param = new HashMap<String, Object>();		
				if (map.containsKey(key)) {
					param = map.get(key);
				}
				else {
					String stAgentId = stIds.get(userAcc);
					if(StringUtils.isBlank(stAgentId)) {
						stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
						stIds.put(userAcc, stAgentId);
					}	
					if(StringUtils.isBlank(stAgentId)) {
						stAgentId = IDGenerator.getDefaultNUMID();
						Agent agent = agentsAcc.get(userAcc);
						if(agent == null)
							continue;				
						stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
						stIds.put(userAcc, stAgentId);
					}	
			    	param.put("ID", IDGenerator.getDefaultNUMID());
			    	param.put("ST_AGENT_ID", stAgentId);
			    	param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			    	param.put("SESSION_TYPE", sessionType);
			    	param.put("CHANNEL_NO", channelId);
			    	param.put("DIRECTION", direction);
			    	param.put("TYPE", type);
			    	param.put("TIME_FLAG", timeRange);
				}			    	
		    	param.put("LESS_ONE_MINUTE", CommonUtil.parseInt(row.getColumnValue("LESS_ONE_MINUTE")));
		    	param.put("ONE_MINUTE", CommonUtil.parseInt(row.getColumnValue("ONE_MINUTE")));
		    	param.put("TWO_MINUTE", CommonUtil.parseInt(row.getColumnValue("TWO_MINUTE")));
		    	param.put("THREE_MINUTE", CommonUtil.parseInt(row.getColumnValue("THREE_MINUTE")));
		    	param.put("FOUR_MINUTE", CommonUtil.parseInt(row.getColumnValue("FOUR_MINUTE")));
		    	map.put(key, param);
			}
		    
			// 被监听明细
		    list = agentDao.stAgentTimeByQc(stBeginTime, stEndTime);				   
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
					+ "，查询到被监听明细的记录数:" + (list == null ? 0 : list.size()));
			for (int i=0;list!=null&&i<list.size();i++) {	
				EasyRow row = list.get(i);			
		    	String userAcc = row.getColumnValue("AGENT_ACC");
		    	String sessionType = row.getColumnValue("SESSION_TYPE");
		    	String channelId = row.getColumnValue("CHANNEL_ID");	
				String direction = row.getColumnValue("DIRECTION");
				String type = "02";
		    	String key = userAcc + sessionType + channelId + direction + timeRange + type;
		    	
				Map<String, Object> param = new HashMap<String, Object>();		
				if (map.containsKey(key)) {
					param = map.get(key);
				}
				else {
					String stAgentId = stIds.get(userAcc);
					if(StringUtils.isBlank(stAgentId)) {
						stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
						stIds.put(userAcc, stAgentId);
					}
					if(StringUtils.isBlank(stAgentId)) {
						stAgentId = IDGenerator.getDefaultNUMID();
						Agent agent = agentsAcc.get(userAcc);
						if(agent == null)
							continue;				
						stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
						stIds.put(userAcc, stAgentId);
					}	
			    	param.put("ID", IDGenerator.getDefaultNUMID());
			    	param.put("ST_AGENT_ID", stAgentId);
			    	param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			    	param.put("SESSION_TYPE", sessionType);
			    	param.put("CHANNEL_NO", channelId);
			    	param.put("DIRECTION", direction);
			    	param.put("TYPE", type);
			    	param.put("TIME_FLAG", timeRange);
				}	
		    	param.put("LESS_ONE_MINUTE", CommonUtil.parseInt(row.getColumnValue("LESS_ONE_MINUTE")));
		    	param.put("ONE_MINUTE", CommonUtil.parseInt(row.getColumnValue("ONE_MINUTE")));
		    	param.put("TWO_MINUTE", CommonUtil.parseInt(row.getColumnValue("TWO_MINUTE")));
		    	param.put("THREE_MINUTE", CommonUtil.parseInt(row.getColumnValue("THREE_MINUTE")));
		    	param.put("FOUR_MINUTE", CommonUtil.parseInt(row.getColumnValue("FOUR_MINUTE")));
		    	map.put(key, param);
			}			
		}
		try {	
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_TIME");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "根据会话类型、渠道、时间段统计坐席工作量,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "根据会话类型、渠道、时间段统计坐席工作量,入库时失败", e);
		}
	}

	
	/**
	 * 统计坐席的申诉记录
	 * @param stDate
	 */
	public void stAgentComplaint(String stDate) {	
		// 删除坐席按技能组的汇总记录
		agentDao.delAgentRecord("C_ST_AGENT_COMPLAINT", stDate, Constants.ST_TYPE_DAY );
		
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		
		// 坐席人工质检量
		List<EasyRow> list = agentDao.stAgentQc(stDate);	
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到坐席人工质检的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String key = userAcc+ sessionType;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
				param.put("SESSION_TYPE", sessionType);
			}	
			param.put("TOTAL_NUM", row.getColumnValue("QC_NUM"));
			map.put(key, param);
		}
		
		// 坐席申诉量
		list = agentDao.stAgentComplaint(stDate);	
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到坐席申诉的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String key = userAcc+ sessionType;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
				param.put("SESSION_TYPE", sessionType);
			}
			param.put("COMPLAINT_NUM", CommonUtil.parseInt(row.getColumnValue("COMPLAINT_NUM")));
			param.put("FIRST_COMPLAINT_NUM", CommonUtil.parseInt(row.getColumnValue("FIRST_COMPLAINT_NUM")));
			param.put("SECOND_COMPLAINT_NUM", CommonUtil.parseInt(row.getColumnValue("SECOND_COMPLAINT_NUM")));
			param.put("FIRST_WAIT_NUM", CommonUtil.parseInt(row.getColumnValue("FIRST_WAIT_NUM")));
			param.put("SECOND_WAIT_NUM", CommonUtil.parseInt(row.getColumnValue("SECOND_WAIT_NUM")));
			map.put(key, param);
		}
		
		// 坐席申诉结果
		list = agentDao.stAgentComResult(stDate);	
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到坐席申诉结果的记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String sessionType = row.getColumnValue("SESSION_TYPE");
			String key = userAcc+ sessionType;
			
			Map<String, Object> param = new HashMap<String, Object>();		
			if (map.containsKey(key)) {
				param = map.get(key);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
				param.put("SESSION_TYPE", sessionType);
			}
			param.put("FIRST_APPROVED_NUM", CommonUtil.parseInt(row.getColumnValue("FIRST_APPROVED_NUM")));
			param.put("FIRST_REJECTED_NUM", CommonUtil.parseInt(row.getColumnValue("FIRST_REJECTED_NUM")));
			param.put("SECOND_APPROVED_NUM", CommonUtil.parseInt(row.getColumnValue("SECOND_APPROVED_NUM")));
			param.put("SECOND_REJECTED_NUM", CommonUtil.parseInt(row.getColumnValue("SECOND_REJECTED_NUM")));
			map.put(key, param);
		}
		try {	
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_COMPLAINT");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席的申诉记录,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席的申诉记录,入库时失败", e);
		}
	}
	
	
	/**
	 * 按号码统计坐席外呼数量
	 * @param stDate
	 */
	public void stAgentCallOutNum(String stDate) {		
		// 坐席按号码的外呼数量汇总记录
		agentDao.delAgentRecord("C_ST_AGENT_CALLOUT_PHONE", stDate, Constants.ST_TYPE_DAY );
		
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		
		List<EasyRow> list = agentDao.stAgentCallOutPhoneNum(stDate);	
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到坐席外呼的记录数:" + (list == null ? 0 : list.size()));		
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			String called = row.getColumnValue("CALLED");
			String key = userAcc + called;
			
			String stAgentId = stIds.get(userAcc);
			if(StringUtils.isBlank(stAgentId)) {
				stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
				stIds.put(userAcc, stAgentId);
			}
			if(StringUtils.isBlank(stAgentId)) {
				stAgentId = IDGenerator.getDefaultNUMID();
				Agent agent = agentsAcc.get(userAcc);
				if(agent == null)
					continue;				
				stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
				stIds.put(userAcc, stAgentId);
			}	
			
			Map<String, Object> param = new HashMap<String, Object>();
			param.put("ID", IDGenerator.getDefaultNUMID());
			param.put("ST_AGENT_ID", stAgentId);
			param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			param.put("PHONENUM", called);
			param.put("CALL_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_NUM")));
			map.put(key, param);	
			
			if(map.size() >= 2000) {			
				try {
					agentDao.map2Save(sqlList, map, "C_ST_AGENT_CALLOUT_PHONE");
					logger.info(CommonUtil.getClassNameAndMethod(this) + "按号码统计坐席外呼数量,入库记录数:" + sqlList.size());
					agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);
					
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + "按号码统计坐席外呼数量,入库时失败", e);
				}
				sqlList.clear();
				map.clear();
			}
		}
		try {	
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_CALLOUT_PHONE");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "按号码统计坐席外呼数量,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "按号码统计坐席外呼数量,入库时失败", e);
		}
	}

	
	/**
	 * 统计坐席的专项记录 
	 * @param stDate
	 */
	public void stAgentZx(String stDate) {
		
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		
		// 专项记录
		List<EasyRow> list = agentDao.stAgentZx(stDate);				
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的专项记录数:" + (list == null ? 0 : list.size()));	
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);		
			String userAcc = row.getColumnValue("WORK_ACC");			
					
			Map<String, Object> param = new HashMap<String, Object>();
			if(map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				if(StringUtils.isBlank(stAgentId)) {		
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			param.put("SEND_NUM", CommonUtil.parseInt(row.getColumnValue("SEND_NUM")));
			param.put("WAIT_RECEIVE_NUM", CommonUtil.parseInt(row.getColumnValue("WAIT_RECEIVE_NUM")));
			param.put("RECEIVED_NUM", CommonUtil.parseInt(row.getColumnValue("RECEIVED_NUM")));
			param.put("DONE_NUM", CommonUtil.parseInt(row.getColumnValue("DONE_NUM")));
			param.put("NOT_DONE_NUM", CommonUtil.parseInt(row.getColumnValue("NOT_DONE_NUM")));
			param.put("FEEDBACK_NUM", CommonUtil.parseInt(row.getColumnValue("FEEDBACK_NUM")));
			param.put("ZX_LONG", CommonUtil.parseDouble(row.getColumnValue("ZX_LONG")));
			param.put("ZX_TRAIN_LEN", CommonUtil.parseDouble(row.getColumnValue("ZX_TRAIN_LEN")));
			param.put("ZX_DETAIL", row.getColumnValue("ZX_DETAIL"));
			param.put("ZX_BACKUP", row.getColumnValue("ZX_BACKUP"));
			map.put(userAcc, param);
		}
		
		// 特殊工作量
		list = agentDao.stAgentSpZsNum(stDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate
				+ "，查询到的特殊工作量记录数:" + (list == null ? 0 : list.size()));

		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);			
			String userAcc = row.getColumnValue("USER_ACC");						
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}		
				if(StringUtils.isBlank(stAgentId)) {					
					stAgentId = IDGenerator.getDefaultNUMID();	
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			param.put("SP_ZS_NUM", CommonUtil.parseDouble(row.getColumnValue("SP_ZS_NUM")));
			param.put("MANUAL_ZS_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_NUM")));
			param.put("MANUAL_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_NUM")));
			param.put("MANUAL_ZS_05_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_05_NUM")));
			param.put("MANUAL_ZS_075_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_075_NUM")));
			param.put("MANUAL_ZS_08_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_08_NUM")));
			param.put("MANUAL_ZS_085_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_085_NUM")));
			param.put("MANUAL_ZS_09_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_09_NUM")));
			param.put("MANUAL_ZS_095_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_095_NUM")));
			param.put("MANUAL_ZS_10_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_10_NUM")));
			param.put("MANUAL_ZS_11_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_11_NUM")));
			param.put("MANUAL_ZS_12_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_12_NUM")));
			param.put("MANUAL_ZS_13_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_13_NUM")));
			param.put("MANUAL_ZS_14_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_14_NUM")));
			param.put("MANUAL_ZS_15_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_15_NUM")));
			param.put("MANUAL_ZS_16_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_16_NUM")));
			param.put("MANUAL_ZS_17_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_17_NUM")));
			param.put("MANUAL_ZS_18_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_18_NUM")));
			param.put("MANUAL_ZS_20_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_20_NUM")));
			param.put("MANUAL_ZS_22_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_22_NUM")));
			param.put("MANUAL_ZS_23_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_23_NUM")));
			param.put("MANUAL_ZS_25_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_25_NUM")));
			param.put("MANUAL_ZS_27_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_27_NUM")));
			param.put("MANUAL_ZS_30_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_30_NUM")));
			param.put("MANUAL_ZS_35_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_35_NUM")));
			param.put("MANUAL_ZS_40_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_40_NUM")));
			param.put("MANUAL_ZS_45_NUM", CommonUtil.parseDouble(row.getColumnValue("MANUAL_ZS_45_NUM")));
			map.put(userAcc, param);
		}

		//特殊工作量指标累加上命令单折算量
		list = agentDao.stAgentComOrdNum(stDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate
				+ "，查询到的命令单折算量记录数:" + JSON.toJSONString(list.stream().map(EasyRow::toJSONObject).collect(Collectors.toList())));
		for (int i=0;list!=null&&i<list.size();i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("USER_ACC");

			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}
			Double spZsNum = (Double) param.get("SP_ZS_NUM");
			spZsNum = spZsNum == null ? 0 : spZsNum;
			spZsNum += CommonUtil.parseDouble(row.getColumnValue("COM_ORD_NUM"));
			param.put("SP_ZS_NUM", spZsNum);
			map.put(userAcc, param);
		}

		try {	
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_ZX");
			// 删除坐席的汇总记录
			agentDao.delAgentRecord("C_ST_AGENT_ZX", stDate, Constants.ST_TYPE_DAY );
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席的专项记录,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席的专项记录,入库时失败", e);
		}
	}
	
	
	/**
	 * 统计坐席其他工作量
	 * @param stDate
	 */
	public void stAgentOther(String stDate) {		
		// 删除坐席的汇总记录
		agentDao.delAgentRecord("C_ST_AGENT_OTHER", stDate, Constants.ST_TYPE_DAY );
				
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
				
		// 工单量
		List<EasyRow> list = agentDao.stAgentOrderCheck(stDate);				
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的工单量记录数:" + (list == null ? 0 : list.size()));				
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);								
			String userAcc = row.getColumnValue("AGENT_ACC");			
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}		
				if(StringUtils.isBlank(stAgentId)) {					
					stAgentId = IDGenerator.getDefaultNUMID();	
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}			
			param.put("ORDER_CHECK_NUM", CommonUtil.parseInt(row.getColumnValue("ORDER_CHECK_NUM")));
			map.put(userAcc, param);
		}
		
		// 个人加扣分
		list = agentDao.stAgentPersonScore(stDate);				
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的个人加扣分记录数:" + (list == null ? 0 : list.size()));				
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);								
			String userAcc = row.getColumnValue("USER_ACC");			
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}				
				if(StringUtils.isBlank(stAgentId)) {					
					stAgentId = IDGenerator.getDefaultNUMID();	
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}			
			param.put("PLUS_POINTS", CommonUtil.parseDouble(row.getColumnValue("PLUS_POINTS")));
			param.put("DEDUCT_POINTS", CommonUtil.parseDouble(row.getColumnValue("DEDUCT_POINTS")));
			map.put(userAcc, param);
		}
		
		// 团队加扣分
		list = agentDao.stAgentTeamScore(stDate);				
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的团队加扣分记录数:" + (list == null ? 0 : list.size()));				
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);								
			String userAcc = row.getColumnValue("MONITOR_ACC");			
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}			
				if(StringUtils.isBlank(stAgentId)) {					
					stAgentId = IDGenerator.getDefaultNUMID();	
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}		
			double personPlus = param.get("PLUS_POINTS") == null ? 0 : (double)param.get("PLUS_POINTS");
			double personDeduct = param.get("DEDUCT_POINTS") == null ? 0 : (double)param.get("DEDUCT_POINTS");		
			double teamPlus = CommonUtil.parseDouble(row.getColumnValue("PLUS_POINTS"));
			double teamDeduct = CommonUtil.parseDouble(row.getColumnValue("DEDUCT_POINTS"));
			
			param.put("PLUS_POINTS", personPlus + teamPlus);
			param.put("DEDUCT_POINTS", -(personDeduct + teamDeduct));
			map.put(userAcc, param);
		}
			
		// 个人正能量指数
		list = agentDao.stAgentPostive();				
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的个人正能量指数记录数:" + (list == null ? 0 : list.size()));				
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);								
			String userAcc = row.getColumnValue("USER_ACC");			
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}					
				if(StringUtils.isBlank(stAgentId)) {					
					stAgentId = IDGenerator.getDefaultNUMID();	
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}			
			param.put("POSTIVE_INDEX", CommonUtil.parseInt(row.getColumnValue("POSTIVE_INDEX")));
			map.put(userAcc, param);
		}
		
		// 质量明星上榜次数
		list = agentDao.stAgentQcStar(stDate);				
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的质量明星上榜记录数:" + (list == null ? 0 : list.size()));				
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);								
			String userAcc = row.getColumnValue("USER_ACC");			
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}		
				if(StringUtils.isBlank(stAgentId)) {					
					stAgentId = IDGenerator.getDefaultNUMID();	
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}			
			param.put("QC_STAR_NUM", CommonUtil.parseInt(row.getColumnValue("QC_STAR_NUM")));
			map.put(userAcc, param);
		}
		
		// 质量明星累计上榜次数
		list = agentDao.stAgentQcStar(null);				
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的质量明星累计上榜记录数:" + (list == null ? 0 : list.size()));				
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);						
			String userAcc = row.getColumnValue("USER_ACC");			
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}			
				if(StringUtils.isBlank(stAgentId)) {					
					stAgentId = IDGenerator.getDefaultNUMID();	
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}			
			param.put("TOTAL_QC_STAR_NUM", CommonUtil.parseInt(row.getColumnValue("QC_STAR_NUM")));
			map.put(userAcc, param);
		}
		
		// 培训考试成绩
		list = agentDao.stAgentTestScore(stDate);				
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的培训考试成绩记录数:" + (list == null ? 0 : list.size()));				
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);						
			String userAcc = row.getColumnValue("USER_ACC");			
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}				
				if(StringUtils.isBlank(stAgentId)) {					
					stAgentId = IDGenerator.getDefaultNUMID();	
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}			
			param.put("AVG_TEST_SCORE", CommonUtil.parseDouble(row.getColumnValue("AVG_TEST_SCORE")));
			map.put(userAcc, param);
		}
		
		// 培训时长
		list = agentDao.stAgentTrainLen(stDate);				
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到的培训时长记录数:" + (list == null ? 0 : list.size()));				
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);								
			String userAcc = row.getColumnValue("USER_ACC");			
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}		
				if(StringUtils.isBlank(stAgentId)) {					
					stAgentId = IDGenerator.getDefaultNUMID();	
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}	
			double trainLen = CommonUtil.parseDouble(row.getColumnValue("TRAIN_DURATION"));
			trainLen = calcAvg(60, trainLen);
			param.put("AVG_TEST_SCORE", trainLen);
			map.put(userAcc, param);
		}
		
		// 绩效排名
		for(String userAcc : agentsAcc.keySet()) {
			int rank = agentDao.stAgentRank(userAcc, stDate);
			if(rank == 9999)
				continue;
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}		
				if(StringUtils.isBlank(stAgentId)) {					
					stAgentId = IDGenerator.getDefaultNUMID();	
					Agent agent = agentsAcc.get(userAcc);
					if(agent == null)
						continue;
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			}	
			param.put("JX_RANKING", rank);
			map.put(userAcc, param);
		}
		try {	
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_OTHER");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席其他信息,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席其他信息,入库时失败", e);
		}
	}

	/**
	 * 统计坐席的专项记录
	 * @param stDate
	 */
	public void stAgentJX(String stDate) {
		EasyQuery query = agentDao.getQueryHelper();
		// 删除坐席的汇总记录
		agentDao.delAgentRecord(query,"C_ST_AGENT_JX", stDate, Constants.ST_TYPE_DAY);

		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();

		this.stAgentJXData(stDate, Constants.ST_TYPE_DAY, map, sqlList);
		agentDao.map2Save(sqlList, map, "C_ST_AGENT_JX");
		try {
			logger.info("【"+Thread.currentThread().getName()+"】" + "统计坐席的绩效记录,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(query, sqlList);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席的绩效记录,入库时失败", e);
		}
	}

	private void stAgentJXData(String stDateTime,String stType, Map<String, Map<String, Object>> map, ArrayList<String> sqlList) {
		String stDate = stDateTime.substring(0, 10);
		//达标值、哺乳假、工作时长
		List<EasyRow> list = agentDao.stAgentStandard(stDate);
		logger.info("【"+Thread.currentThread().getName()+"】" + " 本次统计的时间范围：" + stDate
				+ "，查询到的达标值、哺乳假、工作时长记录数:" + (list == null? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("WORK_NO");

			Map<String, Object> param = this.initParam(stDateTime, stType, map, userAcc, sqlList);
			if (param == null) continue;

			param.put("STANDARD_VALUE", row.getColumnValue("STANDARD_VALUE")); // 应达标值
			param.put("BREASTFEED_LEAVE", row.getColumnValue("BREASTFEED_LEAVE")); // 是否哺乳假
			param.put("WORK_TIME_LEN", row.getColumnValue("WORK_TIME")); // 上班时长
			map.put(userAcc, param);
		}

		// 扣休时长记录
		list = agentDao.stAgentKxTimeLen(stDate);
		logger.info("【"+Thread.currentThread().getName()+"】" + " 本次统计的时间范围：" + stDate
				+ "，查询到的扣休时长记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("WORK_NO");

			Map<String, Object> param = this.initParam(stDateTime, stType, map, userAcc, sqlList);
			if (param == null) continue;
			param.put("EARLY_OFF_TIME_LEN", this.getBigDecimalStrVal(row.getColumnValue("KX_LONG")));
			map.put(userAcc, param);
		}

		// 请假时长记录
		list = agentDao.stAgentLeaveTimeLen(stDate);
		logger.info("【"+Thread.currentThread().getName()+"】" + " 本次统计的时间范围：" + stDate
				+ "，查询到的请假时长记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("WORK_NO");

			Map<String, Object> param = this.initParam(stDateTime, stType, map, userAcc, sqlList);
			if (param == null) continue;
			param.put("LEAVE_TIME_LEN", this.getBigDecimalStrVal(row.getColumnValue("LEAVE_LONG")));
			map.put(userAcc, param);
		}

		// 补休时长记录
		list = agentDao.stAgentRestTimeLen(stDate);
		logger.info("【"+Thread.currentThread().getName()+"】" + " 本次统计的时间范围：" + stDate
				+ "，查询到的补休时长记录数:" + (list == null ? 0 : list.size()));
		for (int i=0;list!=null&&i<list.size();i++) {
			EasyRow row = list.get(i);
			String userAcc = row.getColumnValue("WORK_NO");

			Map<String, Object> param = this.initParam(stDateTime, stType, map, userAcc, sqlList);
			if (param == null) continue;
			param.put("REST_TIME_LEN", this.getBigDecimalStrVal(row.getColumnValue("REST_TIME")));
			map.put(userAcc, param);
		}
	}

	private String getBigDecimalStrVal(String val) {
		if (StringUtils.isBlank(val)) return "0";

		return new BigDecimal(val).setScale(2, RoundingMode.HALF_UP).toString();
	}

	/**
	 * 重新统计30天内坐席的专项时长 请假时长 补休时长
	 * @param stDate
	 */
	public void stAgentJXZXHistoryData(String stDate) {
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		String updateSql = "UPDATE C_ST_AGENT_JX SET LEAVE_TIME_LEN = ?,REST_TIME_LEN = ? WHERE ST_AGENT_ID =?";
		List<Object[]> updateParams = new ArrayList<>();

		List<EasyRow> list = agentDao.stAgentJxHistory(stDate,stDate);
		logger.info("【"+Thread.currentThread().getName()+"】" + " 本次统计的时间范围：" + stDate
				+ "，查询到的 请假时长 补休时长 记录数:" + (list == null ? 0 : list.size()));

		EasyQuery query = agentDao.getQueryHelper();
		try {
			for (int i=0;list!=null&&i<list.size();i++) {
				EasyRow row = list.get(i);
				String stAgentId = row.getColumnValue("ST_AGENT_ID");
				String userAcc = row.getColumnValue("WORK_NO");
				String restTime = row.getColumnValue("REST_TIME");
				String leaveTime = row.getColumnValue("LEAVE_TIME");

				Map<String, Object> param = this.initParamDay(stDate, map, userAcc, sqlList);
				if (param == null) {
					continue;
				}

				if (StringUtils.isBlank(stAgentId)){
					//如果原先不存在需要初始化记录
					param.put("STANDARD_VALUE", 0); // 达标值
					param.put("BREASTFEED_LEAVE", 0); // 是否哺乳假
					param.put("EARLY_OFF_TIME_LEN", 0);
					param.put("LEAVE_TIME_LEN", this.getBigDecimalStrVal(leaveTime));
					param.put("REST_TIME_LEN", this.getBigDecimalStrVal(restTime));
					map.put(userAcc, param);
				}else {
					//如果原先已存在则更新记录
					List<String> updateList = new ArrayList<>();
					updateList.add(this.getBigDecimalStrVal(leaveTime));
					updateList.add(this.getBigDecimalStrVal(restTime));
					updateList.add(stAgentId);
					updateParams.add(updateList.toArray());
				}
			}
			logger.info("【"+Thread.currentThread().getName()+"】" + "统计坐席的绩效记录,入库记录数:" + sqlList.size());
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_JX");
			agentDao.batchExecSql(query, sqlList);
			logger.info("【"+Thread.currentThread().getName()+"】" + "统计坐席的绩效记录,更新记录数:" + updateParams.size());
			query.executeBatch(updateSql, updateParams);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席的绩效记录,入库时失败", e);
		}


		//重新统计 专项时长
		updateSql = "UPDATE C_ST_AGENT_ZX SET ZX_LONG = ? WHERE ST_AGENT_ID =?";
		updateParams = new ArrayList<>();
		list = agentDao.stAgentZxHistory(stDate,stDate);
		logger.info("【"+Thread.currentThread().getName()+"】" + " 本次统计的时间范围：" + stDate
				+ "，查询到的专项时长记录数:" + (list == null ? 0 : list.size()));
		try {
			for (int i=0;list!=null&&i<list.size();i++) {
				EasyRow row = list.get(i);
				String stAgentId = row.getColumnValue("ST_AGENT_ID");
				String userAcc = row.getColumnValue("WORK_NO");
				String zxLong = row.getColumnValue("ZX_LONG");

				Map<String, Object> param = this.initParamDay(stDate, map, userAcc, sqlList);
				if (param == null) continue;


				if (StringUtils.isBlank(stAgentId)){
					//如果原先不存在需要初始化记录
					param.put("ZX_LONG", this.getBigDecimalStrVal(zxLong));
					map.put(userAcc, param);
				}else {
					//如果原先已存在则更新记录
					List<String> updateList = new ArrayList<>();
					updateList.add(this.getBigDecimalStrVal(zxLong));
					updateList.add(stAgentId);
					updateParams.add(updateList.toArray());
				}
			}

			logger.info("【"+Thread.currentThread().getName()+"】" + "统计坐席的专项记录,入库记录数:" + sqlList.size());
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_ZX");
			agentDao.batchExecSql(query, sqlList);
			logger.info("【"+Thread.currentThread().getName()+"】" + "统计坐席的专项记录,更新记录数:" + updateParams.size());
			query.executeBatch(updateSql, updateParams);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席的专项记录,入库时失败", e);
		}
	}

	private void roolback(EasyQuery query){
		try {
			query.roolback();
		}catch (Exception e){
			logger.error(CommonUtil.getClassNameAndMethod(this) + "回滚失败", e);
		}
	}
	private Map<String, Object> initParamDay(String stDate, Map<String, Map<String, Object>> map, String userAcc, ArrayList<String> sqlList) {
		return this.initParam(stDate,Constants.ST_TYPE_DAY, map, userAcc, sqlList);
	}
	private Map<String, Object> initParam(String stDate,String stType, Map<String, Map<String, Object>> map, String userAcc, ArrayList<String> sqlList) {
		Map<String, Object> param = new HashMap<String, Object>();
		if(map.containsKey(userAcc)) {
			param = map.get(userAcc);
		}
		else {
			String stAgentId = stIds.get(userAcc);
			if(StringUtils.isBlank(stAgentId)) {
				stAgentId = agentDao.getAgentRecord(userAcc, stDate, stType);
				stIds.put(userAcc, stAgentId);
			}
			if(StringUtils.isBlank(stAgentId)) {
				stAgentId = IDGenerator.getDefaultNUMID();
				Agent agent = agentsAcc.get(userAcc);
				if(agent == null)
					return null;
				stRecord(sqlList, agent, stAgentId, stDate, stType);
				stIds.put(userAcc, stAgentId);
			}
			param.put("ID", IDGenerator.getDefaultNUMID());
			param.put("ST_AGENT_ID", stAgentId);
			param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
		}
		return param;
	}

	/**
	 * 统计坐席的话务量
	 * @param stType
	 * @param beginTime
	 * @param endTime
	 */
	public void stAgentCall(String stType, String beginTime, String endTime) {
		//检测是否存在多个工号映射同一个坐席账号
		Map<String ,Integer> workNoCount = new HashMap<String, Integer>();
		for (Agent agent : agentsNo.values()){
			workNoCount.put(agent.getUserAcc(), workNoCount.getOrDefault(agent.getUserAcc(), 0) + 1);
		}
		for (String acc:workNoCount.keySet()){
			if(workNoCount.get(acc) > 1){
				logger.warn("stAgentCall()=>账号["+acc+"]存在多个坐席数据:"+workNoCount.get(acc));
			}
		}

		String begin = beginTime.replace("-", "").replace(" ", "").replace(":", "");
		String end = endTime.replace("-", "").replace(" ", "").replace(":", "");		
		String tempTime = beginTime;
		if(Constants.ST_TYPE_DAY.equals(stType))
			tempTime = beginTime.substring(0, 10);

		// 删除坐席话务的汇总记录
		agentDao.delAgentRecord("C_ST_AGENT_CALL", tempTime, stType);
			
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		ArrayList<String> sqlList = new ArrayList<String>();
		
		// 坐席的话务量（顺德）
		List<EasyRow> list = agentDao.stAgentCallNum(Constants.GENESYS_SD_DS, begin, end);
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ beginTime + "~" + endTime + "，查询到坐席的话务量（顺德）记录数:" + (list == null ? 0 :list.size()));
		if(list!=null){
			for (int i=0;list!=null&&i<list.size();i++) {	
				EasyRow row = list.get(i);	
				String workNo = row.getColumnValue("OBJECT_NAME");
				int loginLen = CommonUtil.parseInt(row.getColumnValue("T_LOGIN"));

				if(loginLen == 0)
					continue;
				
				Agent agent = agentsNo.get(workNo);
				if(agent == null){
					logger.info(CommonUtil.getClassNameAndMethod(this) + "没有坐席："+workNo);
					continue;
				}

				Map<String, Object> param = new HashMap<String, Object>();
				if (map.containsKey(agent.getUserAcc())) {
					param = map.get(agent.getUserAcc());
				}
				else {
					String stAgentId = stIds.get(agent.getUserAcc());
					int flag = 0;
					if(StringUtils.isBlank(stAgentId)) {
						stAgentId = agentDao.getAgentRecord(agent.getUserAcc(), tempTime, stType);
						stIds.put(agent.getUserAcc(), stAgentId);
						flag = 1;
					}	
					if(StringUtils.isBlank(stAgentId)) {
						stAgentId = IDGenerator.getDefaultNUMID();
						stRecord(sqlList, agent, stAgentId, tempTime, stType);
						stIds.put(agent.getUserAcc(), stAgentId);
						flag = 2;
					}
					param.put("ID", IDGenerator.getDefaultNUMID());
					param.put("ST_AGENT_ID", stAgentId);
					param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
					stAgentCallMonitor.info(agent.getUserAcc()+"（"+workNo+"） 初始化呼叫统计数据ID="+param.get("ID")+",ST_AGENT_ID="+stAgentId+",flag="+flag);
				}		
				int readyLen = CommonUtil.parseInt(row.getColumnValue("T_RADEY"));
				int callInNum = CommonUtil.parseInt(row.getColumnValue("N_CALL_IB"));
				double avgWaitLen = calcAvg(callInNum, readyLen);
				
				param.put("LOGIN_LEN", loginLen);
				param.put("PLAT_CALL_IN_NUM", callInNum);
				param.put("PLAT_CALL_OUT_NUM", CommonUtil.parseInt(row.getColumnValue("N_CALLS_C_OBTA")));	
				param.put("CALLS_IN_LEN", CommonUtil.parseInt(row.getColumnValue("T_CALLS_IN")));	
				param.put("TALK_OB_LEN", CommonUtil.parseInt(row.getColumnValue("T_TALK_OB")));	
				param.put("READY_LEN", readyLen);
				param.put("NOT_READY_LEN", CommonUtil.parseInt(row.getColumnValue("T_NOT_READY")));
				param.put("ACW_LEN", CommonUtil.parseInt(row.getColumnValue("T_ACW")));
				param.put("RING_OB_LEN", CommonUtil.parseInt(row.getColumnValue("T_RING_OB")));
				param.put("ANSWER_IN_LEN", CommonUtil.parseInt(row.getColumnValue("T_ANSWER_IN")));
				param.put("AVG_WAIT_LEN", avgWaitLen);
//				logger.info(CommonUtil.getClassNameAndMethod(this) + "统计顺德坐席话务量workNo=" +workNo+",loginLen="+loginLen+"record="+JSON.toJSONString(param));
				map.put(agent.getUserAcc(), param);
			}
		}

		// 坐席的话务量（合肥）
//		list = agentDao.stAgentCallNum(Constants.GENESYS_HF_DS, begin, end);
//		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围："
//				+ beginTime + "~" + endTime + "，查询到坐席的话务量（合肥）记录数:" + (list == null ? 0 :list.size()));
//		if(list!=null){
//
//			for (int i=0;list!=null&&i<list.size();i++) {
//				EasyRow row = list.get(i);
//				String workNo = row.getColumnValue("OBJECT_NAME");
//				int loginLen = CommonUtil.parseInt(row.getColumnValue("T_LOGIN"));
//				if(loginLen == 0)
//					continue;
//
//				Agent agent = agentsNo.get(workNo);
//				if(agent == null)
//					continue;
//
//				Map<String, Object> param = new HashMap<String, Object>();
//				if (map.containsKey(agent.getUserAcc())) {
//					param = map.get(agent.getUserAcc());
//				}
//				else {
//					String stAgentId = stIds.get(agent.getUserAcc());
//					if(StringUtils.isBlank(stAgentId)) {
//						stAgentId = agentDao.getAgentRecord(agent.getUserAcc(), tempTime, stType);
//						stIds.put(agent.getUserAcc(), stAgentId);
//					}
//					if(StringUtils.isBlank(stAgentId)) {
//						stAgentId = IDGenerator.getDefaultNUMID();
//						stRecord(sqlList, agent, stAgentId, tempTime, stType);
//						stIds.put(agent.getUserAcc(), stAgentId);
//					}
//					param.put("ID", IDGenerator.getDefaultNUMID());
//					param.put("ST_AGENT_ID", stAgentId);
//					param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
//				}
//				int readyLen = CommonUtil.parseInt(row.getColumnValue("T_RADEY"));
//				int callInNum = CommonUtil.parseInt(row.getColumnValue("N_CALL_IB"));
//				double avgWaitLen = calcAvg(callInNum, readyLen);
//
//				param.put("LOGIN_LEN", plusData(row.getColumnValue("T_LOGIN"),param.get("LOGIN_LEN")));
//				param.put("PLAT_CALL_IN_NUM", plusData(row.getColumnValue("N_CALL_IB"),param.get("PLAT_CALL_IN_NUM")));
//				param.put("PLAT_CALL_OUT_NUM", plusData(row.getColumnValue("N_CALLS_C_OBTA"),param.get("PLAT_CALL_OUT_NUM")));
//				param.put("CALLS_IN_LEN", plusData(row.getColumnValue("T_CALLS_IN"),param.get("CALLS_IN_LEN")));
//				param.put("TALK_OB_LEN", plusData(row.getColumnValue("T_TALK_OB"),param.get("TALK_OB_LEN")));
//				param.put("READY_LEN", plusData(row.getColumnValue("T_RADEY"),param.get("READY_LEN")));
//				param.put("NOT_READY_LEN", plusData(row.getColumnValue("T_NOT_READY"),param.get("NOT_READY_LEN")));
//				param.put("ACW_LEN", plusData(row.getColumnValue("T_ACW"),param.get("ACW_LEN")));
//				param.put("RING_OB_LEN", plusData(row.getColumnValue("T_RING_OB"),param.get("RING_OB_LEN")));
//				param.put("ANSWER_IN_LEN", plusData(row.getColumnValue("T_ANSWER_IN"),param.get("ANSWER_IN_LEN")));
//				param.put("AVG_WAIT_LEN", plusData(avgWaitLen,param.get("AVG_WAIT_LEN")));
//				map.put(agent.getUserAcc(), param);
//			}
//		}
		// 呼出、呼入折算量
		list = agentDao.stAgentCallZs(beginTime + ":00", endTime + ":00");
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ beginTime + "~" + endTime + "，查询到坐席的呼出、呼入折算记录数:" + (list == null ? 0 :list.size()));		
		if(list!=null){
			for (int i=0;list!=null&&i<list.size();i++) {	
				EasyRow row = list.get(i);	
				String userAcc = row.getColumnValue("AGENT_ACC");

				Map<String, Object> param = new HashMap<String, Object>();
				if(map.containsKey(userAcc)) {
					param = map.get(userAcc);
				}
				else {
					int flag = 0;
					String stAgentId = stIds.get(userAcc);
					if(StringUtils.isBlank(stAgentId)) {
						stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
						stIds.put(userAcc, stAgentId);
						flag = 1;
					}	
					if(StringUtils.isBlank(stAgentId)) {		
						stAgentId = IDGenerator.getDefaultNUMID();
						Agent agent = agentsAcc.get(userAcc);
						if(agent == null){
							flag = 3;
							continue;
						}
						stRecord(sqlList, agent, stAgentId, tempTime, stType);
						stIds.put(userAcc, stAgentId);
						flag = 2;
					}	
					param.put("ID", IDGenerator.getDefaultNUMID());
					param.put("ST_AGENT_ID", stAgentId);
					param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
					stAgentCallMonitor.warn(userAcc+" 在折算量处理中为用户创建新记录（可能是顺德数据中被跳过的用户），呼叫统计数据ID="+param.get("ID")+",ST_AGENT_ID="+stAgentId+",flag="+flag);
				}
				int talkIn10 = CommonUtil.parseInt(row.getColumnValue("TALK_IN_10"));
				int talkOb10 = CommonUtil.parseInt(row.getColumnValue("TALK_OB_10"));
				int less10Num = talkIn10 + talkOb10;
				
				param.put("CALL_IN_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_IN_NUM")));	
				param.put("CALL_OUT_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_NUM")));
				param.put("MORE_600_NUM", CommonUtil.parseInt(row.getColumnValue("MORE_600_NUM")));
				param.put("TALK_IN_10", talkIn10);
				param.put("TALK_OB_10", talkOb10);		
				param.put("LESS_10_NUM", less10Num);		
				param.put("CALL_IN_ZS_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_IN_ZS")));
				param.put("CALL_OUT_ZS_NUM", CommonUtil.parseDouble(row.getColumnValue("CALL_OUT_ZS")));		
				param.put("CALL_OUT_ZS_05_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_05_NUM")));
				param.put("CALL_OUT_ZS_075_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_075_NUM")));
				param.put("CALL_OUT_ZS_08_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_08_NUM")));
				param.put("CALL_OUT_ZS_085_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_085_NUM")));
				param.put("CALL_OUT_ZS_09_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_09_NUM")));
				param.put("CALL_OUT_ZS_095_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_095_NUM")));
				param.put("CALL_OUT_ZS_10_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_10_NUM")));
				param.put("CALL_OUT_ZS_11_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_11_NUM")));
				param.put("CALL_OUT_ZS_12_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_12_NUM")));
				param.put("CALL_OUT_ZS_13_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_13_NUM")));
				param.put("CALL_OUT_ZS_14_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_14_NUM")));
				param.put("CALL_OUT_ZS_15_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_15_NUM")));
				param.put("CALL_OUT_ZS_16_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_16_NUM")));
				param.put("CALL_OUT_ZS_17_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_17_NUM")));
				param.put("CALL_OUT_ZS_18_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_18_NUM")));
				param.put("CALL_OUT_ZS_20_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_20_NUM")));
				param.put("CALL_OUT_ZS_22_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_22_NUM")));
				param.put("CALL_OUT_ZS_23_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_23_NUM")));
				param.put("CALL_OUT_ZS_25_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_25_NUM")));
				param.put("CALL_OUT_ZS_27_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_27_NUM")));
				param.put("CALL_OUT_ZS_30_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_30_NUM")));
				param.put("CALL_OUT_ZS_35_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_35_NUM")));
				param.put("CALL_OUT_ZS_40_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_40_NUM")));
				param.put("CALL_OUT_ZS_45_NUM", CommonUtil.parseInt(row.getColumnValue("CALL_OUT_ZS_45_NUM")));
				param.put("VIDEO_NUM", CommonUtil.parseInt(row.getColumnValue("VIDEO_NUM")));
				map.put(userAcc, param);
			}
		}
			
		
		// 当天第一通话务时间
		list = agentDao.stAgentFirstCall(beginTime.substring(0, 10));
		logger.info(CommonUtil.getClassNameAndMethod(this) + "本次统计的时间范围：" 		
				+ beginTime + "~" + endTime + "，查询到坐席当天第一通话务的记录数:" + (list == null ? 0 :list.size()));		
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");

			Map<String, Object> param = new HashMap<String, Object>();
			if(map.containsKey(userAcc)) {
				param = map.get(userAcc);
				param.put("FIRST_CALL_TIME", row.getColumnValue("ANSWER_TIME"));	
				map.put(userAcc, param);
			}
		}	
		try {
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_CALL");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席的话务量 ,入库记录数:" + sqlList.size());
//			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席的话务量sql:" + JSON.toJSONString(sqlList));
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席的话务量 ,入库时失败", e);
		}
	}

	
	/**
	 * 统计坐席超十分钟无话务明细
	 * @param stType
	 * @param beginTime
	 * @param endTime
	 */
	public void stAgentNoCall(String stType, String beginTime, String endTime) {
		String tempTime = beginTime.substring(0, 16);
		if(Constants.ST_TYPE_DAY.equals(stType))
			tempTime = beginTime.substring(0, 10);
		
		// 删除坐席话务的汇总记录
		agentDao.delAgentRecord("C_ST_AGENT_NO_CALL", tempTime, stType);
		
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();	
		ArrayList<String> sqlList = new ArrayList<String>();
		logger.info(EasyDate.getCurrentDateString()+"统计坐席超十分钟无话务明细 ,agentsAcc的长度:"+agentsAcc.size());
		for(String userAcc : agentsAcc.keySet()) {
			Agent agent = agentsAcc.get(userAcc);			
			
			String stAgentId = stIds.get(userAcc);
			if(StringUtils.isBlank(stAgentId)) {
				stAgentId = agentDao.getAgentRecord(userAcc, tempTime, stType);
				stIds.put(userAcc, stAgentId);
			}	
			if(StringUtils.isBlank(stAgentId)) {		
				stAgentId = IDGenerator.getDefaultNUMID();
				stRecord(sqlList, agent, stAgentId, tempTime, stType);
				stIds.put(userAcc, stAgentId);
			}	
			
			if(StringUtils.isBlank(agent.getSchedulingId()) || agent.getAttendanceLen() == 0 
					|| DateUtil.compareDate(agent.getEndTime(), beginTime, DateUtil.TIME_FORMAT) < 1 
					|| DateUtil.compareDate(agent.getStartTime(), endTime, DateUtil.TIME_FORMAT) > -1)
				continue;
			
			List<EasyRow> list = agentDao.stAgent10Call(userAcc, beginTime, endTime);
			if(list == null || list.size() == 0) {
				Map<String, Object> param = new HashMap<String, Object>();
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
				
				if(DateUtil.compareDate(agent.getStartTime(), beginTime, DateUtil.TIME_FORMAT) == -1 
						&& DateUtil.compareDate(agent.getEndTime(), endTime, DateUtil.TIME_FORMAT) == -1) {
					int seconds = DateUtil.bwSeconds(agent.getEndTime(), beginTime);
					if(seconds > 600) {
						param.put("BF_10_CALL_TIME", beginTime);
						param.put("AF_10_CALL_TIME", agent.getEndTime());
						param.put("NO_CALL_10_LEN", seconds);
						map.put(userAcc, param);
					}
				}
				else if(DateUtil.compareDate(agent.getStartTime(), beginTime, DateUtil.TIME_FORMAT) > -1 
						&& DateUtil.compareDate(agent.getEndTime(), endTime, DateUtil.TIME_FORMAT) < 1) {
					int seconds = DateUtil.bwSeconds(agent.getEndTime(), agent.getStartTime());
					if(seconds > 600) {
						param.put("BF_10_CALL_TIME", agent.getStartTime());
						param.put("AF_10_CALL_TIME", agent.getEndTime());
						param.put("NO_CALL_10_LEN", seconds);		
						map.put(userAcc, param);
					}
				}				
				else if(DateUtil.compareDate(agent.getStartTime(), beginTime, DateUtil.TIME_FORMAT) == 1 
						&& DateUtil.compareDate(agent.getEndTime(), endTime, DateUtil.TIME_FORMAT) == 1) {
					int seconds = DateUtil.bwSeconds(endTime, agent.getStartTime());
					if(seconds > 600) {
						param.put("BF_10_CALL_TIME", agent.getStartTime());
						param.put("AF_10_CALL_TIME", endTime);
						param.put("NO_CALL_10_LEN", seconds);	
						map.put(userAcc, param);
					}
				}
			}
			else{		
				String firstTime = list.get(0).getColumnValue("ANSWER_TIME");
				if(DateUtil.compareDate(agent.getStartTime(), beginTime, DateUtil.TIME_FORMAT) == -1) {
					Map<String, Object> param = new HashMap<String, Object>();
					int seconds = DateUtil.bwSeconds(firstTime, beginTime);
					if(seconds > 600) {
						param.put("ID", IDGenerator.getDefaultNUMID());
						param.put("ST_AGENT_ID", stAgentId);
						param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
						param.put("BF_10_CALL_TIME", beginTime);
						param.put("AF_10_CALL_TIME", firstTime);
						param.put("NO_CALL_10_LEN", seconds);	
						map.put(userAcc + beginTime, param);
					}
				}
				else {
					Map<String, Object> param = new HashMap<String, Object>();
					int seconds = DateUtil.bwSeconds(firstTime, agent.getStartTime());
					if(seconds > 600) {
						param.put("ID", IDGenerator.getDefaultNUMID());
						param.put("ST_AGENT_ID", stAgentId);
						param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
						param.put("BF_10_CALL_TIME", agent.getStartTime());
						param.put("AF_10_CALL_TIME", firstTime);
						param.put("NO_CALL_10_LEN", seconds);
						map.put(userAcc + agent.getStartTime(), param);
					}
				}
			
				for(int i = 0; i < list.size() - 1; i++) {
					Map<String, Object> param = new HashMap<String, Object>();
					String bfCallTime = list.get(i).getColumnValue("END_TIME");
					String afCallTime = list.get(i + 1).getColumnValue("ANSWER_TIME");
					
					if(DateUtil.compareDate(agent.getStartTime(), bfCallTime, DateUtil.TIME_FORMAT) == 1)
						continue;
											
					if(DateUtil.compareDate(agent.getEndTime(), afCallTime, DateUtil.TIME_FORMAT) == -1)
						afCallTime = agent.getEndTime();
					
					int seconds = DateUtil.bwSeconds(afCallTime, bfCallTime);
					if(seconds > 600) {
						param.put("ID", IDGenerator.getDefaultNUMID());
						param.put("ST_AGENT_ID", stAgentId);
						param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
						param.put("BF_10_CALL_TIME", bfCallTime);
						param.put("AF_10_CALL_TIME", afCallTime);
						param.put("NO_CALL_10_LEN", seconds);		
						map.put(userAcc + bfCallTime, param);
					}
				} 
				
				String lastTime = list.get(list.size() - 1).getColumnValue("END_TIME");
				if(DateUtil.compareDate(agent.getEndTime(), endTime, DateUtil.TIME_FORMAT) == 1) {
					Map<String, Object> param = new HashMap<String, Object>();
					int seconds = DateUtil.bwSeconds(endTime, lastTime);
					if(seconds > 600) {
						param.put("ID", IDGenerator.getDefaultNUMID());
						param.put("ST_AGENT_ID", stAgentId);
						param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
						param.put("BF_10_CALL_TIME", lastTime);
						param.put("AF_10_CALL_TIME", endTime);
						param.put("NO_CALL_10_LEN", seconds);	
						map.put(userAcc + lastTime, param);
					}
				}
				else {
					Map<String, Object> param = new HashMap<String, Object>();
					int seconds = DateUtil.bwSeconds(agent.getEndTime(), lastTime);
					if(seconds > 600) {
						param.put("ID", IDGenerator.getDefaultNUMID());
						param.put("ST_AGENT_ID", stAgentId);
						param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
						param.put("BF_10_CALL_TIME", lastTime);
						param.put("AF_10_CALL_TIME", agent.getEndTime());
						param.put("NO_CALL_10_LEN", seconds);
						map.put(userAcc + lastTime, param);
					}
				}
			}
			if(map.size() >= 2000) {				
				try {
					agentDao.map2Save(sqlList, map, "C_ST_AGENT_NO_CALL");
					logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席超十分钟无话务明细 ,入库记录数:" + sqlList.size());
					agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);
					
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席超十分钟无话务明细 ,入库时失败", e);
				}
				sqlList.clear();
				map.clear();
			}
		}
		try {
			agentDao.map2Save(sqlList, map, "C_ST_AGENT_NO_CALL");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计坐席超十分钟无话务明细 ,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计坐席超十分钟无话务明细 ,入库时失败", e);
		}
	}
	
	
	/**
	 * 统计全媒体坐席值班情况
	 * @param stDate
	 */
	public void stAgentMediaDuty(String stDate) {
		// 删除全媒体坐席值班情况记录
		agentDao.delAgentRecord("C_ST_MEDIA_AGENT_DUTY", stDate, Constants.ST_TYPE_DAY );
		
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();	
		ArrayList<String> sqlList = new ArrayList<String>();
		
		// 坐席值班情况（全媒体）
		List<EasyRow> list = agentDao.stAgentMediaDuty(stDate);
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate
				+ "，查询到全媒体坐席的值班记录数:" + (list == null ? 0 : list.size()));		
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String workNo = row.getColumnValue("AGENT_ID");
			workNo = workNo.substring(0, workNo.indexOf("@"));		
			int onlineLen = CommonUtil.parseInt(row.getColumnValue("ONLINE_TIME"));
			if(onlineLen == 0)
				continue;	
			
			Agent agent = agentsNo.get(workNo);
			if(agent == null)
				continue;	
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(agent.getUserAcc())) {
				param = map.get(agent.getUserAcc());
			}
			else {
				String stAgentId = stIds.get(agent.getUserAcc());
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(agent.getUserAcc(), stDate, Constants.ST_TYPE_DAY);
					stIds.put(agent.getUserAcc(), stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();			
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(agent.getUserAcc(), stAgentId);
				}	
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}	
			String firstOnline = row.getColumnValue("FIRST_LOGIN_TIME");
			int serviceLen = CommonUtil.parseInt(row.getColumnValue("SERVICE_TIME"));
			int restLen = CommonUtil.parseInt(row.getColumnValue("NOTREADY1_TIME"));
			double restRate = calcRate(onlineLen, restLen);
			int feeLen = CommonUtil.parseInt(row.getColumnValue("NEW_READY_TIME"));
			int notReadyTime = CommonUtil.parseInt(row.getColumnValue("NEW_NOTREADY_TIME"));
			int newOnlineLen = feeLen+notReadyTime;
			double feeRate = calcRate(onlineLen, feeLen);
			
			if(!firstOnline.equals("-")) {
				param.put("FIRST_ONLINE_TIME", stDate + " " + firstOnline);
			}
			param.put("LAST_OFFLINE_TIME", row.getColumnValue("UPDATE_TIME"));
			param.put("LOGIN_NUM", CommonUtil.parseInt(row.getColumnValue("LOGIN_COUNT")));
			param.put("ONLINE_LEN", newOnlineLen);
			param.put("OLD_ONLINE_LEN", onlineLen);
			param.put("SERVICE_LEN", serviceLen);
			//小休时长
			param.put("OLD_REST_LEN", restLen);
			param.put("REST_LEN", notReadyTime);
			param.put("REST_NUM", CommonUtil.parseInt(row.getColumnValue("NOTREADY1_COUNT")));
			param.put("REST_PER", restRate);
			//空闲时长
			param.put("FREE_LEN", feeLen);
			param.put("FREE_PER", feeRate);
			param.put("FIRST_SERVER_TIME",row.getColumnValue("FIRST_SERVER_TIME"));
			param.put("LAST_SERVER_END_TIME",row.getColumnValue("LAST_SERVER_END_TIME"));
			param.put("FIRST_FREE_TIME",row.getColumnValue("FIRST_FREE_TIME"));
			param.put("LAST_REST_TIME",row.getColumnValue("LAST_REST_TIME"));
			map.put(agent.getUserAcc(), param);
		}
		
		// 统计坐席的接通量、转出量、转入量（全媒体）
		list = agentDao.stAgentMediaExtend(stDate);		
		logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次统计的时间范围：" + stDate 
				+ "，查询到坐席的接通量、转出量、转入量（全媒体）的记录数:" + (list == null ? 0 : list.size()));			
		for (int i=0;list!=null&&i<list.size();i++) {	
			EasyRow row = list.get(i);	
			String userAcc = row.getColumnValue("AGENT_ACC");
			Agent agent = agentsAcc.get(userAcc);
			if(agent == null)
				continue;				
			
			Map<String, Object> param = new HashMap<String, Object>();
			if (map.containsKey(userAcc)) {
				param = map.get(userAcc);
			}
			else {
				String stAgentId = stIds.get(userAcc);
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = agentDao.getAgentRecord(userAcc, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}
				if(StringUtils.isBlank(stAgentId)) {
					stAgentId = IDGenerator.getDefaultNUMID();				
					stRecord(sqlList, agent, stAgentId, stDate, Constants.ST_TYPE_DAY);
					stIds.put(userAcc, stAgentId);
				}		
				param.put("ID", IDGenerator.getDefaultNUMID());
				param.put("ST_AGENT_ID", stAgentId);
				param.put("CREATE_TIME", DateUtil.getCurrentDateStr());		
			}
			param.put("IN_NUM", CommonUtil.parseInt(row.getColumnValue("TOTAL")));
			param.put("TRANS_IN_NUM", CommonUtil.parseInt(row.getColumnValue("TRANS_IN_NUM")));
			param.put("TRANS_OUT_NUM", CommonUtil.parseInt(row.getColumnValue("TRANS_OUT_NUM")));
			param.put("RECEPTION_NUM", CommonUtil.parseInt(row.getColumnValue("TOTAL")));
			
			// 班组平均接待量（全媒体）
			EasyRow deptRow = agentDao.stDeptMediaAvgReception(stDate, agent.getDeptCode(), agent.getSchedulingId());
			if(deptRow != null) { 
				int receptionNum = CommonUtil.parseInt(deptRow.getColumnValue("RECEPTION_NUM"));
				int agentNum = CommonUtil.parseInt(deptRow.getColumnValue("AGENT_NUM"));
				double avgReceptionNum = calcAvg(agentNum, receptionNum);
				param.put("AVG_RECEPTION_NUM", avgReceptionNum);
			}		
			map.put(userAcc, param);
		}
		try {
			agentDao.map2Save(sqlList, map, "C_ST_MEDIA_AGENT_DUTY");
			logger.info(CommonUtil.getClassNameAndMethod(this) + "统计全媒体坐席值班情况 ,入库记录数:" + sqlList.size());
			agentDao.batchExecSql(agentDao.getQueryHelper(), sqlList);

		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + "统计全媒体坐席值班情况 ,入库时失败", e);
		}
	}
	
		
	/**
	 * 添加坐席的汇总记录
	 * @param sqlList
	 * @param row
	 * @param stDate
	 * @param stType
	 * @return
	 */
 	public void stRecord(ArrayList<String> sqlList, Agent agent, String stAgentId, String stDate, String stType) {
		Map<String, Object> param = new HashMap<String, Object>();
		param.put("ID", stAgentId);
		param.put("USER_ACC", agent.getUserAcc());
		param.put("USER_NAME", agent.getUserName());
		param.put("DEPT_CODE", agent.getDeptCode());
		param.put("DEPT_NAME", agent.getDeptName());
		param.put("EP_CODE", agent.getEpCode());
		param.put("AREA_CODE", agent.getAreaCode());
		param.put("AREA_NAME", agent.getAreaName());
		param.put("WORK_MONTHS", agent.getWorkMonths());	
		param.put("SCHEDULING_ID", agent.getSchedulingId());
		param.put("SCHEDULING_NAME", agent.getSchedulingName());
		param.put("ATTENDANCE_LEN", agent.getAttendanceLen());
		param.put("ST_TYPE", stType);
		param.put("ST_DATE", stDate);
		param.put("CREATE_TIME", DateUtil.getCurrentDateStr());
		param.put("ENTRY_TIME", agent.getEntryTime()); //入职日期

		agentDao.mapToSave(sqlList, param, "C_ST_AGENT");
	}
 	public AgentService() {
 		
 	}
	public Map<String, Agent> getAgentAcc() {
		Map<String, Agent> json=agentsAcc;
		return json;
	}
	public Map<String, Agent> getAgentsNo() {
		Map<String, Agent> json=agentsNo;
		return json;
	}
	public Map<String, String> getstIds() {
		Map<String, String> json=stIds;
		return json;
	}

	private int plusData(String a,Object b){
		int i = 0;
		int k = 0;
		if(StringUtils.isNotBlank(a)){
			i= CommonUtil.parseInt(a);
		}
		if(b!=null){
			k =  CommonUtil.parseInt(b.toString());;
		}
		return i+k;
	}

	private double plusData(double a,Object b){
		double k = 0;
		if(b!=null){
			k =  CommonUtil.parseDouble(b.toString());;
		}
		return a+k;
	}
}
