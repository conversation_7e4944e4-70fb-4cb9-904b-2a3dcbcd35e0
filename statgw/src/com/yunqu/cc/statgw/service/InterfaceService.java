package com.yunqu.cc.statgw.service;

import java.sql.SQLException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;

import com.yunqu.cc.statgw.thread.ThreadPoolManage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.model.SyncInfoModel;
import com.yq.busi.common.service.SyncService;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.statgw.base.CommonLogger;
import com.yunqu.cc.statgw.base.Constants;


public class InterfaceService extends IService{
	public static Logger logger = CommonLogger.logger;
	public static Logger loggerTime = CommonLogger.getLogger("time");
//	数据修复方法
//	http://127.0.0.1:8080/ccreport/servlet/ent?action=DelMIN
//	http://127.0.0.1:8080/ccreport/servlet/ent?action=DelDay
//	http://127.0.0.1:8080/ccreport/servlet/ent?action=DelHour
//
//		然后改掉修改对应同步的时间
//		SELECT * from C_CF_SYNC_INFO where CODE = 'st_statByDay'
//		SELECT * from C_CF_SYNC_INFO where CODE = 'st_statByMin'
//		SELECT * from C_CF_SYNC_INFO where CODE = 'st_statByHour'
	@Override
	public JSONObject invoke(JSONObject json) {
		String command = json.getString("command");		
		logger.info("定时器执行"+json.toJSONString());

		//按天对数据进行统计
		if(ServiceCommand.STATGW_STAT_BY_DAY.equals(command)){
			try {
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
				LocalDateTime now = LocalDateTime.now();
				loggerTime.info("定时器执行开始时间"+now.format(formatter));
				String currentDate = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
				SyncInfoModel model = SyncService.getSyncInfo("st_statByDay");		
				String startDate = model.getSyncTime();
				logger.info("定时器执行开始时间"+startDate);
				if(null == startDate || "".equals(startDate)) {
					startDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, currentDate, -1);
				}
				int count = DateUtil.bwDays(startDate, currentDate, DateUtil.TIME_FORMAT_YMD);
				loggerTime.info("start:"+startDate+",current:"+currentDate+",count:"+count);
				for(int i = 0; i < count; i++) {
					String stDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, i);
					String nextDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, i + 1);
					logger.info("定时器执行时间段"+stDate+"-"+nextDate);
					AtomicBoolean next = new AtomicBoolean(false);
					//1、构建当天要执行的任务
					List<Runnable> tasks = this.initDateTask(stDate, nextDate, next);

					//2、执行任务
					CountDownLatch latch = new CountDownLatch(CollectionUtils.size(tasks));
					tasks.forEach(task -> {
						ThreadPoolManage.getThreadPool().execute(()->{
							try {
								task.run();
							} catch (Exception e) {
								logger.error(CommonUtil.getClassNameAndMethod(this) + "定时任务执行异常.", e);
							} finally {
								latch.countDown();
							}
						});
					});

					//3、等待任务执行结束
					try {
						latch.await();
						if(next.get()){
							SyncIvrinfoService.virInfoStat(startDate);
						}
					} catch (InterruptedException e) {
						logger.error(CommonUtil.getClassNameAndMethod(this) + "按天对数据进行统计失败(等待线程结束).", e);
					}
//					AgentService agentService = new AgentService(stDate);
//					agentService.stAgentListenRate(stDate);
//					agentService.stAgentMediaJX(stDate);
//					agentService.cleanStIds();
//					agentService.stAgentQd(stDate);
//					agentService.stAgentSkill(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
//					agentService.stAgentSatisDetail(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
//
//					agentService.stAgentTime(stDate);
//					agentService.stAgentComplaint(stDate);
//					agentService.stAgentCallOutNum(stDate);
//					agentService.stAgentOther(stDate);
//					agentService.stAgentCall(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
//					agentService.stAgentNoCall(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
//					agentService.stAgentMediaDuty(stDate);
//
//					QcAgentService qcAgentService = new QcAgentService(stDate);
//					qcAgentService.stQcAgentQd(stDate);
//
//					AreaService areaService = new AreaService();
//					areaService.stAreaQd(stDate);
//					areaService.stAreaRZ(stDate);
//					areaService.stAreaLy(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
//
//					DeptService deptService = new DeptService();
//					deptService.stDeptQc(stDate);
//
//					QueueService queueService = new QueueService();
//					queueService.stQueueCall(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
//					queueService.stQueueServiceCall(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
//					queueService.stQueueTransform(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
//
//					ChannelService channelService = new ChannelService();
//					logger.info("定时器执行stChannelSkillLevel"+stDate);
//					channelService.stChannelSkillLevel(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
				}
				model.setSyncTime(DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, count));
				SyncService.updateSyncTime(model);
				LocalDateTime end = LocalDateTime.now();
				Duration duration = Duration.between(now, end);
				long milliseconds = duration.toMillis();
				loggerTime.info("定时器执行结束时间"+end.format(formatter));
				loggerTime.info("按天对数据进行统计总耗时："+milliseconds+"ms");
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "按天对数据进行统计失败.", e);
			}
		}	
		else if (ServiceCommand.PORTALASSESSSERVICE.equals(command)) {
			try {
				String currentDate = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);	
				SyncInfoModel model = SyncService.getSyncInfo("PORTALASSESSSERVICE");		
				String startDate = model.getSyncTime();
				if(null == startDate || "".equals(startDate))
					startDate = currentDate;
						
				int count = DateUtil.bwDays(startDate, currentDate, DateUtil.TIME_FORMAT_YMD);				
				for(int i = 0; i <= count; i++) {
					String stDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, i);
					
					PortalService portalService = new PortalService(stDate);
					portalService.stAgentWorkByDay(stDate);					
					portalService.stDeptWorkByDay(stDate);
				}	
				model.setSyncTime(DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, count));
				SyncService.updateSyncTime(model);
				
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "首页指标统计失败.", e);
			}
		}
		else if("STATGW_STAT_BY_HOUR".equals(command)) {
			try {
				String fmt = "yyyy-MM-dd HH:mm";
				String currentTime = DateUtil.getCurrentDateStr(fmt);	
				SyncInfoModel model = SyncService.getSyncInfo("st_statByHour");	
				String syncTime = model.getSyncTime();
				logger.info("定时器st_statByHour执行开始时间段"+syncTime);

				if(null == syncTime || "".equals(syncTime))					
					syncTime = DateUtil.addHour(fmt, currentTime, -1).substring(0, 15) + "00";					

				int count = DateUtil.bwMinsWithCurrdate(syncTime + ":00") / 60;				
				for(int i = 0; i < count; i++) {
					String preTime = DateUtil.addHour(fmt, syncTime, i - 1);
					String beginTime = DateUtil.addHour(fmt, syncTime, i);
					String endTime = DateUtil.addHour(fmt, syncTime, i+1);
					
					AgentService agentService = new AgentService(beginTime.substring(0, 10));
					agentService.stAgentSkill(Constants.ST_TYPE_HOUR, preTime + ":00", beginTime + ":00");
					agentService.stAgentSatisDetail(Constants.ST_TYPE_HOUR, preTime + ":00", beginTime + ":00");
					agentService.cleanStIds();
					agentService.stAgentCall(Constants.ST_TYPE_HOUR, beginTime, endTime);
					
					AreaService areaService = new AreaService();	
					areaService.stAreaLy(Constants.ST_TYPE_HOUR, beginTime, endTime);
					
					QueueService queueService = new QueueService();
					queueService.stQueueCall(Constants.ST_TYPE_HOUR, beginTime, endTime);
					queueService.stQueueServiceCall(Constants.ST_TYPE_HOUR, beginTime, endTime);
					
					ChannelService channelService = new ChannelService();
					channelService.stChannelSkill(Constants.ST_TYPE_HOUR, preTime + ":00", beginTime + ":00");
				}	
				model.setSyncTime(DateUtil.addHour(fmt, syncTime, count));
				SyncService.updateSyncTime(model);
				
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "按小时对数据进行统计失败.", e);
			}
		}
		else if("STATGW_STAT_BY_MIN".equals(command)) {
			try {
				String fmt = "yyyy-MM-dd HH:mm";
				String currentTime = DateUtil.getCurrentDateStr(fmt);	
				SyncInfoModel model = SyncService.getSyncInfo("st_statByMin");	
				String syncTime = model.getSyncTime();
				logger.info("定时器st_statByMin执行开始时间段"+syncTime);

				logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始执行半小时统计任务,上次同步时间:"+syncTime+",当前时间:"+currentTime);

				if(null == syncTime || "".equals(syncTime)) {
					Calendar cal = Calendar.getInstance();
					int min = cal.get(Calendar.MINUTE);
					if(min <= 30)
						syncTime = DateUtil.addHour(fmt, currentTime, -1).substring(0, 15)  + "30";
					else
						syncTime = currentTime.substring(0, 15)  + "00";				
				}		

				//系统按半小时的批次，如果要同时计算多个半小时的数据时，并行计算太慢，可以采用多线程处理；所有线程执行完成后再返回，设置同步时间
				final String latestSyncTime = syncTime;
				int count = DateUtil.bwMinsWithCurrdate(latestSyncTime + ":00") / 30;	
				
				//用于控制所有线程执行完成后，主线程才继续往下走
				CountDownLatch cdl = new CountDownLatch(count);
				
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行半小时统计任务,处理截止时间:"+syncTime+",处理批次:"+count);
				
				for(int i = 0; i < count; i++) {
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行半小时统计任务,开始处理第:["+i+"]批次统计.");
					
					int i_ = i;
					new Thread(){
						public void run() {
							try {
								String preTime = DateUtil.addMinute(fmt, latestSyncTime, 30 * (i_ - 1));
								String beginTime = DateUtil.addMinute(fmt, latestSyncTime, 30 * i_);
								String endTime = DateUtil.addMinute(fmt, latestSyncTime, 30 * (i_+1));
								
								logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行半小时统计任务,执行第:["+i_+"]批次统计,preTime="+preTime+",beginTime="+beginTime+",endTime="+endTime);
								
								AgentService agentService = new AgentService(beginTime.substring(0, 10));
								
								agentService.stAgentSkill(Constants.ST_TYPE_MINUTE, preTime + ":00", beginTime + ":00");
								
								agentService.stAgentSatisDetail(Constants.ST_TYPE_MINUTE, preTime + ":00", beginTime + ":00");
								agentService.cleanStIds();
								
								agentService.stAgentCall(Constants.ST_TYPE_MINUTE, beginTime, endTime);
								
								QueueService queueService = new QueueService();
								
								queueService.stQueueCall(Constants.ST_TYPE_MINUTE, beginTime, endTime);
								
								queueService.stQueueServiceCall(Constants.ST_TYPE_MINUTE, beginTime, endTime);
								
								ChannelService channelService = new ChannelService();
								logger.info("定时器执行stChannelSkillLevel,preTime:"+preTime+";beginTime:"+beginTime);
								channelService.stChannelSkillLevel(Constants.ST_TYPE_MINUTE, preTime + ":00", beginTime + ":00");
							} catch (Exception e) {
								
							}finally {
								cdl.countDown(); //执行中的线程减1
								logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行半小时统计任务,线程处理完成,第:["+i_+"]批次.");
							}
						};
					}.start();
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行半小时统计任务,结束处理第:["+i+"]批次统计.");
				}
				
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行半小时统计任务,所有线程分派完成，等待所有线程执行完成.");
				//等待所有线程执行完成
				cdl.await();
				
				syncTime = DateUtil.addMinute(fmt, syncTime, 30 * count);
				model.setSyncTime(syncTime);
				SyncService.updateSyncTime(model);
				
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行半小时统计任务,处理完成，同步时间设置为:"+syncTime);
				
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "按半小时对数据进行统计失败.", e);
			}
		}
		else if("STATGW_STAT_AT10".equals(command)){
			try {
				String currentDate = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);				
				SyncInfoModel model = SyncService.getSyncInfo("st_statAt10");		
				String startDate = model.getSyncTime();		
				logger.info("定时器st_statAt10执行开始时间段"+startDate);

				AgentService agentService = new AgentService(startDate);
				agentService.stAgentJX(startDate);

				if(null == startDate || "".equals(startDate))
					startDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, currentDate, -7);
				else 
					startDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, -6);
				
				int count = DateUtil.bwDays(startDate, currentDate, DateUtil.TIME_FORMAT_YMD);
				for(int i = 0; i < count; i++) {
					String stDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, i);

					agentService = new AgentService(stDate);
					agentService.stAgentZx(stDate);
				}
				model.setSyncTime(DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, count));
				SyncService.updateSyncTime(model);
				
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "每天10点对数据进行统计失败.", e);
			}
		}	
		else if("STATGW_STAT_BF180".equals(command)){
			try {
				String currentDate = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);				
				SyncInfoModel model = SyncService.getSyncInfo("st_statBf180");		
				String endDate = model.getSyncTime();
				String startDate = "";

				if(null == endDate || "".equals(endDate))
					startDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, currentDate, -180);
				else 
					startDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, endDate, -179);
				
				DivisionService divisionService = new DivisionService();
				divisionService.stDivisionOrder(startDate, endDate);
				
				model.setSyncTime(currentDate);
				SyncService.updateSyncTime(model);
				
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "每天10点对数据进行统计失败.", e);
			}
		} else if("STATGW_STAT_JX30".equals(command)){
			try {
				String currentDate = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
				SyncInfoModel model = SyncService.getSyncInfo("st_statJX30");
				String startDate = model.getSyncTime();

				if(StringUtils.isBlank(startDate))
					startDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, currentDate, -30);
				else
					startDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, -29);

				int count = DateUtil.bwDays(startDate, currentDate, DateUtil.TIME_FORMAT_YMD);
				for(int i = 0; i < count; i++) {
					String stDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, i);

					AgentService agentService = new AgentService(stDate);
					agentService.stAgentJXZXHistoryData(stDate);
				}

				model.setSyncTime(currentDate);
				SyncService.updateSyncTime(model);

			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "每天10点对数据进行统计失败.", e);
			}
		}
		else if("TEST".equals(command)) {
			try {		
				logger.info(CommonUtil.getClassNameAndMethod(this) + Constants.ZX_TYPE_OF_TRAIN);	
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "测试统计失败.", e);
			}
		}
		else if ("STATGW_STAT_NEW".equals(command)) {
			try {
				logger.info(CommonUtil.getClassNameAndMethod(this) + "执行新统计任务");
				String startDate = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
				int synchronizationDays = -Constants.SYNCHRONIZATION_DAYS;
				String stDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, synchronizationDays);
				QueueService queueService = new QueueService();
				queueService.stQueueNew(stDate);
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "执行新统计任务失败.", e);
			}
		}
		return null;
	}

	private List<Runnable> initDateTask(String stDate, String nextDate, AtomicBoolean next) {
		List<Runnable> tasks = new ArrayList<>();
		tasks.add(() -> {
			try {
				LocalDateTime now1 = LocalDateTime.now();
				AgentService agentService = new AgentService(stDate);
				agentService.stAgentListenRate(stDate);
				agentService.stAgentMediaJX(stDate);
				agentService.cleanStIds();
				agentService.stAgentQd(stDate);
				agentService.stAgentSkill(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
				agentService.stAgentSatisDetail(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
				agentService.stAgentTime(stDate);
				agentService.stAgentComplaint(stDate);
				agentService.stAgentCallOutNum(stDate);
				agentService.stAgentOther(stDate);
				agentService.stAgentCall(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
				agentService.stAgentNoCall(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
				agentService.stAgentMediaDuty(stDate);
				agentService.stAgentJX(stDate);
				LocalDateTime end1 = LocalDateTime.now();
				Duration duration1 = Duration.between(now1, end1);
				long milliseconds1 = duration1.toMillis();
				loggerTime.info("按天对数据进行统计(第一部分)耗时："+milliseconds1+"ms");
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "按天对数据进行统计失败(第一部分).", e);
			}
		});
		tasks.add(() -> {
			try {
				LocalDateTime now2 = LocalDateTime.now();
				QcAgentService qcAgentService = new QcAgentService(stDate);
				qcAgentService.stQcAgentQd(stDate);
				LocalDateTime end2 = LocalDateTime.now();
				Duration duration2 = Duration.between(now2, end2);
				long milliseconds2 = duration2.toMillis();
				loggerTime.info("按天对数据进行统计(第二部分)耗时："+milliseconds2+"ms");
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "按天对数据进行统计失败(第二部分).", e);
			}
		});
		tasks.add(() -> {
			try {
				LocalDateTime now3 = LocalDateTime.now();
				AreaService areaService = new AreaService();
				areaService.stAreaQd(stDate);
				areaService.stAreaRZ(stDate);
				areaService.stAreaLy(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
				LocalDateTime end3 = LocalDateTime.now();
				Duration duration3 = Duration.between(now3, end3);
				long milliseconds3 = duration3.toMillis();
				loggerTime.info("按天对数据进行统计(第三部分)耗时："+milliseconds3+"ms");
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "按天对数据进行统计失败(第三部分).", e);
			}
		});
		tasks.add(() -> {
			try {
				LocalDateTime now4 = LocalDateTime.now();
				DeptService deptService = new DeptService();
				deptService.stDeptQc(stDate);
				LocalDateTime end4 = LocalDateTime.now();
				Duration duration4 = Duration.between(now4, end4);
				long milliseconds4 = duration4.toMillis();
				loggerTime.info("按天对数据进行统计(第四部分)耗时："+milliseconds4+"ms");
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "按天对数据进行统计失败(第四部分).", e);
			}
		});
		tasks.add(() -> {
			try {
				LocalDateTime now5 = LocalDateTime.now();
				QueueService queueService = new QueueService();
				queueService.stQueueCall(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
				queueService.stQueueServiceCall(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
				queueService.stQueueTransform(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
				LocalDateTime end5 = LocalDateTime.now();
				Duration duration5 = Duration.between(now5, end5);
				long milliseconds5 = duration5.toMillis();
				loggerTime.info("按天对数据进行统计(第五部分)耗时："+milliseconds5+"ms");
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "按天对数据进行统计失败(第五部分).", e);
			}
		});
		tasks.add(() -> {
			try {
				LocalDateTime now6 = LocalDateTime.now();
				SyncIvrinfoService service = new SyncIvrinfoService();
				service.doSync(stDate +" 00:00:00", stDate +" 23:59:59");
				LocalDateTime end6 = LocalDateTime.now();
				Duration duration6 = Duration.between(now6, end6);
				long milliseconds6 = duration6.toMillis();
				loggerTime.info("按天对数据进行统计(第六部分)耗时："+milliseconds6+"ms");
				next.set(true);
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "按天对数据进行统计失败(第六部分).", e);
			}
		});
		tasks.add(() -> {
			try {
				LocalDateTime now6 = LocalDateTime.now();
				ChannelService channelService = new ChannelService();
				channelService.stChannelSkillLevel(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
				LocalDateTime end6 = LocalDateTime.now();
				Duration duration6 = Duration.between(now6, end6);
				long milliseconds6 = duration6.toMillis();
				loggerTime.info("按天对数据进行统计(第七部分)耗时："+milliseconds6+"ms");
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "按天对数据进行统计失败(第七部分).", e);
			}
		});
		return tasks;
	}

	public static void main(String[] args) throws SQLException {
		String startDate="2023-01-30 11:15";
		String fmt = "yyyy-MM-dd HH:mm";
		String currentTime = DateUtil.getCurrentDateStr(fmt);	
		Calendar cal = Calendar.getInstance();
		int min = cal.get(Calendar.MINUTE);
		String syncTime;
		if(min <= 30)
			 syncTime = DateUtil.addHour(fmt, currentTime, -1).substring(0, 15)  + "30";
		else
			syncTime = currentTime.substring(0, 15)  + "00";		
	
		System.out.println(syncTime);
		String stDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, startDate, -1);
		System.out.println(stDate);
	}
}
