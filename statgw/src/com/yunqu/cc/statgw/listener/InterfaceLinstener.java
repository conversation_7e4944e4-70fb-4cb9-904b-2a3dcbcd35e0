package com.yunqu.cc.statgw.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yq.busi.common.base.ServiceID;
import com.yunqu.cc.statgw.base.Constants;


@WebListener
public class InterfaceLinstener extends ServiceContextListener{
	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		
		//处理数据统计的接口
		ServiceResource resource = new ServiceResource();
		resource.appName = Constants.APP_NAME;
		resource.className = "com.yunqu.cc.statgw.service.InterfaceService";
		resource.description = "处理数据统计相关的接口";
		resource.serviceId = ServiceID.STATGW_INTEFACE;
		resource.serviceName = "处理数据统计的接口";
		list.add(resource);

		ServiceResource resource2 = new ServiceResource();
		resource2.appName = Constants.APP_NAME;
		resource2.className = "com.yunqu.cc.statgw.service.SynTracesJobService"	;
		resource2.description = "同步交流轨迹数据服务类";
		resource2.serviceId = "SYN_TRACES_SERVICE";
		resource2.serviceName = "同步交流轨迹数据服务类";
		list.add(resource2);

		return list;
	}

}
