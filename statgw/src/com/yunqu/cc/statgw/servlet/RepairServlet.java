package com.yunqu.cc.statgw.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.cc.statgw.base.AbstractTracesService;
import com.yunqu.cc.statgw.base.AppBaseServlet;
import com.yunqu.cc.statgw.base.CommonLogger;
import com.yunqu.cc.statgw.base.Constants;
import com.yunqu.cc.statgw.factory.TracesServiceFactory;
import com.yunqu.cc.statgw.service.*;
import com.yunqu.cc.statgw.thread.ThreadPoolManage;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;

import javax.servlet.annotation.WebServlet;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.statgw.model.Agent;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

@WebServlet("/servlet/repair/*")
public class RepairServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;
    // 修复历史数据用
    public static Logger logger = CommonLogger.logger;
    public static Logger loggerTime = CommonLogger.getLogger("repair_time");
    public static Logger loggerTraces = CommonLogger.getLogger("repair_traces");
    public EasyCache cache = CacheManager.getMemcache();
    /*
     * public EasyCache cache = CacheManager.getMemcache();
     *
     * public EasyResult actionForHour(){
     * cache.delete("st_statByHour");
     * return EasyResult.ok();
     * }
     *
     * public EasyResult actionForDay(){
     * cache.delete("st_statByDay");
     * return EasyResult.ok();
     * }
     * public EasyResult actionForMIN(){
     * cache.delete("st_statByMin");
     * return EasyResult.ok();
     * }
     */

    // public EasyResult actionForDay(){
    // String key = this.getPara("key");
    // cache.delete(key);
    // return EasyResult.ok();
    // }
    public EasyResult actionForRepairAgentCall(){
        String fmt = "yyyy-MM-dd HH:mm";
        //系统按半小时的批次，如果要同时计算多个半小时的数据时，并行计算太慢，可以采用多线程处理；所有线程执行完成后再返回，设置同步时间
        String startTime = getPara("startTime"); // "2017-01-01 00:00"
        String endTime = getPara("endTime");
        String stType = getPara("stType");
        int bw;
        String stTypeName;
        if (Constants.ST_TYPE_MINUTE.equals(stType)){
            bw = 30;
            stTypeName = "按半小时";
        }else if (Constants.ST_TYPE_HOUR.equals(stType)){
            bw = 60;
            stTypeName = "按小时";
        }else if (Constants.ST_TYPE_DAY.equals(stType)){
            bw = 24 * 60;
            stTypeName = "按天";
        }else {
            return EasyResult.fail("stType参数错误");
        }

        int count = bwMinsWithCurrdate(startTime + ":00",endTime+":59") / bw;
        if (Constants.ST_TYPE_DAY.equals(stType)){
            count = DateUtil.bwDays(startTime, endTime ,fmt);
        }

        try {
            //用于控制所有线程执行完成后，主线程才继续往下走
            CountDownLatch cdl = new CountDownLatch(count);

            logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行"+stTypeName+"统计任务,处理截止时间:"+startTime+",处理批次:"+count);

            for(int i = 0; i < count; i++) {
                logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行"+stTypeName+"统计任务,开始处理第:["+i+"]批次统计.");

                int i_ = i;
                new Thread(){
                    public void run() {
                        try {
                            String preTime = DateUtil.addMinute(fmt, startTime, bw * (i_ - 1));
                            String beginTime = DateUtil.addMinute(fmt, startTime, bw * i_);
                            String endTime = DateUtil.addMinute(fmt, startTime, bw * (i_+1));

                            logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行"+stTypeName+"统计任务,执行第:["+i_+"]批次统计,preTime="+preTime+",beginTime="+beginTime+",endTime="+endTime);

                            AgentService agentService = new AgentService(beginTime.substring(0, 10));
                            agentService.stAgentCall(stType, beginTime, endTime);
                            logger.info("定时器执行stChannelSkillLevel,preTime:"+preTime+";beginTime:"+beginTime);
                        } catch (Exception e) {
                            logger.error(CommonUtil.getClassNameAndMethod(this) + "按半小时对数据进行统计失败.", e);
                        } finally {
                            cdl.countDown(); //执行中的线程减1
                            logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行"+stTypeName+"统计任务,线程处理完成,第:["+i_+"]批次.");
                        }
                    };
                }.start();
                logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行"+stTypeName+"统计任务,结束处理第:["+i+"]批次统计.");
            }

            logger.info(CommonUtil.getClassNameAndMethod(this)+" 执行"+stTypeName+"统计任务,所有线程分派完成，等待所有线程执行完成.");
            //等待所有线程执行完成
            cdl.await();

            return EasyResult.ok();
        }catch (Exception e){
            logger.error(CommonUtil.getClassNameAndMethod(this) + "执行"+stTypeName+"对数据进行统计失败.", e);
            return EasyResult.fail(e.getMessage());
        }
    }

    public static int bwMinsWithCurrdate(String startTime,String endTime) {
        if(org.apache.commons.lang3.StringUtils.isNotBlank(startTime)){
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                Date date = df.parse(endTime);
                Date ctime = df.parse(startTime);
                long bw = date.getTime()-ctime.getTime();
                return (int)(bw/1000/60);
            } catch (Exception e) {
            }
        }
        return 0;
    }
    public EasyResult actionForRepairAgentService() {
        String stDate = this.getPara("stDate");
        if (stDate == null || "".equals(stDate)) {
            return EasyResult.error(500, stDate);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        LocalDateTime now = LocalDateTime.now();
        loggerTime.info("修复执行开始时间" + now.format(formatter));
        logger.info("执行修复数据" + stDate);
        String nextDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, stDate, 1);
        CountDownLatch latch = new CountDownLatch(6);
        ThreadPoolManage.getThreadPool().execute(() -> {
            try {
                LocalDateTime now1 = LocalDateTime.now();
                AgentService agentService = new AgentService(stDate);
                agentService.stAgentListenRate(stDate);
                agentService.stAgentMediaJX(stDate);
                agentService.cleanStIds();
                agentService.stAgentQd(stDate);
                agentService.stAgentSkill(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
                agentService.stAgentSatisDetail(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
                agentService.stAgentTime(stDate);
                agentService.stAgentComplaint(stDate);
                agentService.stAgentCallOutNum(stDate);
                agentService.stAgentOther(stDate);
                agentService.stAgentCall(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
                agentService.stAgentNoCall(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
                agentService.stAgentMediaDuty(stDate);
                LocalDateTime end1 = LocalDateTime.now();
                Duration duration1 = Duration.between(now1, end1);
                long milliseconds1 = duration1.toMillis();
                loggerTime.info("修复按天对数据进行统计(第一部分)耗时：" + milliseconds1 + "ms");
            } catch (Exception e) {
                loggerTime.error(CommonUtil.getClassNameAndMethod(this) + "修复按天对数据进行统计失败(第一部分).", e);
            } finally {
                latch.countDown();
            }
        });
        ThreadPoolManage.getThreadPool().execute(() -> {
            try {
                LocalDateTime now2 = LocalDateTime.now();
                QcAgentService qcAgentService = new QcAgentService(stDate);
                qcAgentService.stQcAgentQd(stDate);
                LocalDateTime end2 = LocalDateTime.now();
                Duration duration2 = Duration.between(now2, end2);
                long milliseconds2 = duration2.toMillis();
                loggerTime.info("修复按天对数据进行统计(第二部分)耗时：" + milliseconds2 + "ms");
            } catch (Exception e) {
                loggerTime.error(CommonUtil.getClassNameAndMethod(this) + "修复按天对数据进行统计失败(第二部分).", e);
            } finally {
                latch.countDown();
            }
        });
        ThreadPoolManage.getThreadPool().execute(() -> {
            try {
                LocalDateTime now3 = LocalDateTime.now();
                AreaService areaService = new AreaService();
                areaService.stAreaQd(stDate);
                areaService.stAreaRZ(stDate);
                areaService.stAreaLy(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
                LocalDateTime end3 = LocalDateTime.now();
                Duration duration3 = Duration.between(now3, end3);
                long milliseconds3 = duration3.toMillis();
                loggerTime.info("修复按天对数据进行统计(第三部分)耗时：" + milliseconds3 + "ms");
            } catch (Exception e) {
                loggerTime.error(CommonUtil.getClassNameAndMethod(this) + "修复按天对数据进行统计失败(第三部分).", e);
            } finally {
                latch.countDown();
            }
        });
        ThreadPoolManage.getThreadPool().execute(() -> {
            try {
                LocalDateTime now4 = LocalDateTime.now();
                DeptService deptService = new DeptService();
                deptService.stDeptQc(stDate);
                LocalDateTime end4 = LocalDateTime.now();
                Duration duration4 = Duration.between(now4, end4);
                long milliseconds4 = duration4.toMillis();
                loggerTime.info("修复按天对数据进行统计(第四部分)耗时：" + milliseconds4 + "ms");
            } catch (Exception e) {
                loggerTime.error(CommonUtil.getClassNameAndMethod(this) + "修复按天对数据进行统计失败(第四部分).", e);
            } finally {
                latch.countDown();
            }
        });
        ThreadPoolManage.getThreadPool().execute(() -> {
            try {
                LocalDateTime now5 = LocalDateTime.now();
                QueueService queueService = new QueueService();
                queueService.stQueueCall(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
                queueService.stQueueServiceCall(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
                queueService.stQueueTransform(Constants.ST_TYPE_DAY, stDate + " 00:00", nextDate + " 00:00");
                queueService.stQueueNew(stDate);
                LocalDateTime end5 = LocalDateTime.now();
                Duration duration5 = Duration.between(now5, end5);
                long milliseconds5 = duration5.toMillis();
                loggerTime.info("修复按天对数据进行统计(第五部分)耗时：" + milliseconds5 + "ms");
            } catch (Exception e) {
                loggerTime.error(CommonUtil.getClassNameAndMethod(this) + "修复按天对数据进行统计失败(第五部分).", e);
            } finally {
                latch.countDown();
            }
        });
        ThreadPoolManage.getThreadPool().execute(() -> {
            try {
                LocalDateTime now6 = LocalDateTime.now();
                ChannelService channelService = new ChannelService();
                channelService.stChannelSkillLevel(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
                LocalDateTime end6 = LocalDateTime.now();
                Duration duration6 = Duration.between(now6, end6);
                long milliseconds6 = duration6.toMillis();
                loggerTime.info("修复按天对数据进行统计(第六部分)耗时：" + milliseconds6 + "ms");
            } catch (Exception e) {
                loggerTime.error(CommonUtil.getClassNameAndMethod(this) + "修复按天对数据进行统计失败(第六部分).", e);
            } finally {
                latch.countDown();
            }
        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            loggerTime.error(CommonUtil.getClassNameAndMethod(this) + "修复按天对数据进行统计失败(等待线程结束).", e);
        }
        LocalDateTime end = LocalDateTime.now();
        Duration duration = Duration.between(now, end);
        long milliseconds = duration.toMillis();
        loggerTime.info("定时器执行结束时间" + end.format(formatter));
        loggerTime.info("按天对数据进行统计总耗时：" + milliseconds + "ms");
        logger.info("修复数据完成" + stDate);

        return EasyResult.ok();
    }

    public EasyResult actionForRepairQueueServiceByHour() {
        String stDate = this.getPara("stDate");
        logger.info("执行修复数据" + stDate);
        if (stDate == null || "".equals(stDate)) {
            return EasyResult.error(500, "参数 stDate 不能为空");
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date date = sdf.parse(stDate);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            QueueService queueService = new QueueService();
            for (int i = 0; i < 24; i++) {
                String now = sdf2.format(calendar.getTime());
                calendar.add(Calendar.HOUR_OF_DAY, 1);
                String next = sdf2.format(calendar.getTime());
                queueService.stQueueCall(Constants.ST_TYPE_HOUR, now, next);
            }
            logger.info("修复数据完成" + stDate);
            return EasyResult.ok();
        } catch (Exception e) {
            logger.error("修复数据失败: " + e.getMessage(), e);
            return EasyResult.error(500, "修复数据失败: " + e.getMessage());
        }
    }

    public EasyResult actionForRepairQueueServiceByMinute() {
        String stDate = this.getPara("stDate");
        logger.info("执行修复数据" + stDate);
        if (stDate == null || "".equals(stDate)) {
            return EasyResult.error(500, "参数 stDate 不能为空");
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            Date date = sdf.parse(stDate);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);

            QueueService queueService = new QueueService();
            for (int i = 0; i < 48; i++) {
                String now = sdf2.format(calendar.getTime());
                calendar.add(Calendar.MINUTE, 30);
                String next = sdf2.format(calendar.getTime());
                queueService.stQueueCall(Constants.ST_TYPE_MINUTE, now, next);
            }
            logger.info("修复数据完成" + stDate);
            return EasyResult.ok();
        } catch (Exception e) {
            logger.error("修复数据失败: " + e.getMessage(), e);
            return EasyResult.error(500, "修复数据失败: " + e.getMessage());
        }
    }

    public EasyResult actionForRepairAgent() {
        String stDate = this.getPara("stDate");
        if (stDate == null || "".equals(stDate)) {
            return EasyResult.error(500, stDate);
        }
        logger.info("执行修复数据" + stDate);
        String nextDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, stDate, 1);

        ChannelService channelService = new ChannelService();
        logger.info("定时器执行stChannelSkillLevel" + stDate);
        channelService.stChannelSkillLevel(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");

        logger.info("修复数据完成" + stDate);

        return EasyResult.ok();
    }

    public EasyResult actionForRepairStQueueNew() {
        String stDate = this.getPara("stDate");
        if (stDate == null || "".equals(stDate)) {
            return EasyResult.error(500, stDate);
        }
        QueueService queueService = new QueueService();
        queueService.stQueueNew(stDate);
        logger.info("修复数据完成" + stDate);
        return EasyResult.ok();
    }

    public EasyResult actionForRepairStIvr() {
        String stDate = this.getJSONObject().getString("stDate");
        if (stDate == null || "".equals(stDate)) {
            return EasyResult.error(500, stDate);
        }
        SyncIvrinfoService service = new SyncIvrinfoService();
        service.doSync(stDate + " 00:00:00", stDate + " 23:59:59");
        SyncIvrinfoService.virInfoStat(stDate);
        logger.info("修复数据完成" + stDate);
        return EasyResult.ok();
    }

    public EasyResult actionForRepairStAreaRZ() {
        String stDate = this.getPara("stDate");
        if (stDate == null || "".equals(stDate)) {
            return EasyResult.error(500, stDate);
        }
        AreaService service = new AreaService();
        service.stAreaRZ(stDate);
        logger.info("修复数据完成" + stDate);
        return EasyResult.ok();
    }

    public EasyResult actionForRepairAgentReport() {
        String stDate = this.getPara("stDate");
        if (stDate == null || "".equals(stDate)) {
            return EasyResult.error(500, stDate);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        LocalDateTime now = LocalDateTime.now();
        loggerTime.info("修复执行开始时间" + now.format(formatter));
        logger.info("执行修复数据" + stDate);
        String nextDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, stDate, 1);
        try {
            LocalDateTime now1 = LocalDateTime.now();
            AgentService agentService = new AgentService(stDate);
            agentService.stAgentSkill(Constants.ST_TYPE_DAY, stDate + " 00:00:00", nextDate + " 00:00:00");
            LocalDateTime end1 = LocalDateTime.now();
            Duration duration1 = Duration.between(now1, end1);
            long milliseconds1 = duration1.toMillis();
            loggerTime.info("修复按天对数据进行统计(第一部分)耗时：" + milliseconds1 + "ms");
        } catch (Exception e) {
            loggerTime.error(CommonUtil.getClassNameAndMethod(this) + "修复按天对数据进行统计失败(第一部分).", e);
        }
        LocalDateTime end = LocalDateTime.now();
        Duration duration = Duration.between(now, end);
        long milliseconds = duration.toMillis();
        loggerTime.info("定时器执行结束时间" + end.format(formatter));
        loggerTime.info("按天对数据进行统计总耗时：" + milliseconds + "ms");
        logger.info("修复数据完成" + stDate);

        return EasyResult.ok();
    }

    public EasyResult actionForRepairAgentJx() {
        String stDate = this.getPara("stDate");
        if (stDate == null || "".equals(stDate)) {
            return EasyResult.error(500, stDate);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        LocalDateTime now = LocalDateTime.now();
        loggerTime.info("修复执行开始时间" + now.format(formatter));
        logger.info("执行修复数据" + stDate);
        try {
            LocalDateTime now1 = LocalDateTime.now();
            AgentService agentService = new AgentService(stDate);
            agentService.stAgentZx(stDate);
            agentService.stAgentJX(stDate);
            LocalDateTime end1 = LocalDateTime.now();
            Duration duration1 = Duration.between(now1, end1);
            long milliseconds1 = duration1.toMillis();
            loggerTime.info("修复按天对数据进行统计(第一部分)耗时：" + milliseconds1 + "ms");
        } catch (Exception e) {
            loggerTime.error(CommonUtil.getClassNameAndMethod(this) + "修复按天对数据进行统计失败(第一部分).", e);
        }
        LocalDateTime end = LocalDateTime.now();
        Duration duration = Duration.between(now, end);
        long milliseconds = duration.toMillis();
        loggerTime.info("定时器执行结束时间" + end.format(formatter));
        loggerTime.info("按天对数据进行统计总耗时：" + milliseconds + "ms");
        logger.info("修复数据完成" + stDate);

        return EasyResult.ok();
    }

    public EasyResult actionForRepairAgentJxHistory() {
        String stDate = this.getPara("stDate");
        if (stDate == null || "".equals(stDate)) {
            return EasyResult.error(500, stDate);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
        LocalDateTime now = LocalDateTime.now();
        loggerTime.info("修复执行开始时间" + now.format(formatter));
        logger.info("执行修复数据" + stDate);
        try {
            LocalDateTime now1 = LocalDateTime.now();
            AgentService agentService = new AgentService(stDate);
            agentService.stAgentJXZXHistoryData(stDate);
            LocalDateTime end1 = LocalDateTime.now();
            Duration duration1 = Duration.between(now1, end1);
            long milliseconds1 = duration1.toMillis();
            loggerTime.info("修复按天对数据进行统计(第一部分)耗时：" + milliseconds1 + "ms");
        } catch (Exception e) {
            loggerTime.error(CommonUtil.getClassNameAndMethod(this) + "修复按天对数据进行统计失败(第一部分).", e);
        }
        LocalDateTime end = LocalDateTime.now();
        Duration duration = Duration.between(now, end);
        long milliseconds = duration.toMillis();
        loggerTime.info("定时器执行结束时间" + end.format(formatter));
        loggerTime.info("按天对数据进行统计总耗时：" + milliseconds + "ms");
        logger.info("修复数据完成" + stDate);

        return EasyResult.ok();
    }

    public JSONObject actionForRepairTraces() {
        String beginTime = this.getPara("beginTime");
        String endTime = this.getPara("endTime");
        String command = this.getPara("command");

        // 通过工厂获取服务实例并执行同步
        AbstractTracesService service = TracesServiceFactory.getService(command);
        if (service == null) {
            JSONObject result = JsonUtil.createInfRespJson(new JSONObject());
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "error：获取服务实例失败！");
            logger.error(CommonUtil.getClassNameAndMethod(this) + " 获取服务实例失败,请检查:" + command);
            return result;
        }
        logger.info("修复数据完成" + beginTime + " " + endTime);
        return service.executeSync(beginTime, endTime);
    }

    public JSONObject actionForDeleteTracesData() {
        String commandStr = this.getPara("commands");
        try {
            List<String> commands;
            if (commandStr != null && !commandStr.isEmpty()) {
                commands = Arrays.asList(commandStr.split(","));
            } else {
                commands = Arrays.asList("AgentCall", "AgentUnCall", "RobotCall", "OutBoundConnected",
                        "OutBoundUnConnected", "OutBoundConnectedAi", "OnlineUnCall", "OnlineCall");
            }
            String commandIn = commands.stream().map(command -> "'" + command + "'").collect(Collectors.joining(","));
            EasySQL sql = new EasySQL();
            sql.append("DELETE FROM C_PF_TRACES_SYNC_INFO WHERE CHANNEL_TYPE IN (" + commandIn + ")");
            getQuery().execute(sql.getSQL(), sql.getParams());
            logger.info("删除交流轨迹执行时间段记录成功");
        } catch (Exception e) {
            logger.error("删除交流轨迹执行时间段记录失败: " + e.getMessage(), e);
            return EasyResult.fail("删除交流轨迹执行时间段记录失败");
        }
        return EasyResult.ok();

    }

    public JSONObject actionForRepairAllTraces() {
        String beginDate = this.getPara("beginDate");
        String endDate = this.getPara("endDate");
        String commandStr = this.getPara("commands");

        if (beginDate == null || beginDate.isEmpty() || endDate == null || endDate.isEmpty()) {
            loggerTraces.error("开始时间或结束时间不能为空");
            JSONObject result = JsonUtil.createInfRespJson(new JSONObject());
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "开始时间或结束时间不能为空");
            return result;
        }

        List<String> commands;
        if (commandStr != null && !commandStr.isEmpty()) {
            commands = Arrays.asList(commandStr.split(","));
        } else {
            commands = Arrays.asList("AgentCall", "AgentUnCall", "RobotCall", "OutBoundConnected",
                    "OutBoundUnConnected", "OutBoundConnectedAi", "OnlineUnCall", "OnlineCall");
        }

        String threadName = Thread.currentThread().getName();
        loggerTraces.info(
                "【" + threadName + "】修复交流轨迹数据开始 >>>>> 开始时间：" + beginDate + "，结束时间：" + endDate + "，修复类型：" + commands
                        + "。");

        ExecutorService executorService = null;

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 解析开始和结束时间
            Date startDate = sdf.parse(beginDate);
            Date endDate2 = sdf.parse(endDate);

            // 获取当前时间，用于比较时间范围
            Date currentDate = new Date();

            // 如果结束时间超过当前时间，则使用当前时间作为结束时间
            if (endDate2.after(currentDate)) {
                endDate2 = currentDate;
                loggerTraces.info("【" + threadName + "】结束时间超过当前时间，将使用当前时间作为结束时间：" + sdf.format(currentDate));
            }

            // 如果开始时间晚于结束时间，则交换它们
            if (startDate.after(endDate2)) {
                Date temp = startDate;
                startDate = endDate2;
                endDate2 = temp;
                loggerTraces.info("【" + threadName + "】开始时间晚于结束时间，已自动交换");
            }

            // 计算小时间隔
            long diffInMillies = endDate2.getTime() - startDate.getTime();
            long hours = diffInMillies / (60 * 60 * 1000);

            // 如果小时数为0，至少处理1小时
            if (hours < 1) {
                hours = 1;
                loggerTraces.info("【" + threadName + "】时间间隔小于1小时，将处理1小时的数据");
            }

            // 限制最大处理小时数，防止资源过度消耗
            // if (hours > 240) { // 最多处理10天数据
            // hours = 240;
            // loggerTraces.info("【" + threadName + "】时间间隔过长，将限制为最多处理240小时(10天)的数据");
            // }
            //
            // loggerTraces.info("【" + threadName + "】需要处理的时间段共计 " + hours + " 小时");

            // 创建固定大小的线程池，最多5个线程
            executorService = Executors.newFixedThreadPool(5);
            // 创建计数器，用于等待所有任务完成
            CountDownLatch latch = new CountDownLatch((int) hours);

            // 存储所有提交的任务
            List<Future<?>> futures = new ArrayList<>();

            // 设置日历对象为开始时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);

            // 遍历每个小时
            for (int i = 0; i < hours; i++) {
                String now = sdf.format(calendar.getTime());
                calendar.add(Calendar.HOUR_OF_DAY, 1);
                String next = sdf.format(calendar.getTime());

                // 检查时间范围是否超过结束时间
                if (calendar.getTime().after(endDate2)) {
                    next = sdf.format(endDate2);
                }

                // 为每个小时创建一个任务，在线程池中执行
                final String finalNow = now;
                final String finalNext = next;

                Future<?> future = executorService.submit(() -> {
                    try {
                        loggerTraces.info("【" + threadName + "】开始修复" + finalNow + " 到 " + finalNext + " 的交流轨迹数据");
                        for (String command : commands) {
                            try {
                                loggerTraces
                                        .info("【" + threadName + "】开始修复" + finalNow + " 的交流轨迹数据，修复类型：" + command + "。");
                                // 通过工厂获取服务实例并执行同步
                                AbstractTracesService service = TracesServiceFactory.getService(command);
                                if (service == null) {
                                    JSONObject result = JsonUtil.createInfRespJson(new JSONObject());
                                    result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
                                    result.put("respDesc", "error：获取服务实例失败！");
                                    loggerTraces
                                            .error(CommonUtil.getClassNameAndMethod(this) + " 获取服务实例失败,请检查:" + command);
                                    continue;
                                }
                                // 5. 执行业务数据同步
                                deleteByTimeAndChannel(command, finalNow, finalNext);

                                // 只执行一次查询和保存操作，不进行分页
                                List<JSONObject> data = service.queryBusinessData(finalNow, finalNext, 1, 10000);
                                boolean success = true;

                                if (data != null && !data.isEmpty()) {
                                    success = service.saveTracesData(finalNow, finalNext, command, data);
                                    loggerTraces.info("【" + threadName + "】" + finalNow + " 到 " + finalNext
                                            + " 的交流轨迹数据处理，获取到 " + data.size() + " 条记录");
                                } else {
                                    loggerTraces.info("【" + threadName + "】" + finalNow + " 到 " + finalNext
                                            + " 的交流轨迹数据处理，未获取到数据");
                                }

                                loggerTraces.info("【" + threadName + "】" + finalNow + " 到 " + finalNext
                                        + " 的交流轨迹数据修复完成，修复类型：" + command
                                        + "，修复结果：" + JSON.toJSONString(success));
                            } catch (Exception e) {
                                // 捕获单个命令执行的异常，但继续执行其他命令
                                loggerTraces
                                        .error("【" + threadName + "】修复数据失败，时间：" + finalNow + " 到 " + finalNext + "，类型："
                                                + command + ", 错误: " + e.getMessage(), e);
                            }
                        }
                    } catch (Exception e) {
                        loggerTraces.error("【" + threadName + "】修复数据失败: " + e.getMessage(), e);
                    } finally {
                        latch.countDown();
                    }
                });

                futures.add(future);
            }

            // 等待所有任务完成，但设置最大等待时间防止永久阻塞
            boolean completed = latch.await(Math.max(hours * 10, 600), TimeUnit.SECONDS); // 最少等待10分钟，每小时最多等待10秒

            if (!completed) {
                loggerTraces.warn("【" + threadName + "】部分任务未在规定时间内完成，将继续执行");
            }

            // 检查是否有任务出现异常
            for (Future<?> future : futures) {
                if (future.isDone() && !future.isCancelled()) {
                    try {
                        future.get(1, TimeUnit.MILLISECONDS); // 快速检查是否有异常
                    } catch (ExecutionException e) {
                        loggerTraces.error("【" + threadName + "】任务执行异常: " + e.getCause().getMessage(), e.getCause());
                    } catch (TimeoutException e) {
                        // 忽略超时异常，因为我们只是想快速检查
                    }
                }
            }

            loggerTraces.info("【" + threadName + "】所有交流轨迹数据修复任务已完成");
            return EasyResult.ok();
        } catch (Exception e) {
            loggerTraces.error(CommonUtil.getClassNameAndMethod(this) + " 修复数据失败,请检查:" + e.getMessage(), e);
            return EasyResult.fail(e.getMessage());
        } finally {
            // 确保线程池被关闭
            if (executorService != null && !executorService.isShutdown()) {
                try {
                    // 尝试优雅关闭
                    executorService.shutdown();
                    if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                        // 如果等待超时，则强制关闭
                        loggerTraces.info("【" + threadName + "】线程池未能在30秒内关闭，将强制关闭");
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    // 如果当前线程被中断，则强制关闭线程池
                    loggerTraces.error("【" + threadName + "】关闭线程池时被中断，将强制关闭线程池", e);
                    executorService.shutdownNow();
                    // 重置中断状态
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    /**
     * 删除指定时间段内指定渠道的记录
     *
     * @param channelType 渠道类型
     * @param beginTime   开始时间
     * @param endTime     结束时间
     * @return 是否成功
     */
    protected void deleteByTimeAndChannel(String channelType, String beginTime, String endTime) {
        try {
            EasySQL sql = new EasySQL();
            sql.append("DELETE FROM C_PF_TRACES_RECORD WHERE CHANNEL_TYPE = ? AND BEGIN_TIME >= ? AND BEGIN_TIME <= ?");
            getQuery().execute(sql.getSQL(), new Object[] { channelType, beginTime, endTime });
        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + "删除交流轨迹记录失败: " + e.getMessage(), e);
        }
    }

    public List<String> getDateRange(String beginDate, String endDate) throws ParseException {
        List<String> dateList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Date start = sdf.parse(beginDate);
        Date end = sdf.parse(endDate);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(start);

        while (!calendar.getTime().after(end)) {
            dateList.add(sdf.format(calendar.getTime()));
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }

        return dateList;
    }

	public EasyResult actionForRepairStAgentMediaDuty(){
		try{
			String stDate = this.getPara("stDate");
			if(StringUtils.isBlank(stDate)){
				return EasyResult.fail("没有传入 stDate");
			}
			long start = System.currentTimeMillis();
			AgentService agentService = new AgentService(stDate);
			agentService.stAgentMediaDuty(stDate);
			long end = System.currentTimeMillis();
			logger.info("修复 全媒体客服工作情况报表 成功,stDate: " + stDate);
			JSONObject res = new JSONObject();
			res.put("statDate",stDate);
			res.put("time",(end-start)+"ms");
			return EasyResult.ok(res);
		}catch (Exception e){
			logger.error("修复 全媒体客服工作情况报表 数据失败: " + e.getMessage(),e);
			return EasyResult.fail();
		}
	}

	public static void main(String[] args) {
		String stDate="2021-05-20";
		String nextDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, stDate,  1);
		System.out.println(nextDate);

    }
}
