package com.yunqu.cc.leavemsggw.inf;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerFactory;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.cc.leavemsggw.base.CommonLogger;
import com.yunqu.cc.leavemsggw.base.Constants;
import com.yunqu.cc.leavemsggw.job.SyncCallLossTask;
import com.yunqu.cc.leavemsggw.job.SyncLeaveMsgTask;

public class InterfaceService extends IService{

	private Logger logger = CommonLogger.logger;
	
	private  EasyCache cache = CacheManager.getMemcache();
	
	public   String BROKER_USER_NAME = "BROKER_USER_";
	
	public static Map<String,Broker> brokerMap = new HashMap<String,Broker>();
	
	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		
		if(ServiceCommand.SYNC_GENESYS_LEAVEMSG.equals(command)){
			new SyncLeaveMsgTask().run();
			return null;
		}
		
		if(ServiceCommand.SYNC_GENESYS_CALLLOSS.equals(command)){
			new SyncCallLossTask().run();
			return null;
		}
		
		if(ServiceCommand.SYNC_OFFLIINE_MSG.equals(command)){
			
			return null;
		}
		
		if(ServiceCommand.SEND_USER_OFFLIINE_MSG.equals(command)){
			return sendUserMessage(json);
		}
		logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法识别的操作类型:"+command);
		
		JSONObject result = JsonUtil.createInfRespJson(json);
		result.put("respDesc", "不可识别的command");
		return result;
	}

	/**
	 * 给客户主动发送会话内容
	 * @param json
	 * @return
	 */
	private JSONObject sendUserMessage(JSONObject json) {
		JSONObject result = JsonUtil.createInfRespJson(json);
		
		String sessionId = json.getString("sessionId");
		String msg = json.getString("msg");
		String agentNo = json.getString("agentNo"); //坐席工号
		String agentAcc = json.getString("agentAcc"); //坐席账号
		
		String insertRecord = json.getString("insertRecord"); //是否将该条消息写入到会话明细表中，Y-写入，N-不写入  默认为Y
		String chatRecordId = json.getString("chatRecordId");  //当要写入消息到会话明细表时，该字段表示要写入的会话记录id，如果该字段为空，系统会找出客户最近一条会话记录id
		
		String channelKey = json.getString("channelKey"); //渠道
		
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+ "客户主动给用户回复,"+json.toJSONString());
		
		//非空判断
		if(StringUtils.isBlank(sessionId) || StringUtils.isBlank(msg) || StringUtils.isBlank(channelKey)){
			result.put("respDesc", "sessionId、msg、channelKey 不能为空!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复失败,sessionId、msg、channelKey 不能为空!");
			return result;
		}
		
		//离线消息类型 OFFLINE_CHAT_TYPE:  OUT 外部，INNER 内部
		//离线消息外部推送地址 OFFLINE_CHAT_URL
		String offLineChatType = "INNER"; 
		String offLineChatUrl = ""; 
		
		try {
			IService service = ServiceContext.getService(ServiceID.CACHES_INTERFACE);
			if(service!=null){
				JSONObject j = new JSONObject();
				j.put("serviceId", ServiceID.CACHES_INTERFACE);
				j.put("command", "cacheSrhChannel");
				j.put("channelKey", channelKey);
				JSONObject r = service.invoke(j);
				
				logger.info(CommonUtil.getClassNameAndMethod(this)+ "主动会话之前，查询渠道配置信息:"+r.toJSONString());
				if(GWConstants.RET_CODE_SUCCESS.equals(r.getString("respCode"))){
					JSONObject  cfg = r.getJSONObject("channelConf");
					if(cfg!=null){
						offLineChatType = cfg.getString("OFFLINE_CHAT_TYPE");  //主动会话时，是走 内部接口还是外部接口 ，INNER-内部接口
						offLineChatUrl = cfg.getString("OFFLINE_CHAT_URL");  //当走外部接口时，配置外部接口的url
					}
				}
			}
		} catch (ServiceException e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 客户主动给用户回复时，查询缓存中的channel对象出错:"+e.getMessage(),e);
		}
		
		//当走外部接口时，而没有配置url，此时无法发送主动消息
		if(!"INNER".equals(offLineChatType) && StringUtils.isBlank(offLineChatUrl)){
			result.put("respDesc", "没有配置主动会话接口地址!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复失败,没有配置主动会话接口地址!");
			return result;
		}
		
		if("INNER".equals(offLineChatType)){
			//判断客户是否正在接入客服
			String agent = cache.get("MEDIA_USER_AGENT_" + sessionId);
			if(StringUtils.isNotBlank(agent)){
				result.put("respDesc", "该客户正在与坐席["+agent+"]进行咨询!");
				logger.error(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复失败,客户正在与坐席咨询:"+agent);
				return result;
			}

				//获得当前坐席所在的mars上的MQ
			String brokerName =  cache.get(BROKER_USER_NAME+sessionId);
			
			if(StringUtils.isBlank(brokerName)){
				result.put("respDesc", "没找到客户缓存!");
				logger.error(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复失败,brokerName在缓存中不存在:"+brokerName);
				return result;
			}
			
			String addr = AppContext.getContext(Constants.APP_NAME,true).getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
//			String addr = ConfigUtil.getString(Constants.APP_NAME, "ActiveMQ_ADDR", "tcp://10.18.11.118:61616"); 
			
			
			//添加到缓存里，避免出现一周要重启一次mq
			Broker broker =  brokerMap.get(brokerName);
			if(broker==null){
				broker =  BrokerFactory.getProducerQueueBroker(addr,brokerName,"","");
				brokerMap.put(brokerName, broker);
			}
			
			if(broker==null){
				result.put("respDesc", "无法建立会话通道!");
				logger.error(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复失败,无法建立会话通道.");
				return result;
			}
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "客户主动给用户回复过程信息:brokerName="+brokerName+",addr="+addr);
			
			try {
				//msg格式
				/**
				 * {
				    "data": {
				        "msgContent": "下午好、",
				        "msgType": "text",
				        "sessionId": "74fee9cf27f2dd423788cf49cc478f69"
				    },
				    "entId": "1000",
				    "event": "agent",
				    "serialId": "84645513258149697831779",
				    "sign": "",
				    "timestamp": 1535448674185
				}
				 */
				
				JSONObject dataJson = new JSONObject();
				dataJson.put("msgContent", msg);
				dataJson.put("msgType", "text");
				dataJson.put("sessionId", sessionId);
				JSONObject msgJson = new JSONObject();
				msgJson.put("entId", AppContext.getContext(Constants.APP_NAME).getProperty("ENT_ID", "1000"));
				msgJson.put("event", "agent");
				msgJson.put("serialId", IDGenerator.getDefaultNUMID());
				msgJson.put("sign", "");
				msgJson.put("timestamp", System.currentTimeMillis());
				msgJson.put("data", dataJson);
				
				logger.info(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复开始发送:"+msgJson.toJSONString());
				
				broker.sendMessage(msgJson.toJSONString());
				
				//写入会话记录
				insertReocrd(sessionId,msg,insertRecord,chatRecordId,agentNo);
				
				result.put("respDesc", "发送成功!");
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				
				logger.info(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复结束发送,成功!");
			} catch (Exception ex) {
				result.put("respDesc", "发送出现异常!");
				logger.error(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复发送异常:"+ex.getMessage(),ex);
			}
		}else{
			try {
				IService service = ServiceContext.getService("MIXGW_MEDIA_APP_INTEFACE");
				if(service!=null){
					JSONObject j = new JSONObject();
					j.put("serviceId", "MIXGW_MEDIA_APP_INTEFACE");
					j.put("command", "mediaAppPushMsg");
					j.put("channelKey", channelKey);
					j.put("content", msg);
					j.put("agentAcc", agentAcc);
					j.put("url", offLineChatUrl);
					JSONObject r = service.invoke(j);
					if(GWConstants.RET_CODE_SUCCESS.equals(r.getString("respCode"))){
						//写入会话记录
						insertReocrd(sessionId,msg,insertRecord,chatRecordId,agentNo);
						
						result.put("respDesc", "发送成功!");
						result.put("respCode", GWConstants.RET_CODE_SUCCESS);
						
						logger.info(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复结束发送(推送给其他接口),成功!");
					}else{
						result.put("respDesc", "发送失败!");
						result.put("respCode", GWConstants.RET_CODE_SUCCESS);
						
						logger.info(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复结束发送(推送给其他接口) 失败,请求:"+j.toJSONString()+",返回:"+r.toJSONString());
					}
				}
			} catch (ServiceException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 客户主动给用户回复时，查询缓存中的channel对象出错:"+e.getMessage(),e);
			}
		}
		
		return result;
	}

	/**
	 * 将客户聊天内容插入到聊天明细表中
	 * @param sessionId   客户sessionid
	 * @param msg           客户消息
	 * @param insertRecord  是否需要插入到客户明细表
	 * @param chatRecordId  会话记录id
	 * @param agentNo       坐席工号
	 */
	private void insertReocrd(String sessionId, String msg, String insertRecord, String chatRecordId,String agentNo) {
		try {
			EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
			//不需要保存明细记录
			if("N".equalsIgnoreCase(insertRecord)){
				return;
			}
			
			if(StringUtils.isBlank(chatRecordId)){
				EasySQL sql = new EasySQL(" SELECT T.SERIAL_ID FROM YCBUSI.CC_MEDIA_RECORD T WHERE 1=1   ");
				sql.append(sessionId," AND T.SESSION_ID=? ");
				sql.append(" ORDER BY T.BEGIN_TIME DESC ");
				
				EasyRow row = query.queryForRow(sql.getSQL(), sql.getParams());
				if(row!=null){
					chatRecordId = row.getColumnValue("SERIAL_ID");
				}
			}
			
			if(StringUtils.isNotBlank(chatRecordId)){
				JSONObject json = new JSONObject();
				json.put("CHAT_ID", IDGenerator.getDefaultNUMID());
				json.put("ENT_ID", Constants.ENTERPRISE_ID);
				json.put("DATE_ID", DateUtil.getCurrentDateStr("yyyyMMdd"));
				json.put("MSG_TIME", DateUtil.getCurrentDateStr());
				json.put("MSG_TIMESTAMP", System.currentTimeMillis());

				json.put("CHAT_SESSION_ID", chatRecordId);
				json.put("CUST_SESSION_ID", sessionId);
				json.put("SENDER", 2); //2-坐席
				json.put("AGENT_ID", agentNo);
				json.put("MSG_TYPE", "text");
				json.put("MSG_CONTENT", msg+"【留言】"); //update20181105 入库时自动追加留言标识，方便坐席查看
				EasyRecord record = new EasyRecord("YCBUSI.CC_MEDIA_CHAT_RECORD", "CHAT_ID").setColumns(json);
				//打印record对象到logger
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 客服主动回复时,插入明细记录:"+record.toJSONString());
				query.save(record);
			}else{
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 客服主动回复时,无法查询到chatRecordId,不保存会话明细记录");
			}
			
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ " 客户主动给用户回复时入库异常:"+e.getMessage(),e);
		}
	}
}
