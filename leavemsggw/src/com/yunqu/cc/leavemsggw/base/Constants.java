package com.yunqu.cc.leavemsggw.base;

import org.easitline.common.core.context.AppContext;

import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_NAME = "yw-ds";     //默认数据源名称
	
	public final static String MARS_DS_NAME = "mars-ds";     //Mars数据源
	
	public final static String AMI_DS_NAME = "ami-ds";     //AMI数据源
	
	public final static String GENESYS_DS_NAME = "genesys-ds";     //genesys留言记录表数据源
	public final static String GENESYS_DS_NAME2 = "genesys-ds2";     //genesys留言记录表数据源
	
	
	public final static String APP_NAME = "leavemsggw";     //应用

	public static final String CSS_BASE_URL = "";
	
	public static final AppContext context = AppContext.getContext(APP_NAME);

	public static boolean isRun = false;


	/** 
	 * 语音通话记录表
	 */
	public static final String CALL_RECORD_TABLE_NAME= context.getProperty("CALL_RECORD_TABLE_NAME", "");
	/** 
	 * 语音通话记录质检记录字段名
	 */
	public static final String CALL_RECORD_QC_FIELD=context.getProperty("CALL_RECORD_QC_FIELD", "");
	/** 
	 * 全媒体会话记录表
	 */
	public static final String SESSION_RECORD_TABLE_NAME=context.getProperty("SESSION_RECORD_TABLE_NAME", "");
	/** 
	 * 全媒体会话消息记录表
	 */
	public static final String SESSION_RECORD_DETAIL_TABLE_NAME=context.getProperty("SESSION_RECORD_DETAIL_TABLE_NAME", "");
	/** 
	 * 全媒体会话记录质检记录字段名
	 */
	public static final String SESSION_RECORD_QC_FIELD=context.getProperty("SESSION_RECORD_QC_FIELD", "");
	
	/**
	 * 超过该时间间隔，则判断为静音，单位：秒
	 */
	public static final int MAX_NO_VOICE_SECONDS = CommonUtil.parseInt(context.getProperty("MAX_NO_VOICE_SECONDS", "5"));
	/**
	 * 呼叫中心平台：genesys、petra、huawei
	 */
	public static final String CALL_CENTER_PLATEFORM = context.getProperty("CALL_CENTER_PLATEFORM", "genesys");
	public static final String CALL_CENTER_PLATEFORM_PETRA = "petra";
	public static final String CALL_CENTER_PLATEFORM_GENESYS = "genesys";
	public static final String CALL_CENTER_PLATEFORM_HUAWEI = "huawei";
	
	
	/**
	 * 质检月的开始日期，即每月的哪天作为质检月的第一天
	 */
	public static final int QC_DATE_OF_MONTH = CommonUtil.parseInt(context.getProperty("QC_DATE_OF_MONTH", "25"));
	
	/**
	 * 统计类型：01-按天 02-按月
	 */
	public static final String ST_TYPE_DAY = "01";
	public static final String ST_TYPE_MONTH = "02";

	/**
	 * AMI的数据库表的Sechme
	 */
	public static final String AMI_DB_SECHME = ConfigUtil.getString(Constants.APP_NAME, "AMI_DB_SECHME","avcs");
	
	/**
	 * 字典LEAVE_MSG_TRANS_STATUS  01 待转写
	 */
	public final static String LEAVE_MSG_TRANS_STATUS_01 ="01";
	/**
	 * 字典LEAVE_MSG_TRANS_STATUS  02 待获取结果
	 */
	public final static String LEAVE_MSG_TRANS_STATUS_02 ="02";
	/**
	 * 字典LEAVE_MSG_TRANS_STATUS  03 转写成功
	 */
	public final static String LEAVE_MSG_TRANS_STATUS_03 ="03";
	
	/**
	 * 被叫号码的前缀,入库是剔除
	 */
	public static final String CALLED_PERFIX = ConfigUtil.getString(Constants.APP_NAME, "CALLED_PERFIX","0086757");

	/**
	 * 企业id
	 */
	public static final Object ENTERPRISE_ID = ConfigUtil.getString(Constants.APP_NAME, "ENTERPRISE_ID","1000");;
	
	
}
