package com.yunqu.cc.leavemsggw.model;

/**
 * 录音转写后的文本对象(一句话对应一个对象)
 */
public class VoiceContentModel {

	//呼叫id
	private String callRecordId;
	//该句会话的开始时间，yyyy-MM-dd HH:mi:ss
	private String beginTime;
	//该句会话的结束时间，yyyy-MM-dd HH:mi:ss
	private String endTime;
	//该句会话开始的秒，相对整通录音
	private int beginSeconds;
	//该句会话结束的秒，相对整通录音
	private int endSeconds;
	//该句话的语速：字/每分
	private double speed;
	//该句会话呼叫时长:秒
	private int callLen;
	//内容
	private String content;
	//用户类型  01-客服 02-客户 
	private String userType;
	//本句通话内容的唯一id
	private String textId;
	public String getCallRecordId() {
		return callRecordId;
	}
	public void setCallRecordId(String callRecordId) {
		this.callRecordId = callRecordId;
	}
	public String getBeginTime() {
		return beginTime;
	}
	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public int getBeginSeconds() {
		return beginSeconds;
	}
	public void setBeginSeconds(int beginSeconds) {
		this.beginSeconds = beginSeconds;
	}
	public int getEndSeconds() {
		return endSeconds;
	}
	public void setEndSeconds(int endSeconds) {
		this.endSeconds = endSeconds;
	}
	public int getCallLen() {
		return callLen;
	}
	public void setCallLen(int callLen) {
		this.callLen = callLen;
	}
	public String getContent() {
		return content;
	}
	public void setContent(String content) {
		this.content = content;
	}
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
	public String getTextId() {
		return textId;
	}
	public void setTextId(String textId) {
		this.textId = textId;
	}
	public void setSpeed(double speed) {
		// TODO Auto-generated method stub
		
	}
	public double getSpeed() {
		return speed;
	}
}
