package com.yunqu.cc.leavemsggw.model;

import java.sql.ResultSet;

import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.utils.lang.ResultSetUtil;


public class KeyWordModelRowMapper implements EasyRowMapper<KeyWordModel> {

	public KeyWordModel mapRow(ResultSet rs, int rowNum) {
		KeyWordModel vo = new KeyWordModel();
		try {
			
			vo.setDirId(ResultSetUtil.getString(rs, "DIR_ID", ""));
			vo.setDirName(ResultSetUtil.getString(rs, "DIR_NAME", ""));
			vo.setKeyWord(ResultSetUtil.getString(rs, "CONTENT", ""));
			vo.setKeyWordId(ResultSetUtil.getString(rs, "ID", ""));
			vo.setUserType(ResultSetUtil.getString(rs, "USER_TYPE", ""));
			
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return vo;
	}
}
