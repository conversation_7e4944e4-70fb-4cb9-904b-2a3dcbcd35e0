package com.yunqu.cc.leavemsggw.model;

/**
 * 语音质检扩展信息表
 */
public class QcVoiceReocrdModel {

	/**
	 * ID
	 */
	private String id;
	/**
	 * 会话id
	 */
	private String sessionReocrdId;
	/**
	 * 质检记录id
	 */
	private String qcRecordId;
	/**
	 * 重复呼叫
	 */
	private String repeatCall;
	
	/**
	 * 语速
	 */
	private double speed;
	
	/**
	 * 抢话总次数
	 */
	private int crossTimes;
	
	/**
	 * 抢话总时长
	 */
	private int crossSeconds;
	
	/**
	 * 静音总次数
	 */
	private int noVoiceTimes;
	
	/**
	 * 静音总时长
	 */
	private int noVoiceSeconds;
	
	/**
	 * 客户抢话次数
	 */
	private int customerCrossTimes=0;
	/**
	 * 客户抢话时长
	 */
	private int customerCrossSeconds=0;
	/**
	 * 客户静音次数
	 */
	private int customerNoVoiceTimes=0;
	/**
	 * 客户静音时长
	 */
	private int customerNoVoiceSeconds=0;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getSessionReocrdId() {
		return sessionReocrdId;
	}
	public void setSessionReocrdId(String sessionReocrdId) {
		this.sessionReocrdId = sessionReocrdId;
	}
	public String getQcRecordId() {
		return qcRecordId;
	}
	public void setQcRecordId(String qcRecordId) {
		this.qcRecordId = qcRecordId;
	}
	public String getRepeatCall() {
		return repeatCall;
	}
	public void setRepeatCall(String repeatCall) {
		this.repeatCall = repeatCall;
	}
	public double getSpeed() {
		return speed;
	}
	public void setSpeed(double speed) {
		this.speed = speed;
	}
	public int getCrossTimes() {
		return crossTimes;
	}
	public void setCrossTimes(int crossTimes) {
		this.crossTimes = crossTimes;
	}
	public int getCrossSeconds() {
		return crossSeconds;
	}
	public void setCrossSeconds(int crossSeconds) {
		this.crossSeconds = crossSeconds;
	}
	public int getNoVoiceTimes() {
		return noVoiceTimes;
	}
	public void setNoVoiceTimes(int noVoiceTimes) {
		this.noVoiceTimes = noVoiceTimes;
	}
	public int getNoVoiceSeconds() {
		return noVoiceSeconds;
	}
	public void setNoVoiceSeconds(int noVoiceSeconds) {
		this.noVoiceSeconds = noVoiceSeconds;
	}
	public int getCustomerCrossTimes() {
		return customerCrossTimes;
	}
	public void setCustomerCrossTimes(int customerCrossTimes) {
		this.customerCrossTimes = customerCrossTimes;
	}
	public int getCustomerCrossSeconds() {
		return customerCrossSeconds;
	}
	public void setCustomerCrossSeconds(int customerCrossSeconds) {
		this.customerCrossSeconds = customerCrossSeconds;
	}
	public int getCustomerNoVoiceTimes() {
		return customerNoVoiceTimes;
	}
	public void setCustomerNoVoiceTimes(int customerNoVoiceTimes) {
		this.customerNoVoiceTimes = customerNoVoiceTimes;
	}
	public int getCustomerNoVoiceSeconds() {
		return customerNoVoiceSeconds;
	}
	public void setCustomerNoVoiceSeconds(int customerNoVoiceSeconds) {
		this.customerNoVoiceSeconds = customerNoVoiceSeconds;
	}
	

}
