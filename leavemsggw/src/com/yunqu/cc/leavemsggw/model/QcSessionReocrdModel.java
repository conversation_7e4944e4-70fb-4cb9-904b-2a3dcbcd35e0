package com.yunqu.cc.leavemsggw.model;

/**
 * 全媒体质检扩展信息表
 */
public class QcSessionReocrdModel {

	/**
	 * ID
	 */
	private String id;
	/**
	 * 会话id
	 */
	private String sessionRecordId;
	/**
	 * 质检记录id
	 */
	private String qcRecordId;
	/**
	 * 首次响应时长
	 */
	private int firstReply;
	/**
	 * 平均响应时长
	 */
	private double avgReply;
	/**
	 * 用户消息量
	 */
	private int custMsgNum;
	/**
	 * 坐席消息量
	 */
	private int agentMsgNum;
	/**
	 * 问答比
	 */
	private double qaPercent;
	/**
	 * 30秒响应量
	 */
	private int thirtySecondsReplyNum;
	/**
	 * 消息响应量(响应时间不为0)
	 */
	private int replyNum;
	/**
	 * 慢回复次数
	 */
	private int slowReplyNum;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getQcRecordId() {
		return qcRecordId;
	}
	public void setQcRecordId(String qcRecordId) {
		this.qcRecordId = qcRecordId;
	}
	public int getFirstReply() {
		return firstReply;
	}
	public void setFirstReply(int firstReply) {
		this.firstReply = firstReply;
	}
	public double getAvgReply() {
		return avgReply;
	}
	public void setAvgReply(double avgReply) {
		this.avgReply = avgReply;
	}
	public int getCustMsgNum() {
		return custMsgNum;
	}
	public void setCustMsgNum(int custMsgNum) {
		this.custMsgNum = custMsgNum;
	}
	public int getAgentMsgNum() {
		return agentMsgNum;
	}
	public void setAgentMsgNum(int agentMsgNum) {
		this.agentMsgNum = agentMsgNum;
	}
	public double getQaPercent() {
		return qaPercent;
	}
	public void setQaPercent(double qaPercent) {
		this.qaPercent = qaPercent;
	}
	public int getThirtySecondsReplyNum() {
		return thirtySecondsReplyNum;
	}
	public void setThirtySecondsReplyNum(int thirtySecondsReplyNum) {
		this.thirtySecondsReplyNum = thirtySecondsReplyNum;
	}
	public int getReplyNum() {
		return replyNum;
	}
	public void setReplyNum(int replyNum) {
		this.replyNum = replyNum;
	}
	public int getSlowReplyNum() {
		return slowReplyNum;
	}
	public void setSlowReplyNum(int slowReplyNum) {
		this.slowReplyNum = slowReplyNum;
	}
	public String getSessionRecordId() {
		return sessionRecordId;
	}
	public void setSessionRecordId(String sessionRecordId) {
		this.sessionRecordId = sessionRecordId;
	}
	
	
}
