package com.yunqu.cc.leavemsggw.model;

/**
 * 录音转写后的文本对象(一句话对应一个对象)
 */
public class VoiceQCModel {

	//呼叫id
	private String callRecordId;
	//该句会话的开始时间，yyyy-MM-dd HH:mi:ss
	private String beginTime;
	//该句会话的结束时间，yyyy-MM-dd HH:mi:ss
	private String endTime;
	
	//质检类型
	private String type;
	
	//质检结果
	private String result;
	//本通通话内容的唯一id
	private String textId;
	
	//本句通话相对整通录音的开始时间(单位：毫秒)
	private int beginSeconds;
	//本句通话相对整通录音的结束时间(单位：毫秒)
	private int endSeconds;
	
	public String getCallRecordId() {
		return callRecordId;
	}
	public void setCallRecordId(String callRecordId) {
		this.callRecordId = callRecordId;
	}
	public String getBeginTime() {
		return beginTime;
	}
	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getResult() {
		return result;
	}
	public void setResult(String result) {
		this.result = result;
	}
	public String getTextId() {
		return textId;
	}
	public void setTextId(String textId) {
		this.textId = textId;
	}
	public int getBeginSeconds() {
		return beginSeconds;
	}
	public void setBeginSeconds(int beginSeconds) {
		this.beginSeconds = beginSeconds;
	}
	public int getEndSeconds() {
		return endSeconds;
	}
	public void setEndSeconds(int endSeconds) {
		this.endSeconds = endSeconds;
	}
	
}
