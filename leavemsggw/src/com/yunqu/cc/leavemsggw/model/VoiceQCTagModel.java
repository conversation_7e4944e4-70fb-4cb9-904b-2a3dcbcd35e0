package com.yunqu.cc.leavemsggw.model;

/**
 * 录音标签结果
 */
public class VoiceQCTagModel {

	//呼叫id
	private String callRecordId;
	//该句会话的开始时间，yyyy-MM-dd HH:mi:ss
	private String beginTime;
	//该句会话的结束时间，yyyy-MM-dd HH:mi:ss
	private String endTime;
	
	//该句话的在录音中的相对开始时间、第几毫秒
	private int beginSeconds;
	//该句话的在录音中的相对结束时间、第几毫秒
	private int endSeconds;
	
	//关键字
	private String keyWord;
	
	//标签名称
	private String tagName;
	//本句通话内容的唯一id
	private String textId;
	
	//用户类型  01-客服 02-客户 
	private String userType;
	
	public String getCallRecordId() {
		return callRecordId;
	}
	public void setCallRecordId(String callRecordId) {
		this.callRecordId = callRecordId;
	}
	public String getBeginTime() {
		return beginTime;
	}
	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getKeyWord() {
		return keyWord;
	}
	public void setKeyWord(String keyWord) {
		this.keyWord = keyWord;
	}
	public String getTagName() {
		return tagName;
	}
	public void setTagName(String tagName) {
		this.tagName = tagName;
	}
	public String getTextId() {
		return textId;
	}
	public void setTextId(String textId) {
		this.textId = textId;
	}
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
	public int getBeginSeconds() {
		return beginSeconds;
	}
	public void setBeginSeconds(int beginSeconds) {
		this.beginSeconds = beginSeconds;
	}
	public int getEndSeconds() {
		return endSeconds;
	}
	public void setEndSeconds(int endSeconds) {
		this.endSeconds = endSeconds;
	}
	
}
