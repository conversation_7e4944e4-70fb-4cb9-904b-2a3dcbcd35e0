package com.yunqu.cc.leavemsggw.model;

import java.util.List;

import org.easitline.common.db.EasyRow;

public class QcReocrdModel {
	
	/**
	 * 质检记录id
	 */
	private String id;

	/**
	 * 质检记录
	 */
	private EasyRow QcRecordRow;
	/**
	 * 会话类型
	 */
	private String sessionType;
	/**
	 * 会话记录或通话记录
	 */
	private EasyRow callRecordrow;
	/**
	 * 会话记录明细
	 */
	private List<EasyRow> callRecordDetailList;
	/**
	 * 会话标签明细
	 */
	private List<EasyRow> callKeyWordList;
	
	/**
	 * 全媒体会话id或语音会话id
	 */
	private String sessionRecordId;
	
	
	/**
	 * 全媒体质检记录扩展属性
	 */
	private QcSessionReocrdModel qcSessionReocrdModel;
	
	/**
	 * 语音质检记录扩展属性
	 */
	private QcVoiceReocrdModel qcVoiceReocrdModel;
	
	public EasyRow getQcRecordRow() {
		return QcRecordRow;
	}
	public void setQcRecordRow(EasyRow qcRecordRow) {
		QcRecordRow = qcRecordRow;
	}
	public String getSessionType() {
		return sessionType;
	}
	public void setSessionType(String sessionType) {
		this.sessionType = sessionType;
	}
	public EasyRow getCallRecordrow() {
		return callRecordrow;
	}
	public void setCallRecordrow(EasyRow callRecordrow) {
		this.callRecordrow = callRecordrow;
	}
	public List<EasyRow> getCallRecordDetailList() {
		return callRecordDetailList;
	}
	public void setCallRecordDetailList(List<EasyRow> callRecordDetailList) {
		this.callRecordDetailList = callRecordDetailList;
	}
	public List<EasyRow> getCallKeyWordList() {
		return callKeyWordList;
	}
	public void setCallKeyWordList(List<EasyRow> callKeyWordList) {
		this.callKeyWordList = callKeyWordList;
	}
	
	public String getSessionRecordId() {
		return sessionRecordId;
	}
	public void setSessionRecordId(String sessionRecordId) {
		this.sessionRecordId = sessionRecordId;
	}
	public QcSessionReocrdModel getQcSessionReocrdModel() {
		return qcSessionReocrdModel;
	}
	public void setQcSessionReocrdModel(QcSessionReocrdModel qcSessionReocrdModel) {
		this.qcSessionReocrdModel = qcSessionReocrdModel;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public QcVoiceReocrdModel getQcVoiceReocrdModel() {
		return qcVoiceReocrdModel;
	}
	public void setQcVoiceReocrdModel(QcVoiceReocrdModel qcVoiceReocrdModel) {
		this.qcVoiceReocrdModel = qcVoiceReocrdModel;
	}
	
	
	
}
