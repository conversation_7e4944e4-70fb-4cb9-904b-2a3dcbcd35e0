package com.yunqu.cc.leavemsggw.job;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.SyncInfoModel;
import com.yq.busi.common.service.SyncService;
import com.yq.busi.common.task.BaseTask;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.leavemsggw.base.CommonLogger;
import com.yunqu.cc.leavemsggw.base.Constants;
import com.yunqu.cc.leavemsggw.utils.CallUtil;

/**
 * 对于语音留言，genesys会记录留言记录；需要业务平台定时去获取
 */
public class SyncLeaveMsgTask extends BaseTask {

	private Logger logger = CommonLogger.logger;

	private EasyQuery genesysQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.GENESYS_DS_NAME,logger);
	private EasyQuery genesysQuery2 = EasyQuery.getQuery(Constants.APP_NAME, Constants.GENESYS_DS_NAME2,logger);
	
	private EasyQuery amiQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.AMI_DS_NAME,logger);
	private EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME,logger);

	public SyncLeaveMsgTask() {
		super("从Genesys同步留言记录", CommonLogger.logger);
	}

	/**
	 * 同步留言记录
	 */
	public void doing() {
		try {
			syncLeaveMsg();
			uploadLeaveMsgToAMI();
			syncLeaveMsgToTransRecord();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " 同步genesys留言记录失败:" + e.getMessage(), e);
		}
	}

	/**
	 * 同步留言记录
	 * 
	 * @throws SQLException
	 */
	private void syncLeaveMsg() throws SQLException {
		// 从genesys查询要同步的留言
		SyncInfoModel model = SyncService.getSyncInfo(Constants.APP_NAME + "-sync-genesys-leave-msg");
		String currTime = DateUtil.getCurrentDateStr();

		model.setSyncTime(currTime);
		model.setBakup("leavemsggw定时从gennesys同步留言记录:" + currTime);
		
		syncLevaeMsgByRegion("HF");
		syncLevaeMsgByRegion("SD");

		// 更新同步时间
		SyncService.updateSyncTime(model);

	}

	

	private void syncLevaeMsgByRegion(String region) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" SELECT T.ID,T.CALLERUMBER,T.CALLEDNUMBER,T.AGENTID,TO_CHAR(T.CREATETIME,'yyyy-MM-dd HH24:mi:ss') CREATE_TIME,T.LIUYANPATH  ");
			sql.append(" FROM IVRINFO T WHERE T.LIUYANDISTINGUISH='0' AND T.LiuyanPath IS NOT NULL  ");
			//sql.append(" AND T.CREATETIME BETWEEN TO_DATE('" + syncTime + "','yyyy-MM-dd HH24:mi:ss') AND TO_DATE('"+ currTime + "','yyyy-MM-dd HH24:mi:ss')  ");
//			sql.append(" ORDER BY T.CREATETIME ASC ");

			List<EasyRow> list =null;
			if("HF".equals(region)){
				list = genesysQuery2.queryForList(sql.getSQL(), sql.getParams());
			}else{
				list = genesysQuery.queryForList(sql.getSQL(), sql.getParams());
			}
			
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 开始同步留言记录,"+region + ","+ sql.getSQL());

			// 没有数据时，也需要更新同步时间
			if (CommonUtil.listIsNull(list)) {
				logger.info(CommonUtil.getClassNameAndMethod(this) + " 暂时没有需要同步的留言" );
				return;
			}
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 本次需要需要获取录音的留言记录:" + list.size());

			int succ = 0;
			int fail = 0;

			// 将同步到的留言记录写入数据库
			for (EasyRow row : list) {
				String id = "";
				String caller = "";
				String called = "";
				try {
					id = row.getColumnValue("ID");
					caller = row.getColumnValue("CALLERUMBER");
					called = row.getColumnValue("CALLEDNUMBER");
					
					called = CallUtil.delPrefix(called);

					String createTime = row.getColumnValue("CREATE_TIME");
					String voicePath = row.getColumnValue("LIUYANPATH");

					EasyRecord record = new EasyRecord("C_LM_LEAVE_MSG", "ID");
					record.put("ID", id);
					record.put("CALLER", caller);
					record.put("CALLED", called);
					record.put("CREATE_TIME", createTime);
					record.put("VOICE_URL", voicePath);
					record.put("STATUS", "01"); // 01-待转写
					record.put("REGION_CODE", region); 
					query.save(record);
					
					EasySQL updateSql = new EasySQL("UPDATE IVRINFO SET LIUYANDISTINGUISH='1' WHERE ID=?");
					
					if("HF".equals(region)){
						genesysQuery2.execute(updateSql.getSQL(), new Object[]{id});
					}else{
						genesysQuery.execute(updateSql.getSQL(), new Object[]{id});
					}

					succ++;

				} catch (Exception e) {
					// 同步失败的记录写日志
					logger.error(CommonUtil.getClassNameAndMethod(this) + " Genesys留言记录同步失败,ID:" + id + ",caller=" + caller
							+ ",called=" + called);
					fail++;
				}
			}
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 同步Genesys留言记录完成,总数:" + list.size() + ",成功:" + succ
					+ ",失败:" + fail);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this) + " Genesys留言记录同步失败," + e.getMessage(),e);
		}
		
	}

	/**
	 * 根据留言记录录音URL上传文件到AMI
	 */
	private void uploadLeaveMsgToAMI() {
		InputStream in = null;
		OutputStream out = null;
		HttpURLConnection conn = null;
		String fileName = null;
		String FILE_PATH = null;// 文件地址
		URL url = null;
		String CREATE_TIME= null;//留言时间
		// 附件上传,文件地址存放地址,根目录
		String filePath = ConfigUtil.getString(Constants.APP_NAME, "AMI_ROOT_PAHT", "//10.32.192.46/Detection");
		// 格式化并转换String类型
//		conn String newPath = "/leavemsggw定时从gennesys同步留言记录avemsg"+"/" + new SimpleDateFormat("/yyyy/MM/dd/HH/").format(new Date());
		EasyRecord record = null;
		int succ = 0;
		int fail = 0;
		String currTime = DateUtil.getCurrentDateStr();
		try {
			query.begin();
			EasySQL sql = new EasySQL();
			sql.append(" select * from C_LM_LEAVE_MSG msg where msg.trans_status='"+Constants.LEAVE_MSG_TRANS_STATUS_01+"' ");
			// 获取所有未转写状态的留言记录
			List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
			File f = null;
			for (EasyRow row : list) {
				FILE_PATH = row.getColumnValue("VOICE_URL");
				if (StringUtils.isNotEmpty(FILE_PATH)) {
					url = new URL(FILE_PATH);
					conn = (HttpURLConnection) url.openConnection();
					conn.setDoInput(true);
					conn.setDoOutput(true);
					CREATE_TIME=row.getColumnValue("CREATE_TIME");
					CREATE_TIME=CREATE_TIME.replace(" ", "");
					CREATE_TIME=CREATE_TIME.replace("-", "");
					CREATE_TIME=CREATE_TIME.replace(":", "");
					// 获取文件名
					fileName="SHUNDE_"+row.getColumnValue("ID")+"_"+CREATE_TIME+"_"+row.getColumnValue("CALLED")+"_"+row.getColumnValue("CALLER")
								+"_"+"I.wav";

					// 读取数据
					if (conn.getResponseCode() == HttpURLConnection.HTTP_OK) {
						in = conn.getInputStream();
						
						// 创建文件夹
						/*f = new File(filePath + newPath);
						if (!f.exists()) {
							f.mkdirs();
						}*/
						byte[] buffer = new byte[1024];
						int len = 0;
						
						// 创建文件夹
						/*f = new File(filePath);
						if (!f.exists()) {
							f.mkdirs();
						}*/
						
						// 文件最终上传的位置
//						String newFileName = filePath + newPath + fileName ;
						String newFileName = filePath  + fileName ;
						out = new FileOutputStream(newFileName);
						
						while ((len = in.read(buffer)) != -1) {
							out.write(buffer, 0, len);
						}
						//修改留言表转写状态
						record = new EasyRecord("C_LM_LEAVE_MSG", "ID").setPrimaryValues(row.getColumnValue("ID"));
						record.put("TRANS_STATUS", Constants.LEAVE_MSG_TRANS_STATUS_02);
						query.update(record);
						succ++;
					}else{
						fail++;
					}
				}else{
					fail++;
				}
			}
			query.commit();
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 上传录音文件完成,总数:" + list.size() + ",成功:" + succ
					+ ",失败:" + fail + ",上传时间更新为:" + currTime);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 上传录音文件失败，原因："+ e.getMessage()+"",e);
			try {
				query.roolback();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			e.printStackTrace();
		} finally{
	          try {
	        	  if(out!=null){
	        		  out.close();
	        	  }
	        	  if(in!=null){
	        		  in.close();
	        	  }
	        	  if(conn!=null){
	        		  conn.disconnect();
	        	  }
	          } catch (IOException e) {
	           e.printStackTrace();
	          }
	         }
	}
	/**
	 * 同步留言语音转写记录
	 */
	private void syncLeaveMsgToTransRecord(){
		try {
			query.begin();
			EasySQL sql = new EasySQL();
			sql.append(" select * from C_LM_LEAVE_MSG msg where msg.trans_status='"+Constants.LEAVE_MSG_TRANS_STATUS_02+"' ");
			// 获取所有待获取结果状态的留言记录
			List<EasyRow> list = query.queryForList(sql.getSQL(), sql.getParams());
			List<EasyRow> v_list = null;
			int succ = 0;
			int fail = 0;
			String currTime = DateUtil.getCurrentDateStr();
			JSONObject json = null;
			EasyRecord record = null;
			EasyRow easyrow = null;
			String voiceBeginTime = "";
			int beginSeconds = 0;
			int endSeconds = 0;
			int callLen = 0;
			String beginTime = null;
			String endTime = null;
			boolean flag=false;
			for(EasyRow row:list){
				flag=true;
				//查询转写记录
				sql = new EasySQL();
				sql.append(" SELECT CONVERSATION_KEY,CONVERSATION_ATTRIBUTE_VALUE,CHANNEL_TYPE_CD,START_DATETIME,START_TIME,END_TIME,SENTENCE,CALL_UUID FROM "+Constants.AMI_DB_SECHME+".V_MD_CONVERSATION_RECOGNITION_RESULT WHERE 1=1");
				sql.append(" AND CALL_UUID = '"+row.getColumnValue("ID")+"' order by START_DATETIME,START_TIME,END_TIME ");
				
				v_list= amiQuery.queryForList(sql.getSQL(), sql.getParams());
				if(v_list!=null&&v_list.size()>0){
					for(int i=0;i<v_list.size();i++){
						easyrow=v_list.get(i);
						json = new JSONObject();
						json.put("ID",RandomKit.randomStr());
						json.put("LEAVE_MSG_ID",easyrow.getColumnValue("CALL_UUID"));
						
						voiceBeginTime = easyrow.getColumnValue("START_DATETIME");
						
						if(StringUtils.isNotBlank(voiceBeginTime)){
							//该句会话在整通录音中的开始时间：毫秒
							beginSeconds = CommonUtil.parseInt(easyrow.getColumnValue("START_TIME"));
							//该句会话在整通录音中的结束时间：毫秒
							endSeconds = CommonUtil.parseInt(easyrow.getColumnValue("END_TIME"));
							//该句会话通话时长
							callLen = endSeconds - beginSeconds;
							beginTime = DateUtil.addSecond(DateUtil.TIME_FORMAT, voiceBeginTime, beginSeconds);
							endTime = DateUtil.addSecond(DateUtil.TIME_FORMAT, voiceBeginTime, endSeconds);
							
							json.put("START_TIME",beginTime);
							json.put("END_TIME",endTime);
							json.put("TIME_SECONDS",callLen);
							
						}
						json.put("INDEX_NO",i);
						json.put("CONTENT",easyrow.getColumnValue("SENTENCE"));
						record = new EasyRecord("C_LM_TRANS_RECORD","ID").setColumns(json);
						query.save(record);
					}
					flag=true;
					succ++;
				}else{
					flag=false;
					fail++;
					logger.info(CommonUtil.getClassNameAndMethod(this) + " 同步AMI录音内容失败,ID为"+row.getColumnValue("ID")+"留言数据没有查询到相对应的留言转写数据" );
				}
				if(flag){
					//修改留言表转写状态
					record = new EasyRecord("C_LM_LEAVE_MSG", "ID").setPrimaryValues(row.getColumnValue("ID"));
					record.put("TRANS_STATUS", Constants.LEAVE_MSG_TRANS_STATUS_03);
					query.update(record);
				}
				
			}
			query.commit();
			logger.info(CommonUtil.getClassNameAndMethod(this) + " 同步AMI录音内容完成,总数:" + list.size() + ",成功:" + succ
					+ ",失败:" + fail + ",上传时间更新为:" + currTime);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 同步AMI录音内容失败，原因："+ e.getMessage()+"",e);
			try {
				query.roolback();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			e.printStackTrace();
		}
	}

}
