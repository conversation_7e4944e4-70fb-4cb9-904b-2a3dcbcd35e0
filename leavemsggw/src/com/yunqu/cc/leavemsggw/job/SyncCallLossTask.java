package com.yunqu.cc.leavemsggw.job;

import java.sql.SQLException;
import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.yq.busi.common.model.SyncInfoModel;
import com.yq.busi.common.service.SyncService;
import com.yq.busi.common.task.BaseTask;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.leavemsggw.base.CommonLogger;
import com.yunqu.cc.leavemsggw.base.Constants;
import com.yunqu.cc.leavemsggw.utils.CallUtil;

/**
 * 对于未接来电，genesys会记录未接记录；需要业务平台定时去获取
 * 未接来电包括:1、客户拨打后没选择技能组就挂断的(genesys无法取到)；2、客户拨打后选择了技能组，未分配坐席挂断的
 */
public class SyncCallLossTask extends BaseTask{
	
	private Logger logger = CommonLogger.logger;
	
	private EasyQuery genesysQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.GENESYS_DS_NAME,logger);
	private EasyQuery genesysQuery2 = EasyQuery.getQuery(Constants.APP_NAME, Constants.GENESYS_DS_NAME2,logger);
	
	private EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME,logger);

	
	public SyncCallLossTask(){
		super("从Genesys同步未接来电记录",CommonLogger.logger);
	}
	
	/**
	 * 同步未接来电记录
	 */
	public void doing() {
		try {
			syncCallLoss();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 同步genesys未接来电记录失败:"+e.getMessage(),e);
		}
	}

	/**
	 * 同步未接来电记录
	 * @throws SQLException 
	 */
	private void syncCallLoss() throws SQLException {
		//从genesys查询要同步的未接来电
		
		SyncInfoModel model = SyncService.getSyncInfo(Constants.APP_NAME+"-sync-genesys-call-loss");
//		String syncTime = model.getSyncTime();
		String currTime = DateUtil.getCurrentDateStr();
		
		syncCallLossByRegion("SD");
		syncCallLossByRegion("HF");
		
		model.setSyncTime(currTime);
		model.setBakup("leavemsggw定时从gennesys同步未接来电:"+currTime);
		
		//更新同步时间
		SyncService.updateSyncTime(model);
		
		
	}

	private void syncCallLossByRegion(String region) {
		try {
			//CLEARCAUSE: 1.排队超时挂断、2.用户主动挂断、3.按键错误挂断、4.按键超时挂断、5.系统异常挂断、6、用户选择人工后排队中挂机、7.留言挂机
			EasySQL sql = new EasySQL();
			sql.append(" SELECT T.ID,T.CALLERUMBER,T.CALLEDNUMBER,T.AGENTID,T.CLEARCAUSE,TO_CHAR(T.CREATETIME,'yyyy-MM-dd HH24:mi:ss') CREATE_TIME,TO_CHAR(T.DEALLOCTIME,'yyyy-MM-dd HH24:mi:ss') DEALLOCTIME  ");
			sql.append(" FROM IVRINFO T WHERE T.LIUYANDISTINGUISH='0' AND T.CLEARCAUSE !='07'  AND T.CLEARCAUSE IS NOT NULL     ");
			//sql.append(" AND T.CREATETIME BETWEEN TO_DATE('"+syncTime+"','yyyy-MM-dd HH24:mi:ss') AND TO_DATE('"+currTime+"','yyyy-MM-dd HH24:mi:ss')  ");
//			sql.append(" ORDER BY T.CREATETIME ASC ");
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 开始同步未接来电,"+region+","+sql.getSQL());
			
			List<EasyRow> list = null;
			if("HF".equals(region)){
				list = genesysQuery2.queryForList(sql.getSQL(), sql.getParams());
			}else{
				list = genesysQuery.queryForList(sql.getSQL(), sql.getParams());
			}
			
			
			//没有数据时，也需要更新同步时间
			if(CommonUtil.listIsNull(list)){
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 暂时没有需要同步的未接来电");
				return;
			}
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 本次需要需要获取录音的通话记录:"+list.size());
			
			int succ = 0;
			int fail = 0;
			
			//将同步到的未接来电记录写入数据库
			for(EasyRow row : list){
				String id = "";
				String caller = "";
				String called = "";
				try {
					id = row.getColumnValue("ID");
					caller = row.getColumnValue("CALLERUMBER");
					called = row.getColumnValue("CALLEDNUMBER");
					
					called = CallUtil.delPrefix(called);
					
					String createTime = row.getColumnValue("CREATE_TIME");
					String deallocTime = row.getColumnValue("DEALLOCTIME");
					String clearCause = row.getColumnValue("CLEARCAUSE"); //1.排队超时挂断、2.用户主动挂断、3.按键错误挂断、4.按键超时挂断、5.系统异常挂断、6、用户选择人工后排队中挂机
					
					EasyRecord record = new EasyRecord("C_LM_CALLLOSS","ID");
					record.put("ID", id +"_"+ IDGenerator.getDefaultNUMID());
					record.put("CALLER", caller);
					record.put("CALLED", called);
					record.put("CALL_TIME", createTime);
					record.put("HANGUP_TIME", deallocTime);
					record.put("WAIT_SECONDS", DateUtil.calDurationSeconds(createTime, deallocTime));
					record.put("STATUS", "01"); //01-待处理
					record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
					record.put("CLEAR_CAUSE", clearCause);
					record.put("REGION_CODE", region); 
					query.save(record);
					
					EasySQL updateSql = new EasySQL("UPDATE IVRINFO SET LIUYANDISTINGUISH='1' WHERE ID=?");
					
					if("HF".equals(region)){
						genesysQuery2.execute(updateSql.getSQL(), new Object[]{id});
					}else{
						genesysQuery.execute(updateSql.getSQL(), new Object[]{id});
					}
					
					succ++;
					
				} catch (Exception e) {
					//同步失败的记录写日志
					logger.error(CommonUtil.getClassNameAndMethod(this)+" Genesys未接来电同步失败,ID:"+id+",caller="+caller+",called="+called,e);
					fail++;
				}
			}	
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 同步Genesys未接来电完成,总数:"+list.size()+",成功:"+succ+",失败:"+fail);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 同步Genesys未接来电失败",e);
		}
		
		
	}
	
}
