package com.yunqu.cc.leavemsggw.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yq.busi.common.base.ServiceID;
import com.yunqu.cc.leavemsggw.base.Constants;

@WebListener
public class InterfaceLinstener extends ServiceContextListener{

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		
		//处理售后接口
		ServiceResource resource = new ServiceResource();
		resource.appName = Constants.APP_NAME;
		resource.className = "com.yunqu.cc.leavemsggw.inf.InterfaceService";
		resource.description = "处理留言、离线相关的接口";
		resource.serviceId = ServiceID.LEAVE_MSG_GW_INTEFACE;
		resource.serviceName = "处理留言、离线相关的接口";
		list.add(resource);
		
		return list;
	}

}
