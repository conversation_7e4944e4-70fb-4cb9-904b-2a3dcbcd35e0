package com.yunqu.cc.leavemsggw.utils;

import org.easitline.common.utils.string.StringUtils;

import com.yunqu.cc.leavemsggw.base.Constants;

public class CallUtil {

	/**
	 * 剔除号码的前缀
	 * @param called
	 * @return
	 */
	public static String delPrefix(String called) {
		//剔除号码前缀
		if(StringUtils.isNotBlank(called) && StringUtils.isNotBlank(Constants.CALLED_PERFIX)){
			String[] fixs = Constants.CALLED_PERFIX.split(";");
			for(String fix : fixs){
				called = called.replace(fix, "");
			}
		}
		return called;
	}
}
