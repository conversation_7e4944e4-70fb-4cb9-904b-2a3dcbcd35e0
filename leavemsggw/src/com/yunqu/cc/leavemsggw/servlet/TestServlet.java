package com.yunqu.cc.leavemsggw.servlet;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yunqu.cc.leavemsggw.base.AppBaseServlet;

@WebServlet({ "/test" })
public class TestServlet extends AppBaseServlet{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		doPost(req, resp);
	}
	
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		
		String command = req.getParameter("command");
		try {
			if(ServiceCommand.SYNC_GENESYS_LEAVEMSG.equals(command)){
				IService service = ServiceContext.getService(ServiceID.LEAVE_MSG_GW_INTEFACE);
				JSONObject json = new JSONObject();
				json.put("command", ServiceCommand.SYNC_GENESYS_LEAVEMSG);
				JSONObject j = service.invoke(json);
				System.out.println(j);
			}
			if(ServiceCommand.SYNC_GENESYS_CALLLOSS.equals(command)){
				IService service = ServiceContext.getService(ServiceID.LEAVE_MSG_GW_INTEFACE);
				JSONObject json = new JSONObject();
				json.put("command", ServiceCommand.SYNC_GENESYS_CALLLOSS);
				JSONObject j = service.invoke(json);
				System.out.println(j);
			}
			
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
