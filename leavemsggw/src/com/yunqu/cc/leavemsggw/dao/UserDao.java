package com.yunqu.cc.leavemsggw.dao;

import org.apache.commons.lang.StringUtils;
import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;

public class UserDao extends BaseDao{

	/**
	 * 根据坐席账号找出坐席的基本嘻嘻你
	 * 可用列：USER_ACC,USER_NAME,EP_CODE,DEPT_CODE,DEPT_NAME,AREA_CODE
	 * @return
	 */
	public JSONObject findUserBaseInfoByUserAcc(String userAcc){
		StringBuffer sql = new StringBuffer(" SELECT DISTINCT RS_ID USER_ID , EX1 DEPT_CODE,EP_CODE  FROM C_CF_DICT_RESOURCE T WHERE T.DICT_GROUP_CODE='AGENT_TYPE' AND T.RS_TYPE='01'  ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 找出坐席基本信息:"+sql.toString());
			EasyRow row  = queryHelper.queryForRow(sql.toString(),null);
			if(row!=null){
				String deptCode = row.getColumnValue("DEPT_CODE");
				JSONObject json = new JSONObject();
				json.put("USER_ACC", userAcc);
				json.put("USER_NAME", userAcc);
				json.put("EP_CODE", userAcc);
				json.put("DEPT_CODE", deptCode);
				json.put("DEPT_NAME", userAcc);
				json.put("AREA_CODE", userAcc);
				if(StringUtils.isNotBlank(deptCode)){
					if(deptCode.length()>3){
						json.put("EP_CODE", deptCode.substring(0,3));
					}
					if(deptCode.length()>12){
						json.put("AREA_CODE", deptCode.substring(0,12));
					}
				}
				return json;
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 找出坐席基本信息失败.",e);
		}
		return null;
	}
}
