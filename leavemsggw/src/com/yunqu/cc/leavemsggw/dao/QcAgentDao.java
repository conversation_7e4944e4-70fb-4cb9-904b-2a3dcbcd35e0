package com.yunqu.cc.leavemsggw.dao;

import java.util.ArrayList;
import java.util.List;

import org.easitline.common.db.EasyRow;

import com.yq.busi.common.util.CommonUtil;

/**
 * 操作质检员的dao
 */
public class QcAgentDao extends BaseDao{

	
	/**
	 * 找出所有的质检人员
	 * @return
	 */
	public List<EasyRow> findAllQcAgent(){
		StringBuffer sql = new StringBuffer(" SELECT * from C_PF_QC_GROUP_MEMBER  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 找出所有的质检人员:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 找出所有的质检人员失败.",e);
		}
		return null;
	}

	/**
	 * 删除质检员某天的汇总记录
	 * @param stDay
	 */
	public boolean delSummaryRecordByDay(String stDay,String stType) {
		ArrayList<String> sqlList = new ArrayList<String>();
		try {
			String sql1 = " DELETE FROM C_ST_QCAGENT_QD  T WHERE EXISTS (SELECT 1 FROM C_ST_QCAGENT T1 WHERE T1.ID =  T.ST_QCAGENT_ID AND T1.STAT_DAY='"+stDay+"'  AND T1.ST_TYPE='"+stType+"')";
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 删除质检员某天的明细记录:"+sql1.toString());
			
			String sql2 = " DELETE FROM C_ST_QCAGENT T1 WHERE  T1.STAT_DAY='"+stDay+"'  "; 
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 删除质检员某天的汇总记录:"+sql2.toString());
			
			sqlList.add(sql1);
			sqlList.add(sql2);
			
			this.batchExecSql(getQueryHelper(), sqlList);
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 删除质检员某天的汇总记录失败.",e);
		}
		return false;
		
	}

	
}
