package com.yunqu.cc.leavemsggw.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.leavemsggw.model.KeyWordModel;
import com.yunqu.cc.leavemsggw.model.KeyWordModelRowMapper;

/**
 * 操作标签和关键字的dao
 */
public class TagDao extends BaseDao{
	

	/**
	 * 根据关键字目录id，删除关键字目录
	 * @param id
	 */
	public boolean delKeyWordDirById(String id) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(id," DELETE FROM C_CF_KEYWORD_DIR WHERE ID=?");
			this.queryHelper.execute(sql.getSQL(), sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据ID删除关键字目录失败.",e);
		}
		return false;
		
	}

	/**
	 * 从AMI同步的标签，写入到库中，标记为语音类型的关键字目录
	 * @param id
	 * @param name
	 * @param desc
	 */
	public boolean insertKeyWordDir(String id, String name, String desc) {
		try {
			JSONObject json = new JSONObject();
			json.put("ID", id);
			json.put("CODE", id);
			json.put("NAME", name);
			json.put("BAKUP", desc);
			json.put("CREATE_ACC", "system");
			json.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			json.put("CREATE_DEPT", "001");
			json.put("EP_CODE",  "001");
			json.put("IS_DEFALUT", DictConstants.DICT_SY_YN_Y);
			json.put("BUSI_TYPE", "");
			json.put("MODULE", DictConstants.SYSTEM_MODULE_PF);
			json.put("SORT_NUM", 1);
			json.put("ENABLE_STATUS", DictConstants.DICT_SY_YN_Y);
//			json.put("USER_TYPE", id);
			EasyRecord record = new EasyRecord("C_CF_KEYWORD_DIR", "ID").setColumns(json);
			queryHelper.save(record);
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 新增关键字目录失败.",e);
		}
		return false;
	}
	
	/**
	 * 查询出语音质检需要用到的所有关键字目录(所有标签)
	 * @return
	 */
	public List<EasyRow> listVoiceQcKeyWordDir() {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" SELECT * FROM C_CF_KEYWORD_DIR WHERE 1=1 ");
			sql.append(DictConstants.SYSTEM_MODULE_PF," AND MODULE=?");
			sql.append(""," AND BUSI_TYPE=?");
			
			return queryHelper.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询语音质检关键字目录失败.",e);
		}
		return null;
	}

	/**
	 * 根据关键字id删除关键字
	 * @param keyWordId
	 */
	public boolean delKeyWordById(String keyWordId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(keyWordId," DELETE FROM C_CF_KEYWORD WHERE ID=?");
			this.queryHelper.execute(sql.getSQL(), sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据ID删除关键字失败.",e);
		}
		return false;
		
	}

	/**
	 * 新增关键字
	 * @param keyWordId
	 * @param keyWord
	 * @param keyWordDirId
	 * @param userType
	 */
	public boolean insertKeyWord(String keyWordId, String keyWord, String keyWordDirId, String userType) {
		try {
			JSONObject json = new JSONObject();
			json.put("ID", keyWordId);
			json.put("DIR_ID", keyWordDirId);
			json.put("TITLE", keyWord);
			json.put("CONTENT", keyWord);
			json.put("IS_PUBLIC",DictConstants.DICT_SY_YN_Y);
			json.put("CREATE_ACC", "system");
			json.put("CREATE_TIME", DateUtil.getCurrentDateStr());
			json.put("CREATE_DEPT", "001");
			json.put("SORT_NUM", 1);
			json.put("ENABLE_STATUS", DictConstants.DICT_SY_YN_Y);
			json.put("USER_TYPE", userType);
			EasyRecord record = new EasyRecord("C_CF_KEYWORD", "ID").setColumns(json);
			queryHelper.save(record);
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 新增关键字失败.",e);
		}
		return false;
		
	}

	/**
	 * 获取语音关键字的 NAME -> ID map
	 * @return
	 */
	public Map<String, String> getVoiceNameIdMap() {
		List<EasyRow> keyWordDirList = this.listVoiceQcKeyWordDir();
		//存储语音关键字目录名称-ID的组合
		Map<String,String> keyWOrdDirMap = new HashMap<String,String>();
		if(CommonUtil.listIsNotNull(keyWordDirList)){
			for(EasyRow row : keyWordDirList){
				String name = row.getColumnValue("NAME");
				String keyWordDirId = row.getColumnValue("ID");
				keyWOrdDirMap.put(name, keyWordDirId);		
			}
		}
		return keyWOrdDirMap;
	}

	
	/**
	 * 根据模块查询关键字集合
	 * @param module
	 * @return
	 */
	public List<KeyWordModel> findKeyWordByModule(String module) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" SELECT T1.ID,T1.CONTENT,T2.ID DIR_ID,T2.NAME DIR_NAME,T1.USER_TYPE FROM C_CF_KEYWORD T1 LEFT JOIN C_CF_KEYWORD_DIR T2 ON T1.DIR_ID = T2.ID WHERE 1=1 ");
			sql.append(DictConstants.ENABEL_STATUS_ENABLE,"  AND T1.ENABLE_STATUS=? ");
			sql.append(DictConstants.ENABEL_STATUS_ENABLE,"  AND T2.ENABLE_STATUS=? ");
			sql.append(module,"  AND T2.MODULE=? ");
			
			return queryHelper.queryForList(sql.getSQL(), sql.getParams(),new KeyWordModelRowMapper());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据模块查询关键字集合失败.",e);
		}
		return null;
	}

}
