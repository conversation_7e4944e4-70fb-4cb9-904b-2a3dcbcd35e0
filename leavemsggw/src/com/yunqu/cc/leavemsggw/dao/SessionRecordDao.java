package com.yunqu.cc.leavemsggw.dao;

import java.util.List;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.AgentModel;
import com.yq.busi.common.util.CommonUtil;
import com.yunqu.cc.leavemsggw.base.CommonLogger;
import com.yunqu.cc.leavemsggw.base.Constants;

/**
 * 对全媒体话单进行操作的dao
 */
public class SessionRecordDao extends BaseDao{
	
	private Logger logger = CommonLogger.logger;
	
	private EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	
	/**
	 * 根据会话记录id，查询会话记录
	 * @param recordId
	 * @return
	 */
	public EasyRow getSessionRecordById(String recordId) {
		return getRowById(queryHelper, "根据会话记录id，查询会话记录", "C_PF_V_SESSION_RECORD", "ID", recordId);
	}
	
	/**
	 * 根据会话记录id，查询会话明细记录
	 * @param recordId
	 * @return
	 */
	public List<EasyRow> getSessionRecordDetailByRecordId(String recordId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" SELECT * FROM V_PF_SESSION_DETAIL T WHERE T.SESSION_RECORD_ID='"+recordId+"' ORDER BY T.CREATE_TIME ASC ");
			return query.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据会话记录id，查询会话明细记录失败:"+e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 从全媒体话单中找出没有坐席资料的话单
	 * @return
	 */
	public List<EasyRow> findNoAgentInfoRecord() {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" SELECT * FROM V_PF_SESSION_RECORD T WHERE T.AGENT_NO IS NULL AND T.AGENT_ID IS NOT NULL ");
			return query.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 全媒体话单查询没有坐席资料的话单失败:"+e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 设置某条全媒体话单的坐席信息
	 * @param agent
	 */
	public boolean updateAgentInfo(String id,AgentModel agent) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" UPDATE ").append(Constants.SESSION_RECORD_TABLE_NAME).append(" SET 1=1 ");
			sql.append(agent.getAgentAcc()," ,AGENT_ACC =? ");
			sql.append(agent.getAgentNo()," ,AGENT_NO =? ");
			sql.append(agent.getAgentName()," ,AGENT_NAME =? ");
			sql.append(agent.getDeptCode()," ,AGENT_DEPT =? ");
			sql.append(agent.getAreaCode()," ,OP_AREA_CODE =? ");
			sql.append(agent.getEpCode()," ,EP_CODE =? ");
			sql.append(id," WHERE SERIAL_ID = ? ");
			query.execute(sql.toString(),sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 设置某条全媒体话单的坐席信息失败.",e);
		}
		return false;
	}

	/**
	 * 设置某条全媒体话单的坐席工号
	 * @param workNo
	 */
	public boolean updateAgentWorkNo(String id,String workNo) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(workNo," UPDATE ").append(Constants.SESSION_RECORD_TABLE_NAME).append(" SET AGENT_NO = ? ");
			sql.append(id," WHERE SERIAL_ID = ? ");
			query.execute(sql.toString(),sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 设置某条全媒体话单的坐席工号失败.",e);
		}
		return false;
		
	}

	/**
	 * 将已结束会话的全媒体的状态设置为待打标签的状态
	 */
	public boolean setTagStatus() {
		try {
			EasySQL sql = new EasySQL();
			sql.append(DictConstants.CALL_RECORD_STATUS_WAIT_TAG," UPDATE ").append(Constants.SESSION_RECORD_TABLE_NAME).append(" SET STATUS = ? ");
			sql.append(" WHERE  END_TIME IS NOT NULL AND STATUS IS NULL  ");
			query.execute(sql.toString(),sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 将已结束会话的全媒体的状态设置为待打标签的状态失败.",e);
		}
		return false;
	}

	
	/**
	 * 根据状态查询会话记录
	 * @param status
	 * @return
	 */
	public List<EasyRow> findSessionRecordByStatus(String status) {
		StringBuffer sql = new StringBuffer(" SELECT * from V_PF_SESSION_RECORD where STATUS='"+status+"'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 根据状态查询会话记录:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据状态查询会话记录失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 根据会话记录id，找出会话记录下面的所有会话明细记录
	 * @param id
	 * @return
	 */
	public List<EasyRow> findSessionRecordDetail(String id) {
		StringBuffer sql = new StringBuffer(" SELECT * from V_PF_SESSION_DETAIL where SESSION_RECORD_ID='"+id+"'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 根据会话ID查询所有的会话明细记录:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据会话ID查询所有的会话明细记录失败.",e);
		}
		return null;
	}
	
	/**
	 * 更新会话记录的状态
	 * @param id
	 * @param status
	 */
	public boolean updateSessionReocrdStatus(String id, String status) {
		StringBuffer sql = new StringBuffer(" UPDATE "+Constants.SESSION_RECORD_TABLE_NAME+" SET STATUS ='"+status+"' WHERE ID='"+id+"'  ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 更新会话记录的状态:"+sql.toString());
			queryHelper.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 更新会话记录的状态失败.",e);
		}
		return false;
	}
	

	/**
	 * 更新会话记录的质检记录id
	 * @param id
	 * @param qcRecordId
	 */
	public boolean updateSessionReocrdQcRecordId(String id, String qcRecordId,String status) {
		StringBuffer sql = new StringBuffer(" UPDATE C_PF_SESSION_RECORD SET QC_RECORD_ID ='"+qcRecordId+"',STATUS ='"+status+"' WHERE ID='"+id+"'  ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 更新通话记录的质检记录id:"+sql.toString());
			queryHelper.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 更新通话记录的质检记录id失败.",e);
		}
		return false;
	}
}
