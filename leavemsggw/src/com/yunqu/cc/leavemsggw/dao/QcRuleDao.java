package com.yunqu.cc.leavemsggw.dao;

import java.util.List;

import org.easitline.common.db.EasyRow;

import com.yq.busi.common.util.CommonUtil;

public class QcRuleDao extends BaseDao{

	
	/**
	 * 找出所有的自动质检规则
	 * @return
	 */
	public List<EasyRow> findAllQcRule(){
		StringBuffer sql = new StringBuffer(" SELECT * from C_PF_QC_RULE  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询所有的自动质检规则:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询所有的自动质检规则失败.",e);
		}
		return null;
	}

	/**
	 * 查询某条自动质检规则下的所有规则明细
	 * @return
	 */
	public List<EasyRow> findAllDetailByRuleId(String ruleId){
		StringBuffer sql = new StringBuffer(" SELECT * from C_PF_RULE_DETAIL where RULE_ID='"+ruleId+"'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询某条自动质检规则下的所有规则明细:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询某条自动质检规则下的所有规则明细失败.",e);
		}
		return null;
	}
	
	/**
	 * 查询出所有的自动质检指标
	 * @return
	 */
	public List<EasyRow> findAllQcKpi(){
		StringBuffer sql = new StringBuffer(" SELECT * from C_PF_QC_KPI WHERE QC_TYPE='01'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询出所有的自动质检指标:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询出所有的自动质检指标失败.",e);
		}
		return null;
	}
	
	/**
	 * 查询出某个指标下的所有考评分类
	 * @return
	 */
	public List<EasyRow> findAllQcCategoryByKpiId(String kpiId){
		StringBuffer sql = new StringBuffer(" SELECT * from C_PF_QC_KPI_CATEGORY WHERE KPI_ID='"+kpiId+"'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询出某个指标下的所有考评分类:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询出某个指标下的所有考评分类失败.",e);
		}
		return null;
	}
	
	/**
	 * 查询出某个考评分类下的所有考评细项
	 * @return
	 */
	public List<EasyRow> findAllQcItemByCategoryId(String categoryId){
		StringBuffer sql = new StringBuffer(" SELECT * from C_PF_QC_KPI_ITEM WHERE CATEGORY_ID='"+categoryId+"'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询出某个考评分类下的所有考评细项:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询出某个考评分类下的所有考评细项失败.",e);
		}
		return null;
	}
}
