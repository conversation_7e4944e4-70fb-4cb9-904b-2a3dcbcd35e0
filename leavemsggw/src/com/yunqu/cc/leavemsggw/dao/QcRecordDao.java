package com.yunqu.cc.leavemsggw.dao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.leavemsggw.model.QcReocrdModel;
import com.yunqu.cc.leavemsggw.model.QcSessionReocrdModel;
import com.yunqu.cc.leavemsggw.model.QcVoiceReocrdModel;

public class QcRecordDao extends BaseDao{
	
	private SessionRecordDao sessionRecordDao = new SessionRecordDao();

	/**
	 * 找出需要进行质检的语音会话记录
	 * @return
	 */
	public List<EasyRow> findNeedQcCallRecord(){
		StringBuffer sql = new StringBuffer(" SELECT V.TABLE_NAME,V.SESSION_TYPE,V.ID,V.ANSWER_TIME,V.END_TIME,V.AGENT_ACC,V.LENS,V.DIRECTION  ");
		sql.append("  FROM C_PF_V_CALL_RECORD V WHERE V.QC_RECORD_ID IS NULL AND V.ANSWER_TIME IS NOT NULL AND V.END_TIME IS NOT NULL AND V.STATUS='"+DictConstants.CALL_RECORD_STATUS_GEN_QC_RECORD+"'");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询需要进行质检的语音会话记录:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询需要进行质检的语音会话记录失败.",e);
		}
		return null;
	}

	/**
	 * 加载所有的可用的质检指标id
	 * key：会话类型_方向_质检类型
	 * @return
	 */
	public Map<String, String> loadAllQcKpiId() {
		StringBuffer sql = new StringBuffer(" SELECT T.SESSION_TYPE,T.DIRECTION,T.QC_TYPE,T.ID FROM C_PF_QC_KPI T WHERE T.ENABLE_STATUS='01' AND IS_DEFAULT='Y'  ");
		Map<String, String> map = new HashMap<String, String>();
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询所有的质检指标:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			if(CommonUtil.listIsNotNull(list)){
				for(EasyRow row :list){
					String sessionType = row.getColumnValue("SESSION_TYPE");
					String direction = row.getColumnValue("DIRECTION");
					String qcType = row.getColumnValue("QC_TYPE");
					String id = row.getColumnValue("ID");
					map.put(sessionType+"_"+direction+"_"+qcType, id);
				}
			}
			return map;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询所有的质检指标失败.",e);
		}
		return map;
	}

	/**
	 * 插入质检记录
	 * @param qcRecordId  质检记录id
	 * @param id          语音通话记录id(或会话记录id)
	 * @param sessionType 会话类型
	 * @param autoQcApiID 自动质检指标ID
	 * @param manualQcApiID 人工质检指标ID
	 * @param agentAcc      坐席账号
	 * @return
	 */
	public boolean addQcRecord(String qcRecordId, String id,String sessionType, String autoQcApiID, String manualQcApiID,
			String agentAcc) {
		StringBuffer sql = new StringBuffer(" INSERT INTO C_PF_QC_RECORD(ID,SESSION_RECORD_ID,STATUS,SESSION_TYPE,AUTO_QC_KPI_ID,MANUAL_QC_KPI_ID,CREATE_TIME,AGENT_ACC) VALUES(  ");
		sql.append("'").append(qcRecordId).append("',");
		sql.append("'").append(id).append("',");
		sql.append("'").append(DictConstants.QC_RECORD_STATUS_WAIT_AUTO_QC).append("',");
		sql.append("'").append(sessionType).append("',");
		sql.append("'").append(autoQcApiID).append("',");
		sql.append("'").append(manualQcApiID).append("',");
		sql.append("'").append(DateUtil.getCurrentDateStr()).append("',");
		sql.append("'").append(agentAcc).append("' ");
		sql.append(" ) ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 插入质检记录:"+sql.toString());
			
			//同时生成质检扩展记录数据
			StringBuffer sql2 = new StringBuffer();
			if(DictConstants.SESSION_TYPE_VOICE.equals(sessionType)){
				sql2.append(" INSERT INTO C_PF_QC_VOICE(ID,SESSION_RECORD_ID,QC_RECORD_ID) VALUES ('"+IDGenerator.getDefaultNUMID()+"','"+id+"','"+qcRecordId+"')");
			}else{
				sql2.append(" INSERT INTO C_PF_QC_SESSION(ID,SESSION_RECORD_ID,QC_RECORD_ID) VALUES ('"+IDGenerator.getDefaultNUMID()+"','"+id+"','"+qcRecordId+"')");
			}
			
			queryHelper.execute(sql2.toString());
			queryHelper.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 插入质检记录失败.",e);
		}
		return false;
	}

	/**
	 * 更新通话记录的质检记录id
	 * @param id
	 * @param qcRecordId
	 */
	public boolean updateCallReocrdQcRecordId(String id, String qcRecordId,String status) {
		StringBuffer sql = new StringBuffer(" UPDATE C_PF_CALL_RECORD SET QC_RECORD_ID ='"+qcRecordId+"',STATUS='"+status+"' WHERE ID='"+id+"'  ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 更新通话记录的质检记录id:"+sql.toString());
			queryHelper.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 更新通话记录的质检记录id失败.",e);
		}
		return false;
	}
	/**
	 * 更新通话记录的状态
	 * @param id
	 * @param status
	 */
	public boolean updateCallReocrdStatus(String id, String status) {
		StringBuffer sql = new StringBuffer(" UPDATE C_PF_CALL_RECORD SET STATUS ='"+status+"' WHERE ID='"+id+"'  ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 更新通话记录的状态:"+sql.toString());
			queryHelper.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 更新通话记录的状态失败.",e);
		}
		return false;
	}

	/**
	 * 找出需要生成质检记录的会话记录
	 * @return
	 */
	public List<EasyRow> findNeedQcSessionRecord() {
		StringBuffer sql = new StringBuffer(" SELECT V.TABLE_NAME,V.SESSION_TYPE,V.ID,V.ANSWER_TIME,V.END_TIME,V.AGENT_ACC,V.LENS,V.DIRECTION  ");
		sql.append("  FROM C_PF_V_SESSION_RECORD V WHERE V.QC_RECORD_ID IS NULL AND V.ANSWER_TIME IS NOT NULL AND V.END_TIME IS NOT NULL AND V.STATUS='"+DictConstants.CALL_RECORD_STATUS_GEN_QC_RECORD+"' ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询需要进行质检的全媒体会话记录:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询需要进行质检的全媒体会话记录失败.",e);
		}
		return null;
	}

	
	/**
	 * 根据质检记录id，查询该质检记录对应的详细信息：包括对应的会话记录、会话明细、标签明细
	 * @param qcRecordId
	 * @return
	 */
	public QcReocrdModel getQcRecordById(String qcRecordId) {
		StringBuffer sql = new StringBuffer(" SELECT * from C_PF_QC_RECORD WHERE ID='"+qcRecordId+"' ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询质检记录:"+sql.toString());
			EasyRow qcRecordRow = queryHelper.queryForRow(sql.toString(),null);
			if(qcRecordRow==null){
				logger.warn(CommonUtil.getClassNameAndMethod(this)+" 根据id无法查询到质检记录,id="+qcRecordId);
				return null;
			}
			
			//查询到质检记录
			QcReocrdModel model = new QcReocrdModel();
			model.setId(qcRecordRow.getColumnValue("ID"));
			model.setQcRecordRow(qcRecordRow);
			String sessionType = qcRecordRow.getColumnValue("SESSION_TYPE");
			model.setSessionType(sessionType);
			
			//根据会话类型，查询出通话记录
			String recordId = qcRecordRow.getColumnValue("SESSION_RECORD_ID");
			model.setSessionRecordId(recordId);
			
			EasyRow callRecordrow = null;
			if(DictConstants.SESSION_TYPE_VOICE.equals(sessionType)){
				callRecordrow = this.getCallRecordById(recordId);
				model.setCallRecordrow(callRecordrow);
				//查询出会话明细
				model.setCallRecordDetailList(getCallRecordDetailByRecordId(recordId));
				//查询出标签明细
				model.setCallKeyWordList(getCallKeywordListByRecordId(recordId));
				//质检扩展记录
				QcVoiceReocrdModel qcVoiceReocrdModel = new QcVoiceReocrdModel();
				qcVoiceReocrdModel.setQcRecordId(qcRecordId);
				qcVoiceReocrdModel.setSessionReocrdId(recordId);
				model.setQcVoiceReocrdModel(qcVoiceReocrdModel);
			}else if(DictConstants.SESSION_TYPE_MEDIA.equals(sessionType)){
				callRecordrow = sessionRecordDao.getSessionRecordById(recordId);
				model.setCallRecordrow(callRecordrow);
				//查询出会话明细
				model.setCallRecordDetailList(sessionRecordDao.getSessionRecordDetailByRecordId(recordId));
				//查询出标签明细
				model.setCallKeyWordList(getCallKeywordListByRecordId(recordId));
				//质检扩展记录
				QcSessionReocrdModel qcSessionReocrdModel = new QcSessionReocrdModel();
				qcSessionReocrdModel.setQcRecordId(qcRecordId);
				qcSessionReocrdModel.setSessionRecordId(recordId);
				model.setQcSessionReocrdModel(qcSessionReocrdModel);
			}
			
			return model;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询质检记录失败.",e);
		}
		
		return null;
		
	}



	/**
	 * 根据会话记录id，查询标签明细
	 * @param recordId
	 * @return
	 */
	private List<EasyRow> getCallKeywordListByRecordId(String recordId) {
		return getListById(queryHelper, "根据会话记录id，查询标签明细", "C_PF_CALL_KEYWORD", "CALL_ID", recordId);
	}

	/**
	 * 根据通话记录，查询转写明细
	 * @param recordId
	 * @return
	 */
	private List<EasyRow> getCallRecordDetailByRecordId(String recordId) {
		StringBuffer sql = new StringBuffer(" SELECT * from C_PF_CALL_RECORD_DETAIL where CALL_RECORD_ID=? order by  BEGIN_TIME asc ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 根据通话记录，查询转写明细:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),new Object[]{recordId});
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据通话记录，查询转写明细失败.",e);
		}
		return null;
	}

	/**
	 * 根据通话记录id，查询通话记录
	 * @param recordId
	 * @return
	 */
	private EasyRow getCallRecordById(String recordId) {
		return getRowById(queryHelper, "根据通话记录id，查询通话记录", "C_PF_V_CALL_RECORD", "ID", recordId);
	}

	/**
	 * 找出需要进行自动质检的记录
	 * @return
	 */
	public List<EasyRow> findNeedQcRecord() {
		StringBuffer sql = new StringBuffer(" SELECT * from C_PF_QC_RECORD where STATUS='01'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 找出需要进行自动质检的记录:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 找出需要进行自动质检的记录失败.",e);
		}
		return null;
	}

	/**
	 * 根据状态查询通话
	 * @param status
	 * @return
	 */
	public List<EasyRow> findCallRecordByStatus(String status) {
		StringBuffer sql = new StringBuffer(" SELECT * from C_PF_V_CALL_RECORD where STATUS='"+status+"'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 根据状态查询通话记录:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据状态查询通话记录失败.",e);
		}
		return null;
	}

	/**
	 * 根据通话记录唯一ID删除对应的所有通话转写明细结果
	 * @param callRecordId
	 * @return
	 */
	public boolean delCallRecordDetailByCallRecordId(String callRecordId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(callRecordId," DELETE FROM C_PF_CALL_RECORD_DETAIL WHERE CALL_RECORD_ID=? ");
			queryHelper.execute(sql.getSQL(), sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据通话记录唯一ID删除对应的所有通话转写明细结果.",e);
		}
		return false;
	}
	
	/**
	 * 根据通话记录唯一ID删除对应的会话明细关键字
	 * @param callRecordId
	 * @return
	 */
	public boolean delCallKeyWordByCallRecordId(String callRecordId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(callRecordId," DELETE FROM C_PF_CALL_KEYWORD WHERE CALL_ID=? ");
			queryHelper.execute(sql.getSQL(), sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据通话记录唯一ID删除对应的会话明细关键字.",e);
		}
		return false;
	}
	
	/**
	 * 根据通话记录唯一ID删除对应的关键字目录
	 * @param sessionId  通话记录或会话记录的唯一ID
	 * @return
	 */
	public boolean delCallKeyWordDirBySessionId(String sessionId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(sessionId," DELETE FROM C_PF_CALL_KEYWORD_DIR WHERE SESSION_ID=? ");
			queryHelper.execute(sql.getSQL(), sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据通话记录唯一ID删除对应的关键字目录.",e);
		}
		return false;
	}

	public void delZxResult(String callRecordId) {
		this.delCallRecordDetailByCallRecordId(callRecordId);
		this.delCallKeyWordByCallRecordId(callRecordId);
		this.delCallKeyWordDirBySessionId(callRecordId);
		
	}

	/**
	 * 查询需要获取录音的通话记录
	 * @return
	 */
	public List<EasyRow> findNeedSyncVoiceCallRecord() {
		StringBuffer sql = new StringBuffer(" SELECT V.ID,V.SESSION_ID,V.ID,V.CALL_ID  ");
		sql.append("  FROM C_PF_V_CALL_RECORD V WHERE V.END_TIME IS NOT NULL AND V.VOICE_URL IS NULL ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询需要获取录音的通话记录:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询需要获取录音的通话记录失败.",e);
		}
		return null;
	}
	
	/**
	 * 更改通话记录的录音地址
	 * @param id
	 * @param voiceName
	 */
	public boolean updateVoicePath(String id, String voiceName) {
		EasySQL sql=new EasySQL();
		 sql .append(voiceName," UPDATE C_PF_CALL_RECORD SET VOICE_URL =?  ",false);
		 sql .append(id," WHERE ID=? ",false);
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 更新会话记录的状态:"+sql.toString());
			queryHelper.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 更新会话记录的状态失败.",e);
		}
		return false;
		
	}

	/**
	 * 修改呼叫记录的满意度指标
	 * @param id
	 * @param statisfyCode
	 * @param bakup
	 * @param statisfyTime
	 */
	public boolean updateCallRecordStatisfaction(String id, String statisfyCode, String bakup, String statisfyTime) {
		StringBuffer sql = new StringBuffer(" UPDATE C_PF_CALL_RECORD SET SATISFACTION_CODE ='"+statisfyCode+"',SATISFACTION_BAKUP='"+bakup+"',SATISFACTION_TIME='"+statisfyTime+"' WHERE ID='"+id+"'  ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 修改呼叫记录的满意度指标:"+sql.toString());
			queryHelper.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 修改呼叫记录的满意度指标失败.",e);
		}
		return false;
	}
}
