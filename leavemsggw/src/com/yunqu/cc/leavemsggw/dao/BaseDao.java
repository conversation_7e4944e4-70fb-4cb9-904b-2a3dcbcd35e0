package com.yunqu.cc.leavemsggw.dao;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.leavemsggw.base.CommonLogger;
import com.yunqu.cc.leavemsggw.base.Constants;

public class BaseDao {

public static Logger logger =  CommonLogger.logger;
	
	protected EasyQuery queryHelper = null;
	
	
	public BaseDao() {
		queryHelper = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	}
	
	/**
	 * 根据某字段，从某个表里查询出集合
	 * @param queryHelper
	 * @param prompt    提示语
	 * @param tableName 表名
	 * @param idFiled   要作为查询条件的字段名
	 * @param id        字段值
	 * @return
	 */
	public List<EasyRow> getListById(EasyQuery queryHelper,String prompt,String tableName,String idFiled,String id) {
		StringBuffer sql = new StringBuffer(" SELECT * from ").append(tableName).append(" WHERE ").append(idFiled).append("='").append(id).append("'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+prompt+" :"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+prompt+" 失败.",e);
		}
		return null;
	}

	/**
	 * 根据id字段，从某个表里查询出唯一记录
	 * @param queryHelper
	 * @param prompt    提示语
	 * @param tableName 表名
	 * @param idFiled   要作为查询条件的字段名
	 * @param id        字段值
	 * @return
	 */
	public EasyRow getRowById(EasyQuery queryHelper,String prompt,String tableName,String idFiled,String id) {
		StringBuffer sql = new StringBuffer(" SELECT * from ").append(tableName).append(" WHERE ").append(idFiled).append("='").append(id).append("'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+prompt+" :"+sql.toString());
			EasyRow row = queryHelper.queryForRow(sql.toString(),null);
			return row;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+prompt+" 失败.",e);
		}
		return null;
	}
	
	/**
	 * 批量执行sql
	 * @param queryHelper
	 * @param sqlList
	 * @throws SQLException
	 */
	public void batchExecSql(EasyQuery queryHelper,ArrayList<String> sqlList) throws SQLException {
		if(CommonUtil.listIsNull(sqlList)){
			return ;
		}
		if(queryHelper!=null){
			queryHelper.executeBatch(sqlList);
		}else{
			this.queryHelper.executeBatch(sqlList);
		}
	}
	
	/**
	 * 对json里的记录新增或修改到对应的表里
	 * json里如有ID字段，则表示新增，否则为修改
	 * @param json
	 */
	public boolean saveOrUpdate(String tableName,JSONObject json) {
		if(StringUtils.isBlank(tableName)){
			return false;
		}
		boolean insert = false;
		String id = json.getString("ID");
		if(StringUtils.isBlank(id)){
			id = IDGenerator.getDefaultNUMID();
			json.put("ID", id);
			insert = true;
		}
		StringBuffer sql = new StringBuffer();
		StringBuffer valuesSql = new StringBuffer();
		
		if(insert){//新增
			sql.append(" INSERT INTO ").append(tableName).append("(");
			Set<String> keys = json.keySet();
			for(String key : keys){
				sql.append(key).append(",");
				Object obj = json.get(key);
				if(obj instanceof String){
					valuesSql.append("'").append(obj).append("',");
				}else{
					valuesSql.append("").append(obj).append(",");
				}
				
			}
			//删除最后一个","
			sql.deleteCharAt(sql.length()-1);
			valuesSql.deleteCharAt(valuesSql.length()-1);
			
			sql.append(") VALUES (").append(valuesSql.toString()).append(")");
		}else{
			
			sql.append(" UPDATE ").append(tableName).append(" SET ");
			Set<String> keys = json.keySet();
			for(String key : keys){
				sql.append(key).append("=");
				Object obj = json.get(key);
				if(obj instanceof String){
					valuesSql.append("'").append(obj).append("',");
				}else{
					valuesSql.append("").append(obj).append(",");
				}
				
			}
			//删除最后一个","
			sql.deleteCharAt(sql.length()-1);
			sql.append(" WHERE ID = '").append(id).append("'");
		}
		
		//执行sql
		try {
			queryHelper.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 执行新增或修改sql时出错",e);
		}
		return false;
	}

	public EasyQuery getQueryHelper() {
		return queryHelper;
	}

}
