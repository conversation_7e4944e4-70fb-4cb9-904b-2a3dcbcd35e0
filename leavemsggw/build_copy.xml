<?xml version="1.0" encoding="UTF-8"?>
<project name="makewar" default="movewar" basedir=".">

	<property name="DestDir" location="" />
	<property name="WebDir" location="WebContent" />
	<property name="mars_home" location="D:\work\server\mars\mars-server-2.1.2\" />
	
	<target name="cleanDir" description="清除文件">
				
	</target>
	
	<target name="makewar" description="Create a war for the module">
		<jar jarfile="${DestDir}/leavemsggw.war" basedir="${WebDir}" />
	</target>

	<target name="movewar" description="Create a war for the module" depends="makewar">
		<copy todir="${mars_home}\webapps" overwrite="true" >
				<fileset dir="./">
					<include name="leavemsggw.war" />
				</fileset>
		</copy>	
	</target>
	
</project>

