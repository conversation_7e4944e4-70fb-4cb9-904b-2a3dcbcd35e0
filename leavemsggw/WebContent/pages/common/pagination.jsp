<%@ include file="/pages/common/global.jsp" %>
<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8" %>
	<div class="_pagination" style="display: inline-block;position: relative;float: left;margin-left: 15px;">
			<input type="hidden" name="pageIndex" class="pageIndexV" value="-1">
			<input type="hidden" class="totalPageV" value="0">
			<input type="hidden" class="totalRowV" value="0">
			<c:choose>
				<c:when test="${empty param.pageSizes}">
					<c:set var="pageSizes" value="5,10,25,50,100"></c:set>
					<c:set var="pageSize" value="10"></c:set>
				</c:when>
				<c:otherwise>
					<c:set var="pageSizes" value="${param.pageSizes}"></c:set>
					<c:set var="pageSize" value="${param.pageSize}"></c:set>
				</c:otherwise>
			</c:choose>
			 <select name="pageSize" class="form-control input-sm pageSizeV" style="width: 90px;display: inline-block;height: 28px;padding: 2px 5px">
					<c:forEach items="${fn:split(pageSizes,',')}"  var="val" varStatus="vs">
	                         <option value="${val}"  <c:if test="${pageSize==val}">selected="selected"</c:if>>${val} 条/页</option>
	                </c:forEach>
			</select>&nbsp;
			 共 <span class="totalRow"></span>&nbsp;条 ,&nbsp;<span class="totalPage"></span> 页
	</div>
	 <ul class="pagination pagination-sm pageNumV"  style="margin: 0px"></ul>
