package com.yunqu.project.dao;

import cn.hutool.core.stream.StreamUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.*;
import com.yunqu.project.base.AppDaoContext;
import com.yunqu.project.base.Constants;
import com.yunqu.project.utils.CacheLongTxtUtil;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@WebObject(name = "sessionTask")
public class SessionTask_Dao extends AppDaoContext {
	
	/**
	 * 任务抽取查询
	 * @return
	 * @throws SQLException 
	 */
	@WebControl(name = "taskQuery", type = Types.LIST)
	public JSONObject taskQuery() throws SQLException {
		Object status = this.getMethodParam(0);
		if (status == null) {
			status = DictConstants.QC_RECORD_STATUS_QC_SUCC;
		}
		String queryType = param.getString("queryType");
		
		//抽取类型,1-指定抽取，2-随机抽取
		String extractType = param.getString("extractType");
		if(StringUtils.isBlank(extractType)){
			extractType = "1";
		}
		
		String date = DateUtil.getCurrentDateStr("yyyy-MM");
		UserModel userModel = UserUtil.getUser(request);
		String epCode = userModel.getEpCode();
		
		EasySQL sql = getEasySQL("select distinct t2.ID VID,t1.AUTO_QC_SCORE_ID,t2.HANGUP_TYPE, t1.QC_SCORE_ID, t1.ID, t1.QC_ACC, t1.QC_NAME, t2.CUSTOMER_ACC, t2.CUSTOMER_NAME, t2.DIRECTION, t2.OP_AREA_CODE, t2.ANSWER_TIME, t2.END_TIME, t2.LENS, t2.CHANNEL_ID, ");
		sql.append("t2.SATISF_CODE, t1.AUTO_QC_SCORE, t1.QC_SCORE, t2.AGENT_NAME, t2.AGENT_ACC, t3to.WEEK_MEDIA_LISTEN_RATE, t3to.MEDIA_LISTEN_RATE,t3to.MEDIA_LISTEN_PER_RATE, t4.ENTRY_TIME,t8.ORDER_TYPE_FIRST, t2.SATISF_CODE as SATISFACTION_CODE,t2.SATISF_NAME as SATISFACTION_NAME ");
		if (DictConstants.QC_RECORD_STATUS_DONE.equals(status)) {//如果状态为质检完成，则需要查询辅导次数、申述次数
			sql.append(", t1.TUTORING_NUM");
			sql.append(", t1.COMPLAINT_TIMES");
			sql.append(", CYWR.WIN_TYPE");
		}
		sql.append(", t1.STATUS,ENTRY_MONTHS");
		sql.append(", s.FIRST_SATISFY_NAME,s.SECOND_SATISFY_NAME,s.THIRD_SATISFY_NAME ");
		sql.append(" from C_PF_QC_RECORD t1 ");
		sql.append("right join V_PF_SESSION_RECORD t2 on t1.ID = t2.QC_RECORD_ID ");
		/*sql.append(date, "left join C_PF_ST_AGENT_MONTH t3 on t1.AGENT_ACC = t3.CREATE_ACC and t3.STAT_MONTH=? ");*/
		sql.append(date, "left join C_ST_AGENT t3 on t1.AGENT_ACC = t3.USER_ACC and t3.ST_TYPE='02' and t3.ST_DATE=? left join C_ST_AGENT_LISTEN_RATE t3to on t3.id=t3to.ST_AGENT_ID ");
		sql.append("left join C_YG_EMPLOYEE t4 on t4.USER_ACC = t2.AGENT_ACC ");
		//sql.append("left join C_NO_APPEALLIST t6 on t2.SH_ORDER_ID = t6.CONTACT_SERAIL_ID ");
		//sql.append("left join C_NO_CONTACT t7 on t2.ID = t7.SESSION_ID ");
		//sql.append("left join C_NO_APPEALLIST t8 on t7.ID = t8.CONTACT_SERAIL_ID ");
		sql.append("left join C_OL_CONSULT_ORDER t8 ON t8.SESSION_ID = t2.ID ");
		sql.append("left join V_PF_SESSION_DETAIL IL ON t2.ID = IL.SESSION_RECORD_ID  ");
		sql.append("left join C_YG_WIN_RECORD CYWR on t2.AGENT_ACC = CYWR.USER_ACC ");
		sql.append("left join C_CF_SATISFY_RECORD s on s.SESSION_ID = t2.ID ");
		// 满意度多选树选中值处理
		String satisfyLev1Ids = param.getString("satisfyLev1Ids");
		String satisfyLev2Ids = param.getString("satisfyLev2Ids");
		String satisfyLev3Ids = param.getString("satisfyLev3Ids");
		String satisfyLev1Codes = param.getString("satisfyLev1Codes");
		if (StringUtils.isNotBlank(satisfyLev3Ids)) {
			sql.append("left join C_CF_SATISFY_THIRD_RECORD st on  s.ID = st.THIRD_ID ");
		}
		//查询上个月质检得分时，需要关联分组查询质检表
		if (StringUtils.isNotBlank(param.getString("lastAvgScoreStart")) || StringUtils.isNotBlank(param.getString("lastAvgScoreEnd"))) {
			sql.append("left join ("
					+ "select CPQR.AGENT_ACC, AVG(CPQR.QC_SCORE) as AVG_QC_SCORE "
					+ "from C_PF_QC_RECORD CPQR "
					+ "where CPQR.SESSION_TYPE = '"+DictConstants.SESSION_TYPE_MEDIA+"' ");
			String theMonthBeginTime = DateUtil.getCurrMonthBeginDay(DateUtil.TIME_FORMAT_YMD)+" 00:00:00";
			String theMonthEndTime = DateUtil.getCurrMonthEndDay(DateUtil.TIME_FORMAT_YMD)+" 23:59:59";
			sql.append(DateUtil.addMonth(DateUtil.TIME_FORMAT, theMonthBeginTime, -1), "and CPQR.MANUAL_QC_TIME >= ? ");
			sql.append(DateUtil.addMonth(DateUtil.TIME_FORMAT, theMonthEndTime, -1), "and CPQR.MANUAL_QC_TIME <= ? ");
			sql.append("group by CPQR.AGENT_ACC ");
			sql.append(") CPQR on t1.AGENT_ACC = CPQR.AGENT_ACC ");
		}
		sql.append("where 1=1 ");
		// 满意度过滤
		if (StringUtils.isNotBlank(satisfyLev1Ids) || StringUtils.isNotBlank(satisfyLev2Ids) || StringUtils.isNotBlank(satisfyLev3Ids)) {
			sql.append("and ( ");
			if (StringUtils.isNotBlank(satisfyLev1Ids)) { // 一级满意度过滤
				sql.append(" (s.FIRST_SATISFY_ID in ('-1'");
				for (String code : satisfyLev1Ids.split(",")) {
					sql.append(code, ", ?");
				}
				sql.append(")) or ");
				// code过滤
				sql.append(" (s.FIRST_SATISFY_CODE in ('-1'");
				for (String code : satisfyLev1Codes.split(",")) {
					sql.append(code, ", ?");
				}
				sql.append(")) or ");
			}
			if (StringUtils.isNotBlank(satisfyLev2Ids)) { // 二级满意度过滤
				sql.append(" (s.SECOND_SATISFY_ID in ('-1'");
				for (String code : satisfyLev2Ids.split(",")) {
					sql.append(code, ", ?");
				}
				sql.append(")) or ");
			}
			if (StringUtils.isNotBlank(satisfyLev3Ids)) { // 三级满意度过滤
				sql.append(" (st.THIRD_SATISFY_ID in ('-1'");
				for (String code : satisfyLev3Ids.split(",")) {
					sql.append(code, ", ?");
				}
				sql.append(")) or ");
			}
			sql.append(" 0=1 )");
		}
		//是否视频客服
		String hasVideo = param.getString("hasVideo");
		if("Y".equals(hasVideo)){
		   sql.append("and exists(select 1 from ycbusi.CC_MEDIA_VIDEO_RECORD t5 where t5.CHAT_SESSION_ID = t1.SESSION_RECORD_ID and t5.TOTAL_TIME >0)");
		}else if("N".equals(hasVideo)){
		   sql.append("and not exists(select 1 from ycbusi.CC_MEDIA_VIDEO_RECORD t5 where t5.CHAT_SESSION_ID = t1.SESSION_RECORD_ID and t5.TOTAL_TIME >0)");
		}
		
		//最复杂的条件查询放到最前面，提高查询效率
		if (StringUtils.isNotBlank(param.getString("KEYWORD_DIR_ID"))) {
			//sql.append("and exists (select 1 from C_PF_CALL_KEYWORD CPCK where CPCK.CALL_ID = t2.ID ");
			//sql.appendLike(param.get("keyWord"), "and CPCK.KEYWORD like ? ");//关键字查询
			sql.append(param.get("KEYWORD_DIR_ID"), "and exists (select 1 from C_PF_CALL_KEYWORD CCKD "
					+ "where CCKD.CALL_ID = t2.ID and CCKD.KEYWORD_DIR_ID = ?  ) ");
			//sql.append(") ");
		}
		//查询上个月质检得分时，需要关联分组查询质检表
		if (StringUtils.isNotBlank(param.getString("lastAvgScoreStart")) || StringUtils.isNotBlank(param.getString("lastAvgScoreEnd"))) {
			sql.append(param.get("lastAvgScoreStart"), "and CPQR.AVG_QC_SCORE >= ? ");
			sql.append(param.get("lastAvgScoreEnd"), "and CPQR.AVG_QC_SCORE <= ? ");
		}
		if (StringUtils.isNotBlank(param.getString("qcTimeStart")) || StringUtils.isNotBlank(param.getString("qcTimeEnd"))) {
			sql.append("and exists (select 1 from C_PF_QC_SCORE CPQS where t1.QC_SCORE_ID = CPQS.ID ");
			sql.append(param.get("qcTimeStart"), "and CPQS.SCORE_TIME >= ? ");
			sql.append(param.get("qcTimeEnd"), "and CPQS.SCORE_TIME <= ? ");
			sql.append(") ");
		}
		if (DictConstants.QC_RECORD_STATUS_QC_SUCC.equals(status)) {//如果状态为带抽取，需要判断voc的状态是否为01和04
			sql.append("and (t1.VOC_STATUS in ('"+DictConstants.QC_RECORD_VOC_STATUS_NOT+"', '"+DictConstants.QC_RECORD_VOC_STATUS_AUDIT_UNPASS+"')or t1.VOC_STATUS is null ) ");
		}
		sql.append(param.get("labelId"), "and exists (select 1 from C_PF_QC_GROUP CPQG, C_PF_QC_GROUP_MEMBER CPQGM where CPQG.ID=CPQGM.QC_GROUP and CPQGM.QC_ACC=t2.AGENT_ACC and CPQG.ID=?) ");
		sql.appendLike(param.getString("CONTENT")," AND  IL.CONTENT like ?");//关键字
		sql.append(param.get("serviceRequire"), "and t8.ORDER_TYPE_FIRST = ? ");
		sql.append(param.get("entryMonthsStart"), "and t4.ENTRY_MONTHS >= ? ");
		sql.append(param.get("entryMonthsEnd"), "and t4.ENTRY_MONTHS <= ? ");
		sql.append(param.get("entryEnd"), "and t2.ANSWER_TIME <= ? ");//开始时间
		sql.append(param.get("entryStart"), "and t2.ANSWER_TIME >= ? ");
		sql.append(param.getString("HANGUP_TYPE")," and  t2.HANGUP_TYPE = ?");//挂断类型
		sql.append(param.get("entryTimeStart"), "and t4.ENTRY_TIME <= ? ");//入职时间
		sql.append(param.get("entryTimeEnd"), "and t4.ENTRY_TIME >= ? ");
		sql.append(param.get("weekVoiceListenRateStart"), "and t3to.WEEK_MEDIA_LISTEN_RATE >= ? ");//本周监听率
		sql.append(param.get("weekVoiceListenRateEnd"), "and t3to.WEEK_MEDIA_LISTEN_RATE <= ? ");
		sql.append(param.get("voiceListenRateStart"), "and t3to.MEDIA_LISTEN_RATE >= ? ");//本月监听率
		sql.append(param.get("voiceListenRateEnd"), "and t3to.MEDIA_LISTEN_RATE <= ? ");
		sql.append(param.get("mediaPerMonthRateStart"), "and t3to.MEDIA_LISTEN_PER_RATE >= ? ");//绩效月监听率
		sql.append(param.get("mediaPerMonthRateEnd"), "and t3to.MEDIA_LISTEN_PER_RATE <= ? ");
		sql.append(param.get("lensStart"), "and t2.LENS >= ? ");
		sql.append(param.get("lensEnd"), "and t2.LENS <= ? ");
		sql.append(param.get("autoQcScoreStart"), "and t1.AUTO_QC_SCORE >= ? ");
		sql.append(param.get("autoQcScoreEnd"), "and t1.AUTO_QC_SCORE <= ? ");
		sql.append(param.get("opAreaCode"), "and t2.OP_AREA_CODE = ? ");//区域查询
		sql.append(param.get("direction"), "and t2.DIRECTION = ? ");
		sql.append(param.get("agentDept"), "and t2.AGENT_DEPT = ? ");
		sql.append(param.get("CHANNEL_ID"), "and CHANNEL_ID = ? ");
		sql.append(param.get("agentAcc"), "and t2.AGENT_ACC = ? ");
		sql.appendRLike(param.get("agentAcc_like"), "and t2.AGENT_ACC like ? ");
		sql.appendRLike(param.get("agentName_like"), "and t2.AGENT_NAME like ? ");
		sql.append(param.get("ygType"), "and CYWR.WIN_TYPE = ? ");
		sql.appendRLike(param.get("qcAcc_like"), "and t1.QC_ACC like ? ");
		sql.appendRLike(param.get("qcName_like"), "and t1.QC_NAME like ? ");
		sql.append(param.get("satisfactionCode"), "and t2.SATISF_NAME = ? ");//满意度编号
		sql.append(param.get("inType"), "and t2.IN_TYPE = ? ");//接入类型
		sql.appendRLike(param.get("custAcc"), "and t2.CUSTOMER_ACC like ? ");
		sql.appendRLike(param.get("custName"), "and t2.CUSTOMER_NAME like ? ");
		sql.append(param.get("status"), "and t1.STATUS = ? ");

		if ("fd".equals(queryType)) {
			sql.append("and t1.NEED_TOTURING = 'Y' ");
		}
		if (DictConstants.QC_RECORD_STATUS_WAIT_MQC.equals(status) || "99".equals(status)) {
			String userAcc = userModel.getUserAcc();
			sql.append(userAcc, "and t1.QC_ACC = ? ");
		}
		//拆分 自动质检抽取取消关联 
		if (DictConstants.QC_RECORD_STATUS_QC_SUCC.equals(status)) {
			//当没有质检分数查询条件时，应该查询状态为04的质检记录，也就是查询可以被人工抽取的记录，因为状态02只有被人工抽取的情况下才能被抽检
			if (StringUtils.isBlank(param.getString("autoQcScoreStart")) 
					&& StringUtils.isBlank(param.getString("autoQcScoreEnd"))) {
				//sql.append("and t1.ALLOW_MANUAL_QC = 'Y' ");
			}
			sql.append("and ( t1.STATUS in ('"+DictConstants.QC_RECORD_STATUS_WAIT_AUTO_QC+"', '"+DictConstants.QC_RECORD_STATUS_QC_SUCC+"', '"+DictConstants.QC_RECORD_STATUS_WAIT_EXTRACT+"') OR t1.VOC_STATUS IS NULL)");
		} else if ("99".equals(status)) {//自定义的值，用于查询07、08的质检记录
			sql.append("07", "and t1.STATUS in (?, ");
			sql.append("08", "? ) ");
		} else if ("98".equals(status)) {
			sql.append("05", "and t1.STATUS in (?, ");
			sql.append("06", "? ) ");
		} else if ("97".equals(status)) {
			sql.append("06", "and t1.STATUS in (?, ");
			sql.append("07", "?) ");
		} else {
			sql.append(status, "and t1.STATUS = ? ");
		}
		sql.append(DictConstants.SESSION_TYPE_MEDIA, "and t1.SESSION_TYPE = ? ");
		
		
		sql.append(epCode, "and t2.EP_CODE = ? ");
		/*sql.append("order by t2.ANSWER_TIME desc ");*/
		/*sql.append("order by dbms_random.value() ");*/
		if("1".equals(extractType)){
			sql.append("order by t2.ANSWER_TIME desc ");
		}
		logger.info(CommonUtil.getClassNameAndMethod(this)+ "查询状态为["+status+"]的任务记录sql：" + sql.getSQL(), null);
		//对于指定抽取的,只需要将数据列出所有数据，给用户自行选择抽取即可
				if("1".equals(extractType)){
					//进行信息脱敏
					JSONObject obj = this.queryForPageList(sql.getSQL(), sql.getParams());
					JSONObject str=new JSONObject().fluentPut("CUSTOMER_NAME", PrivacyUtil.userName);
					obj = PrivacyUtil.desensitization(obj, str);
					return obj;
				}
				
				//对于随机抽取的，需要结合每个坐席的被抽检情况，根据抽检率筛选出每个坐席能被抽检多少条记录
				EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
				query.setMaxRow(500000);
				List<EasyRow>  list = query.queryForList(sql.getSQL(), sql.getParams());
				int pageIndex = param.getInteger("pageIndex"); // -1 开始
				int pageSize = param.getInteger("pageSize");// 默认为10
				int totalPage = 0;
				int total = 0;
				JSONArray array = new JSONArray();
				
				
				//集合不为空时
				if(CommonUtil.listIsNotNull(list)){
					//当天每人能被抽取的最大数据
					int maxRandomExtractDay = CommonUtil.parseInt(ConfigUtil.getString(Constants.APP_NAME, "MAX_RANDOM_EXTRACT_DAY", "2"));
					
					//抽检率
					double extractRate = param.getDoubleValue("extractRate");
					if(extractRate<=0){
						extractRate = 1;
					}
					String currDate = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
					
					
					//加载集合
					int begin = (  ( pageIndex<0 ? 1 : pageIndex )-1 ) * pageSize;
					int end = begin+pageSize-1;
					
					//获取各个坐席话单集合
					Map<String,List<EasyRow>> agentRowMap  = new HashMap<String,List<EasyRow>>();
					
					Map<String,Integer> agentExtractNumMap = new HashMap<String,Integer>();
					
					for(EasyRow row : list){
						String agentAcc = row.getColumnValue("AGENT_ACC");
						//坐席当天被抽取量
						Integer agentExtractNum = agentExtractNumMap.get(agentAcc);
						if(agentExtractNum==null){
							agentExtractNum = getAgentExtractNum(agentAcc,currDate);
							agentExtractNumMap.put(agentAcc, agentExtractNum);
						}
						if(agentExtractNum>=maxRandomExtractDay){
							continue;
						}
						
						List<EasyRow> agentRowList = agentRowMap.get(agentAcc);
						if(agentRowList==null	){
							agentRowList = new ArrayList<EasyRow>();
							agentRowMap.put(agentAcc, agentRowList);
						}
						agentRowList.add(row);
					}
					
					List<EasyRow>  totalList = new ArrayList<EasyRow>();
					
					//根据抽检率从各个坐席的话单集合里抽取记录，形成新集合
					for(String agentAcc : agentRowMap.keySet()){
						List<EasyRow> rowList = agentRowMap.get(agentAcc);
						int size = rowList.size();
						if(size<0){
							continue;
						}
						//坐席当天被抽取量
						int agentExtractNum = getAgentExtractNum(agentAcc,currDate);
						//按监听率要抽取该坐席的数据，监听率*坐席话单记录 ,往下取整
						double extract =  Math.floor( (double)(extractRate*rowList.size()/100));
						//抽取数不足1时，取1
						int extractSize = extract<1 ? 1 : (int)extract;
						
						for(int i=0;i<extractSize;i++){
							if(agentExtractNum>maxRandomExtractDay){
								break;
							}
							EasyRow r = rowList.remove((int)(Math.random()*size));
							totalList.add(r);
							agentExtractNum++;
						}
					}
					//根据新集合分页
					total = totalList.size();
					totalPage =  (total %pageSize ==0 ) ? total / pageSize : total / pageSize + 1;
					
					//加载出本页要显示的记录
					for(int i=0;i<total;i++){
						if(array.size() == pageSize){
							break;
						}
						EasyRow row = totalList.get(i);
						if(i>=begin && i<=end){
							array.add(row.toJSONObject());
						}
					}
					//清除数据，释放内存
					agentRowMap.clear();
					list.clear();
					totalList.clear();
				}
				
				
				
				JSONObject json = new JSONObject();
				json.put("msg", "请求成功");
				json.put("totalRow", total 	);
				json.put("pageNumber", pageIndex);
				json.put("pageType", 1);
				json.put("totalPage", totalPage);
				json.put("pageSize", pageSize);
				json.put("state", 1);
				json.put("data", array);
				
				return json;
	}
	
	/**
	 * 往缓存里写入坐席抽取数量
	 * @param agentAcc
	 * @param currDate
	 */
	private void putAgentExtractNum(String agentAcc,String currDate) {
		try {
			int num = getAgentExtractNum(agentAcc,currDate);
			cache.put(agentAcc+"_"+currDate, num+1,72000); //设置有效期为20小时，避免缓存数据太多，隔天后由缓存自行清理
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 往缓存里写入坐席抽取数量时失败",e);
		}
	}

	/**
	 * 往缓存里获取坐席抽取数量
	 * @param agentAcc
	 * @param currDate
	 * @return
	 */
	private int getAgentExtractNum(String agentAcc,String currDate) {
		if(cache==null){
			return 0;
		}
		try {
			//return (int)cache.get(agentAcc+"_"+currDate);
			return cache.get(agentAcc+"_"+currDate)==null?0:(int)cache.get(agentAcc+"_"+currDate);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 往缓存里获取坐席抽取数量时失败",e);
		}
		
		return 0;
	}

	/**
	 * 任务抽取
	 * @return
	 * @throws SQLException
	 */
	@WebControl(name = "taskExtraction", type = Types.RECORD)
	public EasyResult taskExtraction() {
		try {		
			String ids = param.getString("ids");
			UserModel userModel = UserUtil.getUser(request);
			String userAcc = userModel.getUserAcc();
			String userName = userModel.getUserName();
			String cqTime = DateUtil.getCurrentDateStr();
			String newStatus = DictConstants.QC_RECORD_STATUS_WAIT_ASSIGN;
			String oldStatus = "'"+DictConstants.QC_RECORD_STATUS_QC_SUCC+"', '"+DictConstants.QC_RECORD_STATUS_WAIT_EXTRACT+"'";
			
			String[] idArray = ids.split(",");
			List<Object[]> params = new ArrayList<>();
			List<Object[]> logParams = new ArrayList<>();
			EasyQuery query = this.getQuery();

			Map<String,String> cacheMap = this.loadQcKpiIdByQcType("02");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务抽取["+oldStatus +"->"+newStatus +"]，渠道考评标准："+JSON.toJSONString(cacheMap));

			EasySQL easySQL = new EasySQL("SELECT * FROM V_PF_SESSION_RECORD WHERE ");
			easySQL.append(" QC_RECORD_ID IN ('"+String.join("','",idArray)+"')");
			List<EasyRow> rows = query.queryForList(easySQL.getSQL(),easySQL.getParams());
			Map<String,EasyRow> map = StreamUtil.of(rows).collect(Collectors.toMap(r->r.getColumnValue("QC_RECORD_ID"),r->r));

			for (String id : idArray) {
				EasyRow row = map.get(id);
				if (row == null) continue;
				String channelId = row.getColumnValue("CHANNEL_ID");
				String sessionType = row.getColumnValue("SESSION_TYPE");
				String direction = row.getColumnValue("DIRECTION");
				String key = channelId + "_" + sessionType + "_" + direction;
				String kpiId = cacheMap.get(key);

				params.add(new String[]{newStatus ,kpiId, id});
				Object[] logParam = new Object[]{
						IDGenerator.getDefaultNUMID(), id, userAcc, userName, cqTime						
				};
				logParams.add(logParam);
			}

			//任务抽取
			String sql = "update C_PF_QC_RECORD t1 set t1.STATUS = ?,t1.MANUAL_QC_KPI_ID = ? where id = ? and (t1.STATUS is  null or t1.STATUS in ('"+DictConstants.QC_RECORD_STATUS_WAIT_AUTO_QC+"','"+DictConstants.QC_RECORD_STATUS_QC_SUCC+"', '"+DictConstants.QC_RECORD_STATUS_WAIT_EXTRACT+"') )";
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务抽取["+oldStatus +"->"+newStatus +"]sql:"+ sql +", params:" + JSON.toJSONString(params));
			query.executeBatch(sql, params);
			//插入抽取日志
			sql = "insert into C_PF_QC_ASSIGN(ID, QC_RECORD_ID, CQ_ACC, CQ_NAME, CQ_TIME) values(?, ?, ?, ?, ?)";
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务抽取日志sql:"+ sql, null);
			query.executeBatch(sql, logParams);
			
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "任务抽取异常:"+ e.getMessage(), e);
			return EasyResult.error(500, "操作异常:"+ e.getMessage());
		}
	}
	public Map<String, String> loadQcKpiIdByQcType(String qcTypeParam) {
		EasySQL sql = new EasySQL(" SELECT T.CHANNEL_ID,T.SESSION_TYPE,T.DIRECTION,T.QC_TYPE,T.ID FROM C_PF_QC_KPI T WHERE T.ENABLE_STATUS='01' AND IS_DEFAULT='Y'  ");
		sql.append(qcTypeParam," AND QC_TYPE=? ");
		Map<String, String> map = new HashMap<String, String>();
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 查询所有的质检指标:"+sql.toString());
			List<EasyRow> list = this.getQuery().queryForList(sql.getSQL(),sql.getParams());
			if(CommonUtil.listIsNotNull(list)){
				for(EasyRow row :list){
					String channelIds = row.getColumnValue("CHANNEL_ID");
					for (String channelId : channelIds.split(",")) {
						String sessionType = row.getColumnValue("SESSION_TYPE");
						String direction = row.getColumnValue("DIRECTION");
						String id = row.getColumnValue("ID");
						map.put(channelId+"_"+sessionType+"_"+direction, id);
					}
				}
			}
			return map;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询所有的质检指标失败.",e);
		}
		return map;
	}
	private String getDefaultKpiId(Map<String,String> cacheMap,String channelId, String sessionType, String direction) throws SQLException {
		String key = channelId + "_" + sessionType + "_" + direction;
		String kpiId = cacheMap.get(key);
		if(kpiId!=null){
			if (StringUtils.equals("-1",kpiId)) kpiId = "";
			return kpiId;
		}

		EasySQL kpiSql = new EasySQL(" SELECT T.ID FROM C_PF_QC_KPI T WHERE T.ENABLE_STATUS='01' ");
		kpiSql.append("AND IS_DEFAULT='Y'");
		kpiSql.appendLike(channelId," and CHANNEL_ID LIKE ? ");
		kpiSql.append(sessionType," and SESSION_TYPE=? ");
		kpiSql.append(direction," and DIRECTION=? ");
		kpiSql.append("02"," and QC_TYPE=? ");
		JSONObject kpiInfo = getQuery().queryForRow(kpiSql.getSQL(), kpiSql.getParams(),new JSONMapperImpl());
		if(kpiInfo!=null){
			kpiId = kpiInfo.getString("ID");
		}else {
			kpiId = "-1";
		}
		cacheMap.put(key, kpiId);
		return kpiId;
	}

	/**
	 * 任务发布
	 * @return
	 * @throws SQLException
	 */
	@WebControl(name = "taskRelease", type = Types.RECORD)
	public EasyResult taskRelease() {
		try {		
			UserModel userModel = UserUtil.getUser(request);
			String userAcc = userModel.getUserAcc();
			String userName = userModel.getUserName();
			String cqTime = DateUtil.getCurrentDateStr();
			String newStatus = DictConstants.QC_RECORD_STATUS_WAIT_RECEIVE;
			String oldStatus = DictConstants.QC_RECORD_STATUS_WAIT_ASSIGN;
			String ids = param.getString("ids");
			
			String[] idArray = ids.split(",");
			List<Object[]> params = new ArrayList<>();
			List<Object[]> logParams = new ArrayList<>();
			for (String id : idArray) {
				params.add(new String[]{newStatus, cqTime, id, oldStatus});
				Object[] logParam = new Object[]{
						userAcc, userName, cqTime, "01", id					
				};
				logParams.add(logParam);
			}

			EasyQuery query = this.getQuery();
			//任务发布
			String sql = "update C_PF_QC_RECORD t1 set t1.STATUS = ?, t1.MANUAL_QC_ASSIGN_TIME = ? where id = ? and t1.STATUS = ?";
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务发布["+oldStatus +"->"+newStatus +"]sql:"+ sql +", ids:" +ids, null);
			query.executeBatch(sql, params);
			//插入发布日志
			sql = "update C_PF_QC_ASSIGN set ASSIGN_ACC = ?, ASSIGN_NAME = ?, ASSIGN_TIME = ?, ASSIGN_TYPE = ? where ID = ?";
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务发布日志sql:"+ sql, null);
			query.executeBatch(sql, logParams);
			
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "任务发布异常:"+ e.getMessage(), e);
			return EasyResult.error(500, "操作异常:"+ e.getMessage());
		}
	}

	/**
	 * 任务派发
	 * @return
	 * @throws SQLException
	 */
	@WebControl(name = "taskPaifa", type = Types.RECORD)
	public EasyResult taskPaifa() {
		try {		
			String ids = param.getString("ids");
			String targetAccs = param.getString("userIds");
			String targetNames = param.getString("userNames");
			UserModel userModel = UserUtil.getUser(request);
			String userAcc = userModel.getUserAcc();
			String userName = userModel.getUserName();
			String cqTime = DateUtil.getCurrentDateStr();
			String newStatus = DictConstants.QC_RECORD_STATUS_WAIT_MQC;
			String oldStatus1 = DictConstants.QC_RECORD_STATUS_WAIT_ASSIGN;
			String oldStatus2 = DictConstants.QC_RECORD_STATUS_WAIT_RECEIVE;
			
			String[] idArray = ids.split(",");
			String[] targetAccArray = targetAccs.split(",");
			String[] targetNameArray = targetNames.split(",");

			EasyQuery query = this.getQuery();
			//任务派发
			String sql1 = "update C_PF_QC_RECORD t1 set t1.STATUS = ?, t1.MANUAL_QC_ASSIGN_TIME = ?, t1.MANUAL_QC_TAKE_TIME = ?, t1.QC_ACC = ?, t1.QC_NAME = ? where id = ? and t1.STATUS in (?, ?)";
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务派发["+oldStatus1 +","+ oldStatus2 +"->"+newStatus +"]sql:"+ sql1 +", ids:" +ids, null);
			//插入派发日志
			String sql2 = "update C_PF_QC_ASSIGN set ASSIGN_ACC = ?, ASSIGN_NAME = ?, ASSIGN_TIME = ?, ASSIGN_TYPE = ?, "
					+ "RECE_ACC = ?, RECE_NAME = ?, RECE_TIME = ? where ID = ?";
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务派发日志sql:"+ sql2, null);

			int successNum = 0;
			int loopTemp = 0;
			for (String id : idArray) {
				if (loopTemp >= targetAccArray.length) {
					loopTemp = 0;
				}
				String targetAcc = targetAccArray[loopTemp];
				String targetName = targetNameArray[loopTemp];
				
				int updateNum = query.executeUpdate(sql1, newStatus, cqTime, cqTime, targetAcc, targetName, id, oldStatus1, oldStatus2);
				successNum += updateNum;
				query.executeUpdate(sql2, userAcc, userName, cqTime, "02", targetAcc, targetName, cqTime, id);
				loopTemp++;
			}

			return EasyResult.ok(null, "总共选择["+idArray.length+"]条任务记录,成功派发了["+successNum+"]条任务记录");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "任务派发异常:"+ e.getMessage(), e);
			return EasyResult.error(500, "操作异常:"+ e.getMessage());
		}
	}

	/**
	 * 任务领取
	 * @return
	 * @throws SQLException
	 */
	private int taskReceive(String... ids) throws SQLException {
		EasyQuery query = this.getQuery();
		try {		
			query.begin();
			UserModel userModel = UserUtil.getUser(request);
			String userAcc = userModel.getUserAcc();
			String userName = userModel.getUserName();
			String userDeptCode = userModel.getDept().getDeptCode();
			String cqTime = DateUtil.getCurrentDateStr();
			String newStatus = DictConstants.QC_RECORD_STATUS_WAIT_MQC;
			String oldStatus = DictConstants.QC_RECORD_STATUS_WAIT_RECEIVE;

			//任务领取
			String sql1 = "update C_PF_QC_RECORD t1 set t1.STATUS = ?, MANUAL_QC_TAKE_TIME = ?, t1.QC_ACC = ?, t1.QC_NAME = ?, t1.QC_DEPT = ?  where id = ? and t1.STATUS = ?";
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务领取["+oldStatus +"->"+newStatus +"]sql:"+ sql1 +", ids:" +ids.toString(), null);
			//插入领取日志
			String sql2 = "update C_PF_QC_ASSIGN set RECE_ACC = ?, RECE_NAME = ?, RECE_TIME = ? where ID = ?";
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务领取日志sql:"+ sql2, null);
			
			
			int successNum = 0;
			for (String id : ids) {
				int updateNum = query.executeUpdate(sql1, newStatus, cqTime, userAcc, userName, userDeptCode, id, oldStatus);
				successNum += updateNum;
				if (updateNum > 0) {
					query.executeUpdate(sql2, userAcc, userName, cqTime, id);
				}
			}
			query.commit();
			
			return successNum;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ "任务领取异常:"+ e.getMessage(), e);
			query.roolback();
			return 0;
		}
	}
	
	//随机获取任务ID
	private String[] findRandomId(int receiveNum,String opAreaCode,String agentDept,String agentAcc,String agentName,String entryStart,String entryEnd) throws Exception {
		if (receiveNum > Constants.RANDOM_RECEIVE_NUM) {
			receiveNum = Constants.RANDOM_RECEIVE_NUM;
		}
		EasySQL randomSql = getEasySQL("");
		EasyQuery query = this.getQuery();

		String userAcc = UserUtil.getUser(request).getUserAcc();
		/*String checkSql = "select count(1) from C_PF_QC_RECORD where STATUS = ? and SESSION_TYPE = ? and QC_ACC = ? ";*/
		EasySQL checkSql = getEasySQL("select count(1) from C_PF_QC_RECORD d1 ");
		checkSql.append("left join V_PF_SESSION_RECORD d2 on d1.ID=d2.QC_RECORD_ID where 1=1");
		checkSql.append(DictConstants.QC_RECORD_STATUS_WAIT_MQC, " and d1.STATUS = ? ");
		checkSql.append(DictConstants.SESSION_TYPE_MEDIA, " and d1.SESSION_TYPE = ?");
		checkSql.append(userAcc, " and d1.QC_ACC = ? ");
		checkSql.append(opAreaCode, " and d2.OP_AREA_CODE = ? ");
		checkSql.append(agentDept, " and d2.AGENT_DEPT = ? ");
		checkSql.appendRLike(agentAcc, " and d2.AGENT_ACC like ? ");
		checkSql.appendRLike(agentName, " and d2.AGENT_NAME like ? ");
		checkSql.append(entryStart, " and d2.ANSWER_TIME >= ? ");
		checkSql.append(entryEnd, " and d2.ANSWER_TIME <= ? ");
		
		int checkNum = query.queryForInt(checkSql.getSQL(), checkSql.getParams());
		if (checkNum > Constants.WAIT_MAKE_ZJ_NUM) {
			throw new Exception("待完成质检记录过多，请先处理未完成的质检记录再领取");
		}
		
		/*randomSql = "select count(1) from C_PF_QC_RECORD where STATUS = ? and SESSION_TYPE = ? ";*/
		randomSql.append("select count(1) from C_PF_QC_RECORD d1 ");
		randomSql.append("left join V_PF_SESSION_RECORD d2 on d1.ID=d2.QC_RECORD_ID  where 1=1 ");
		randomSql.append(DictConstants.QC_RECORD_STATUS_WAIT_RECEIVE, "and d1.STATUS = ? ");
		randomSql.append(DictConstants.SESSION_TYPE_MEDIA, "and d1.SESSION_TYPE = ? ");
		randomSql.append(opAreaCode, " and d2.OP_AREA_CODE = ? ");
		randomSql.append(agentDept, " and d2.AGENT_DEPT = ? ");
		randomSql.appendRLike(agentAcc, " and d2.AGENT_ACC like ? ");
		randomSql.appendRLike(agentName, " and d2.AGENT_NAME like ? ");
		randomSql.append(entryStart, " and d2.ANSWER_TIME >= ? ");
		randomSql.append(entryEnd, " and d2.ANSWER_TIME <= ? ");
		int count = query.queryForInt(randomSql.getSQL(), randomSql.getParams());
		if (count == 0) {
			throw new Exception("没有可领取的数据");
		}
		int randomNum = 0;
		if (receiveNum<count) {
			int targetNum = count - receiveNum;
			/**
			 * 主要用于处理random的缺点:
			 * 根据工号生成随机码，保证每个账号的随机码不一样，生成的随机数也会在同一个时间段内有区别，否则很大几率在同个时间段内取的随机数是相同的
			 */
			byte[] buffer = userAcc.getBytes();
			long  values = 0;
			for (int i = 0; i < buffer.length; i++) {    
		        values <<= i; values|= (buffer[i] & 0xff);   
		    }
			randomNum = new Random(values).nextInt(targetNum);
		} else {
			receiveNum = count;
		}
		info(CommonUtil.getClassNameAndMethod(this)+ "随机抽取["+randomNum+"~"+(randomNum+receiveNum)+ "]的数据", null);
		randomNum = randomNum/receiveNum + 1;
		/*randomSql = "select ID from C_PF_QC_RECORD where STATUS = ? and SESSION_TYPE = ? ";*/
		randomSql = getEasySQL("");
		randomSql.append("select d1.ID from C_PF_QC_RECORD d1 ");
		randomSql.append("left join V_PF_SESSION_RECORD d2 on d1.ID=d2.QC_RECORD_ID where 1=1 ");
		randomSql.append(DictConstants.QC_RECORD_STATUS_WAIT_RECEIVE, "and d1.STATUS = ? ");
		randomSql.append(DictConstants.SESSION_TYPE_MEDIA, "and d1.SESSION_TYPE = ? ");
		randomSql.append(opAreaCode, " and d2.OP_AREA_CODE = ? ");
		randomSql.append(agentDept, " and d2.AGENT_DEPT = ? ");
		randomSql.appendRLike(agentAcc, " and d2.AGENT_ACC like ? ");
		randomSql.appendRLike(agentName, " and d2.AGENT_NAME like ? ");
		randomSql.append(entryStart, " and d2.ANSWER_TIME >= ? ");
		randomSql.append(entryEnd, " and d2.ANSWER_TIME <= ? ");
		List<EasyRow> rows = query.queryForList(randomSql.getSQL(), randomSql.getParams(), randomNum, receiveNum);
		String[] ids = new String[rows.size()];
		for (int i = 0; i<rows.size(); i++) {
			EasyRow row = rows.get(i);
			ids[i] = row.getColumnValue("ID");
		}
		return ids;
	}
	
	/**
	 * 随机领取任务
	 * @return
	 */
	@WebControl(name = "randomReceive", type = Types.RECORD)
	public EasyResult randomReceive() {
		try {
			int receiveNum = param.getIntValue("receiveNum");
			String opAreaCode=param.getString("opAreaCode");//区域
			String agentDept=param.getString("agentDept");//班组
			String agentAcc=param.getString("agentAcc");//坐席账号
			String agentName=param.getString("agentName");//坐席姓名
			String entryStart=param.getString("entryStart");//开始时间
			String entryEnd=param.getString("entryEnd");//结束时间
			String[] ids = this.findRandomId(receiveNum,opAreaCode,agentDept,agentAcc,agentName,entryStart,entryEnd);
			int successNum = this.taskReceive(ids);

			if (successNum < receiveNum) {
				return EasyResult.ok(null, "总共选择["+receiveNum+"]条任务记录进行随机领取,只成功领取了["+successNum+"]条任务记录,可能同一时刻多人同时领取或没有更多的任务记录");
			}
			return EasyResult.ok(null, "总共选择["+receiveNum+"]条任务记录进行随机领取,成功领取了["+successNum+"]条任务记录");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ " 随机领取任务异常:"+ e.getMessage(), e);
			return EasyResult.error(500, "操作异常:"+ e.getMessage());
		}
	}

	@WebControl(name = "taskRecovery", type = Types.RECORD)
	public EasyResult taskRecovery() {
		try {
			String ids = param.getString("ids");
			UserModel userModel = UserUtil.getUser(request);
			String userAcc = userModel.getUserAcc();
			String cqTime = DateUtil.getCurrentDateStr();
			String newStatus = DictConstants.QC_RECORD_STATUS_WAIT_ASSIGN;
			String oldStatus1 = DictConstants.QC_RECORD_STATUS_WAIT_RECEIVE;
			String oldStatus2 = DictConstants.QC_RECORD_STATUS_WAIT_MQC;

			String[] idArray = ids.split(",");
			//任务领取
			String sql1 = "update C_PF_QC_RECORD t1 set t1.STATUS = ?,t1.QC_NAME='',t1.QC_ACC='',t1.QC_NO=''  where id = ? and t1.STATUS in (?, ?)";
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务回收["+oldStatus1+","+oldStatus2 +"->"+newStatus +"]sql:"+ sql1 +", ids:" +ids, null);
			//插入领取日志
			String sql2 = "update C_PF_QC_ASSIGN set RECOVERY_ACC = ?, RECOVERY_TYPE = ?, RECOVERY_TIME = ? where ID = ?";
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "任务回收日志sql:"+ sql2, null);
			
			EasyQuery query = this.getQuery();
			
			int successNum = 0;
			for (String id : idArray) {
				int updateNum = query.executeUpdate(sql1, newStatus, id, oldStatus1, oldStatus2);
				successNum += updateNum;
				query.executeUpdate(sql2, userAcc, "01", cqTime, id);
			}
			
			return EasyResult.ok(null, "总共选择["+idArray.length+"]条任务记录,成功回收了["+successNum+"]条任务记录");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+ " 任务回收异常:"+ e.getMessage(), e);
			return EasyResult.error(500, "操作异常:"+ e.getMessage());
		}
	}
	
	@WebControl(name = "findDirByUserType", type = Types.LIST)
	public JSONObject findDirByUserType() {
		Object methodParam1 = getMethodParam(0);
		Object methodParam2 = getMethodParam(1);
		if (StringUtils.isNotBlank(methodParam1.toString()) && StringUtils.isNotBlank(methodParam1.toString())) {
			String userType = (methodParam1.toString()).trim();
			String[] status = (methodParam2.toString()).trim().split(",");
			EasySQL sql = this.getEasySQL("select t2.ID, t2.NAME ");
			sql.append("from C_PF_CALL_KEYWORD t1 ");
			sql.append("join C_CF_KEYWORD_DIR t2 on t1.KEYWORD_DIR_ID = t2.ID ");
			sql.append("join C_PF_QC_RECORD t3 on t3.SESSION_RECORD_ID = t1.CALL_ID ");
			sql.append("where 1=1 ");
			sql.append(" and t2.BUSI_TYPE = '01' "
					+ "and t2.MODULE = 'PerformExamWeb_online'");
			sql.append("and t3.STATUS in ('-1' ");
			for (String s:status) {
				sql.append(s, ", ?");
			}
			sql.append(") ");
			sql.append(userType, "and t1.USER_TYPE = ? ");
			sql.append("group by t2.ID, t2.NAME");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "查询状态为["+status+"]的任务记录标签目录sql：" + sql.getSQL(), null);
			return this.queryForList(sql.getSQL(), sql.getParams(), null);
		} else {
			return new JSONObject();
		}
	}

	/**
	 * 通过质检记录id获取全媒体通话明细 / 录音库聊天记录查询
	 * @return
	 */
	@WebControl(name = "findCallRecordDetal", type = Types.LIST)
	public JSONObject findCallRecordDetal() {
		String chatType=param.getString("chatType");
		String refserialId="";
		String qcId = param.getString("qcId");
		String sessionId = param.getString("sessionId");
		if(StringUtils.isNotBlank(chatType)){
			refserialId=connection(sessionId, qcId);
		} 
		JSONObject data = new JSONObject();
		EasySQL sql = this.getEasySQL("");
		if (StringUtils.isNotBlank(sessionId)) {
			sql.append("select t2.CHAT_ID as RECORD_DETAIL_ID, t2.MSG_TIME as BEGIN_TIME, t2.MSG_CONTENT CONTENT,t6.QUOTE_DATA, t2.ROBOT_DATA, t2.TEMP_CONFIG, t2.WITHDRAW,");
			sql.append("(CASE t2.SENDER WHEN 1 THEN '02' WHEN 2 THEN '01' WHEN 3 THEN '03' WHEN 4 THEN '04' END) as USER_TYPE, ");
			sql.append("t2.AGENT_ID AGENT_ACC,t5.channel_id, ");
			//20221018 暂时新增"emotion"企微表情类型 视为图片类型、新增voice，类型暂时用07
			sql.append("(CASE t2.MSG_TYPE WHEN 'text' THEN '01'  WHEN 'image' THEN '02'  WHEN 'video' THEN '06'  WHEN 'emotion' THEN '02' WHEN 'voice' THEN '07' ELSE t2.MSG_TYPE END ) TYPE,");
			sql.append("t5.AGENT_NAME, t5.CUSTOMER_NAME, ");
			sql.append("t3.KEYWORD,  t4.NAME, t3.KEYWORD_DIR_ID, t5.HANGUP_TYPE ");
			sql.append("from ycbusi.CC_MEDIA_CHAT_RECORD t2 ");
			sql.append("left join ycbusi.CC_MEDIA_CHAT_RECORD_EXT t6 on t2.CHAT_ID = t6.CHAT_ID");
			sql.append("left join C_PF_CALL_KEYWORD t3 on t3.CALL_DETAIL_ID = t2.CHAT_ID and t3.call_id=t2.CHAT_SESSION_ID");
			sql.append("left join C_CF_KEYWORD_DIR t4 on t4.ID = t3.KEYWORD_DIR_ID ");
			sql.append("left join V_PF_SESSION_RECORD t5 on t5.ID = t2.CHAT_SESSION_ID ");
			if(!"".equals(refserialId)){
				sql.append(sessionId, "where (t2.CHAT_SESSION_ID = ? ");
				sql.append(refserialId, " or t2.CHAT_SESSION_ID = ? )");
			}else{
				sql.append(sessionId, "where t2.CHAT_SESSION_ID = ? ");
			}
			sql.append("order by t2.CUST_SESSION_ID,t2.MSG_TIMESTAMP");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "查询语音通话明细[语音id:"+ sessionId +"]sql:" + sql.getSQL(), null);
			data = this.queryForList(sql.getSQL(), sql.getParams(), null);
		}else if (StringUtils.isNotBlank(qcId)) {
			sql.append("select t2.CHAT_ID as RECORD_DETAIL_ID, t2.MSG_TIME as BEGIN_TIME, t2.MSG_CONTENT CONTENT,t6.QUOTE_DATA, t2.ROBOT_DATA, t2.TEMP_CONFIG, t2.WITHDRAW,");
			sql.append("(CASE t2.SENDER WHEN 1 THEN '02' WHEN 2 THEN '01' WHEN 3 THEN '03' WHEN 4 THEN '04' END) as USER_TYPE, ");
			sql.append("t2.AGENT_ID AGENT_ACC,t5.channel_id, ");
			//20221018 新增"emotion"企微表情类型 视为图片类型(同步企微聊天记录)、新增voice，类型暂时用07
			sql.append("(CASE t2.MSG_TYPE WHEN 'text' THEN '01'  WHEN 'image' THEN '02'  WHEN 'video' THEN '06'  WHEN 'emotion' THEN '02' WHEN 'voice' THEN '07' ELSE t2.MSG_TYPE END) TYPE,");
			sql.append("t5.AGENT_NAME, t5.CUSTOMER_NAME, ");
			sql.append("t3.KEYWORD,  t4.NAME, t3.KEYWORD_DIR_ID, t5.HANGUP_TYPE ");
			sql.append("from ycbusi.CC_MEDIA_CHAT_RECORD t2 ");
			sql.append("left join ycbusi.CC_MEDIA_CHAT_RECORD_EXT t6 on t2.CHAT_ID = t6.CHAT_ID");
			sql.append("left join C_PF_CALL_KEYWORD t3 on t3.CALL_DETAIL_ID = t2.CHAT_ID and t3.call_id=t2.CHAT_SESSION_ID");
			sql.append("left join C_CF_KEYWORD_DIR t4 on t4.ID = t3.KEYWORD_DIR_ID ");
			sql.append("left join V_PF_SESSION_RECORD t5 on t5.ID = t2.CHAT_SESSION_ID ");

			if(!"".equals(refserialId)){
				sql.append(qcId, "where (t5.QC_RECORD_ID = ? ");//通过质检id查询 20220613
				sql.append(refserialId, " or t2.CHAT_SESSION_ID = ? )");
			}else{
				sql.append(qcId, "where t5.QC_RECORD_ID = ? ");//通过质检id查询 20220613
			}
			sql.append("order by t2.CUST_SESSION_ID,t2.MSG_TIMESTAMP");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "查询语音通话明细[质检id:"+ qcId +"]sql:" + sql.getSQL(), null);
			data = this.queryForList(sql.getSQL(), sql.getParams(), null);
		}
		//判断聊天内容是否来自企微渠道，如果来自企微需要对附件链接的消息处理
		if(data!=null&&data.getJSONArray("data")!=null&&data.getJSONArray("data").size()>0) {
			JSONArray dataArr = data.getJSONArray("data");
			setMsgData(dataArr);
			JSONObject obj = dataArr.getJSONObject(0);
			if("wecom_sidebar".equals(obj.getString("CHANNEL_ID"))) {
				data.put("data", appendWeComOSSFileCode(dataArr));
			}
		}
		return data;
	}
	
	private String connection(String sessionId,String qcId){
		String refSerialId="";
		try {
			if(!"".equals(sessionId)&&sessionId!=null){
				String sql="select REF_SERIAL_ID from  V_PF_SESSION_RECORD where ID = '"+sessionId+"'";
				List<EasyRow> list=this.getQuery().queryForList(sql,new Object[]{});
				if(list == null || list.size() == 0){
					return "";
				}
				refSerialId=list.get(0).getColumnValue("REF_SERIAL_ID");
				System.out.println(refSerialId);
			}else if(!"".equals(qcId)&&qcId!=null){
				String sql="select REF_SERIAL_ID from  V_PF_SESSION_RECORD t1 left join C_PF_QC_RECORD t2 on t2.SESSION_RECORD_ID=t1.ID where t2.ID = '"+qcId+"'";
				List<EasyRow> list=this.getQuery().queryForList(sql,new Object[]{});
				if(list == null || list.size() == 0){
					return "";
				}
				refSerialId=list.get(0).getColumnValue("REF_SERIAL_ID");
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return refSerialId;
		/*EasySQL sql=this.getEasySQL("select REF_SERIAL_ID from  V_PF_SESSION_RECORD t1 left join C_PF_QC_RECORD t2 on t2.SESSION_RECORD_ID=t1.ID where 1=1 ");
		sql.append(sessionId," and t1.ID = ?");
		sql.append(qcId," and t2.ID = ? ");
		JSONObject obj=queryForList(sql.getSQL(),sql.getParams());*/
	}
	
	
	@WebControl(name = "findCallInfoByQcId", type = Types.LIST)
	public JSONObject findCallInfoByQcId() {
		if (StringUtils.isNotBlank(param.getString("qcId"))) {
			String date = DateUtil.getCurrentDateStr("yyyy-MM");
			
			EasySQL sql = this.getEasySQL("select distinct t1.MANUAL_QC_KPI_ID,t1.QC_SCORE_ID,t1.QC_ACC, t1.QC_NAME,t2.SATISF_NAME, t1.EDIT_QC_ACC, t1.EDIT_QC_NAME, t2.AGENT_NAME, t2.CUSTOMER_NAME, t2.DIRECTION, t1.AUTO_QC_SCORE, t3to.MEDIA_LISTEN_RATE, t8.ORDER_TYPE_FIRST CONTACT_ORDER_SERV_TYPE_NAME, ");
			sql.append("wc.WX_AGENT_USERID,wc.WX_EXT_USERID");
			sql.append(",t11.KEY_TYPE_DICT,t11.STANDARD_TYPE,t2.CHANNEL_ID");
			sql.append("from C_PF_QC_RECORD t1 ");
			sql.append("join V_PF_SESSION_RECORD t2 on t1.SESSION_RECORD_ID = t2.ID ");
			/*sql.append(date, "left join C_PF_ST_AGENT_MONTH t3 on t1.AGENT_ACC = t3.CREATE_ACC and t3.STAT_MONTH=? ");*/
			sql.append(date, "left join C_ST_AGENT t3 on t1.AGENT_ACC = t3.USER_ACC and t3.ST_TYPE='02' and t3.ST_DATE=? ");
			sql.append("left join C_ST_AGENT_LISTEN_RATE t3to on t3.id=t3to.ST_AGENT_ID ");
			//sql.append("left join C_NO_CONTACT t7 on t2.ID = t7.SESSION_ID ");
			//sql.append("left join C_NO_APPEALLIST t8 on t7.ID = t8.CONTACT_SERAIL_ID ");
			sql.append("left join C_OL_CONSULT_ORDER t8 ON t8.SESSION_ID = t2.ID ");
			sql.append("left join C_NO_WECOM_LOGIN_LOG wc on wc.agent_acc=t2.agent_acc and wc.WX_EXT_USERID=t2.CUSTOMER_ACC");
			sql.append("left join C_PF_QC_KPI t11 on t11.ID = t1.MANUAL_QC_KPI_ID");
			sql.append(param.get("qcId"), "where t1.ID = ? ");
			JSONObject obj = this.queryForList(sql.getSQL(), sql.getParams(), null);

			List<JSONObject> objects = obj.getJSONArray("data").toJavaList(JSONObject.class);
			String manualQcKpiId = param.getString("manualQcKpiId");
			if (StringUtils.isNotBlank(manualQcKpiId)){
				EasyRow row = this.getKpiRow(this.getQuery(),manualQcKpiId);
				String standardType = row.getColumnValue("STANDARD_TYPE");
				String keyTypeDict = row.getColumnValue("KEY_TYPE_DICT");

				for (JSONObject object : objects){
					object.put("KEY_TYPE_DICT", keyTypeDict);
					object.put("STANDARD_TYPE", standardType);
				}
				obj.put("data", objects);
			}
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "通过质检记录id获取呼叫信息[质检id:"+param.getString("qcId")+"]sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			return obj;
		} else {
			return new JSONObject();
		}
	}


	@WebControl(name = "findAutoQCByQcIdNew", type = Types.LIST)
	public JSONObject findAutoQCByQcIdNew() {
		String autoQcScoreId = param.getString("autoQcScoreId");
		if (StringUtils.isNotBlank(autoQcScoreId)) {
			EasySQL sql = this.getEasySQL("select t1.SYS_EVALUATION, t2.KPI_CATEGORY_NAME AS CATEGORY_NAME, "
					+ "t2.KPI_ITEM_NAME as ITEM_NAME, t2.BAKUP, t2.SCORE ");
			sql.append("from C_NO_AUTO_QC_SCORE_DETAIL t1 ");
			sql.append(autoQcScoreId, "where t1.ID = ? ");
			sql.append("order by SCORE desc ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "通过自动质检评分id获取自动质检评分信息[评分id:"+autoQcScoreId+"]sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			JSONObject queryForList = this.queryForList(sql.getSQL(), sql.getParams(), null);
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "通过自动质检评分id获取自动质检评分信息[评分id:"+autoQcScoreId+"]结束");
			return queryForList;
		} else if (StringUtils.isNotBlank(param.getString("qcId"))) {
			EasySQL sql = this.getEasySQL("select KPI_CATEGORY_NAME as CATEGORY_NAME, "
					+ "KPI_ITEM_NAME as ITEM_NAME, BAKUP, SCORE ");
			sql.append("from C_NO_AUTO_QC_SCORE_DETAIL");
			sql.append(param.get("qcId"), "where QC_SCORE_ID = ? ");
			sql.append("order by SCORE desc ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "通过质检记录id获取自动质检评分信息[质检id:"+param.getString("qcId")+"]sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			JSONObject queryForList = this.queryForList(sql.getSQL(), sql.getParams(), null);
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "通过自动质检评分id获取自动质检评分信息[评分id:"+autoQcScoreId+"]结束");
			return queryForList;
		} else {
			return new JSONObject();
		}
	}
	
	@WebControl(name = "findAutoQCByQcId", type = Types.LIST)
	public JSONObject findAutoQCByQcId() {
		String autoQcScoreId = param.getString("autoQcScoreId");
		if (StringUtils.isNotBlank(autoQcScoreId)) {
			EasySQL sql = this.getEasySQL("select t1.SYS_EVALUATION, t3.NAME as CATEGORY_NAME, t3.SCORE as CATEGORY_SCORE, "
					+ "t4.NAME as ITEM_NAME, t4.SCORE as ITEM_SCORE, t2.BAKUP, t2.SCORE ");
			sql.append("from C_PF_AUTOQC_SCORE t1 ");
			sql.append("left join C_PF_AUTOQC_SCORE_DETAIL t2 on t1.ID = t2.QC_SCORE_ID ");
			sql.append("left join C_PF_QC_KPI_CATEGORY t3 on t3.ID = t2.KPI_CATEGORY_ID ");
			sql.append("left join C_PF_QC_KPI_ITEM t4 on t4.ID = t2.KPI_ITEM_ID ");
			sql.append(autoQcScoreId, "where t1.ID = ? ");
			sql.append("order by t3.SORT_NUM,t4.NAME ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "通过自动质检评分id获取自动质检评分信息[评分id:"+autoQcScoreId+"]sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			return this.queryForList(sql.getSQL(), sql.getParams(), null);
		} else if (StringUtils.isNotBlank(param.getString("qcId"))) {
			EasySQL sql = this.getEasySQL("select t1.SYS_EVALUATION, t3.NAME as CATEGORY_NAME, t3.SCORE as CATEGORY_SCORE, "
					+ "t4.NAME as ITEM_NAME, t4.SCORE as ITEM_SCORE, t2.BAKUP, t2.SCORE ");
			sql.append("from C_PF_QC_RECORD qc ");
			sql.append("join C_PF_QC_KPI t5 on t5.ID = qc.AUTO_QC_KPI_ID ");
			sql.append("left join C_PF_QC_KPI_CATEGORY t3 on t3.KPI_ID = t5.ID ");
			sql.append("left join C_PF_QC_KPI_ITEM t4 on t4.CATEGORY_ID = t3.ID ");
			sql.append("left join C_PF_AUTOQC_SCORE t1 on qc.AUTO_QC_SCORE_ID = t1.ID and t1.QC_KPI_ID = t5.ID ");
			sql.append("left join C_PF_AUTOQC_SCORE_DETAIL t2 on t1.ID = t2.QC_SCORE_ID and t2.KPI_CATEGORY_ID = t3.ID and t2.KPI_ITEM_ID = t4.ID ");
			sql.append(param.get("qcId"), "where qc.ID = ? ");
			sql.append("order by t3.SORT_NUM,t4.NAME ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "通过质检记录id获取自动质检评分信息[质检id:"+param.getString("qcId")+"]sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			return this.queryForList(sql.getSQL(), sql.getParams(), null);
		} else {
			return new JSONObject();
		}
	}

	@WebControl(name = "findHandmadeQc", type = Types.LIST)
	public JSONObject findHandmadeQc() {
		String rgQcScoreId = param.getString("rgQcScoreId");
		if (StringUtils.isNotBlank(rgQcScoreId)) {//如果有人工质检评分记录id
			EasySQL sql = this.getEasySQL("select t1.EVALUATION, t3.NAME as CATEGORY_NAME, t3.SCORE as CATEGORY_SCORE, t3.ID as CATEGORY_ID, "
						+ "t4.NAME as ITEM_NAME, t4.SCORE as ITEM_SCORE, t4.ID as ITEM_ID, t2.BAKUP, t2.SCORE, t5.SCORE_OPTION_ID, t5.SCORE_OPTION, "
						+ "t1.PRODUCT_TYPE, t1.QC_ADVICE, t1.KEY_TYPE,T1.NEED_TUTORING, t1.ADD_POINTS, t1.ZFJL_ITEMS, t1.WT_CATAGORY, t1.WT_ITEM, t1.WT_IMPROVE, t1.WT_CONTENT, t1.PENDING_REASON, "
						+ "t3.TYPE, t1.NOT_SATIS_REASON, t1.CALL_CONTENT ");
			sql.append("from C_PF_QC_SCORE t1 ");
			sql.append("left join C_PF_QC_SCORE_DETAIL t2 on t1.ID = t2.QC_SCORE_ID ");
			sql.append("left join C_PF_QC_KPI_CATEGORY t3 on t3.ID = t2.KPI_CATEGORY_ID ");
			sql.append("left join C_PF_QC_KPI_ITEM t4 on t2.KPI_ITEM_ID = t4.ID ");
			sql.append("left join C_PF_QC_SCORE_OPTION t5 on t5.KPI_SCORE_ID = t2.ID ");
			sql.append(rgQcScoreId, "where t1.ID = ? ");
			sql.append("order by t3.SORT_NUM,t4.NAME ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取人工质检评分信息sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			return this.queryForList(sql.getSQL(), sql.getParams(), null);
		} else if (StringUtils.isNotBlank(param.getString("qcId"))) {//没有则通过质检记录id找到人工质检评分记录id
			EasySQL sql = this.getEasySQL("select t1.EVALUATION, t3.NAME as CATEGORY_NAME, t3.SCORE as CATEGORY_SCORE, t3.ID as CATEGORY_ID, "
					+ "t4.NAME as ITEM_NAME, t4.SCORE as ITEM_SCORE, t4.ID as ITEM_ID, t2.BAKUP, t2.SCORE, t5.SCORE_OPTION_ID, t5.SCORE_OPTION, "
						+ "t1.PRODUCT_TYPE, t1.QC_ADVICE, t1.KEY_TYPE, t1.ADD_POINTS, t1.ZFJL_ITEMS, t1.WT_CATAGORY, t1.WT_ITEM, t1.WT_IMPROVE, t1.WT_CONTENT, t1.PENDING_REASON, "
						+ "t3.TYPE, t1.NOT_SATIS_REASON, t1.CALL_CONTENT ");
			sql.append("from C_PF_QC_RECORD qc ");
			if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getString("manualQcKpiId"))){
				//如果有手动切换质检考评标准id，则根据这个id查询
				sql.append(param.getString("manualQcKpiId"),"join C_PF_QC_KPI t6 on t6.ID = ? ");
			}else {
				//否则根据质检记录的考评标准id查询
				sql.append("join C_PF_QC_KPI t6 on t6.ID = qc.MANUAL_QC_KPI_ID ");
			}
			sql.append("left join C_PF_QC_KPI_CATEGORY t3 on t3.KPI_ID = t6.ID ");
			sql.append("left join C_PF_QC_KPI_ITEM t4 on t3.ID = t4.CATEGORY_ID ");
			sql.append("left join C_PF_QC_SCORE t1 on qc.QC_SCORE_ID = t1.ID and t1.QC_KPI_ID = t6.ID ");
			sql.append("left join C_PF_QC_SCORE_DETAIL t2 on t1.ID = t2.QC_SCORE_ID ");
			sql.append("left join C_PF_QC_SCORE_OPTION t5 on t5.KPI_SCORE_ID = t2.ID ");
			sql.append(param.getString("qcId"), "where qc.ID = ? ");
			sql.append("order by t3.SORT_NUM,t4.NAME ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取人工质检评分信息sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			return this.queryForList(sql.getSQL(), sql.getParams(), null);
		} else {
			return new JSONObject();
		}		
	}
	
	@WebControl(name = "findItemScoreByItemId", type = Types.RECORD)
	public JSONObject findItemScoreByItemId() {
		if (StringUtils.isNotBlank(param.getString("itemId"))) {
			EasySQL sql = this.getEasySQL("select t1.SCORE, t1.SCORE_TEXT, t1.SELECT_OPTION, t1.SELECT_DEFAULT ");
			sql.append("from C_PF_QC_KPI_ITEM_SOCRE t1 ");
			sql.append(param.get("itemId"), "where t1.ITEM_ID = ? ");
			sql.append("01", "and t1.ENABLE_STATUS = ? ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取分值项信息sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			return this.queryForList(sql.getSQL(), sql.getParams(), null);
		} else {
			return new JSONObject();
		}
	}
	
	@WebControl(name = "findItemBakupByItemId", type = Types.RECORD)
	public JSONObject findItemBakupByItemId() {
		if (StringUtils.isNotBlank(param.getString("itemId"))) {
			EasySQL sql = this.getEasySQL("select t1.ID, t1.CONTENT ");
			sql.append("from C_PF_QC_KPI_ITEM_OPTION t1 ");
			sql.append(param.get("itemId"), "where t1.ITEM_ID = ? ");
			sql.append("01", "and t1.ENABLE_STATUS = ? ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取备注项信息sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			return this.queryForList(sql.getSQL(), sql.getParams(), null);
		} else {
			return new JSONObject();
		}
	}
	
	@WebControl(name = "findStaffCoachView", type = Types.RECORD)
	public JSONObject findStaffCoachView() {
		if (StringUtils.isNotBlank(param.getString("qcId"))) {
			EasySQL sql = this.getEasySQL("select t1.ID as QC_RECORD_ID, t2.ID as SESSION_RECORD_ID, t2.SESSION_TYPE,t2.CUSTOMER_NAME, t2.AGENT_NAME, t2.ANSWER_TIME, t1.AUTO_QC_SCORE, t1.QC_SCORE, t1.TUTORING_NUM, t8.ORDER_TYPE_FIRST CONTACT_ORDER_SERV_TYPE_NAME, t3.* ");
			sql.append("from C_PF_QC_RECORD t1 ");
			sql.append("left join V_PF_SESSION_RECORD t2 on t1.SESSION_RECORD_ID = t2.ID ");
			//sql.append("left join C_NO_CONTACT t7 on t2.ID = t7.SESSION_ID ");
			//sql.append("left join C_NO_APPEALLIST t8 on t7.ID = t8.CONTACT_SERAIL_ID ");
			sql.append("left join C_OL_CONSULT_ORDER t8 ON t8.SESSION_ID = t2.ID ");
			sql.append("left join C_PF_TUTORING_RECORD t3 on t1.ID = t3.QC_RECORD_ID ");
			sql.append(param.get("qcId"), "where t1.ID = ? ");
			sql.append("order by t3.CREATE_TIME desc ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取辅导弹窗信息sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			return this.queryForRecord(sql.getSQL(), sql.getParams(), null);
		} else {
			return new JSONObject();
		}
	}
	
	@WebControl(name = "findItemBakupByScoreId", type = Types.RECORD)
	public JSONObject findItemBakupByScoreId() {
		String scoreId = param.getString("scoreId");
		String itemId = param.getString("itemId");
		if (StringUtils.isNotBlank(scoreId) && StringUtils.isNotBlank(itemId)) {
			EasySQL sql = this.getEasySQL("select t1.ID, t1.CONTENT ");
			sql.append("from C_PF_QC_KPI_ITEM_OPTION t1 ");
			sql.append(param.get("itemId"), "where t1.ITEM_ID = ? ");
			sql.append(scoreId, "and t1.ITEM_SCORE_ID = ? ");
			sql.append(" order by CONTENT asc");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取备注项信息sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			return this.queryForList(sql.getSQL(), sql.getParams(), null);
		} else {
			return new JSONObject();
		}
	}

	@WebControl(name = "findStaffCoach", type = Types.RECORD)
	public JSONObject findStaffCoach() {
		if (StringUtils.isNotBlank(param.getString("qcId"))) {
			EasySQL sql = this.getEasySQL("select t1.ID as QC_RECORD_ID, t2.ID as SESSION_RECORD_ID, t2.SESSION_TYPE, t2.CUSTOMER_NAME, t2.AGENT_NAME, t2.ANSWER_TIME, t1.AUTO_QC_SCORE, t1.QC_SCORE, t1.TUTORING_NUM,t8.ORDER_TYPE_FIRST CONTACT_ORDER_SERV_TYPE_NAME ");
			sql.append("from C_PF_QC_RECORD t1 ");
			sql.append("left join V_PF_SESSION_RECORD t2 on t1.SESSION_RECORD_ID = t2.ID ");
			//sql.append("left join C_NO_CONTACT t7 on t2.ID = t7.SESSION_ID ");
			//sql.append("left join C_NO_APPEALLIST t8 on t7.ID = t8.CONTACT_SERAIL_ID ");
			sql.append("left join C_OL_CONSULT_ORDER t8 ON t8.SESSION_ID = t2.ID ");
			sql.append(param.get("qcId"), "where t1.ID = ? ");
			logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取辅导弹窗信息sql:" + sql.getSQL()+ ",params:"+ param.toJSONString(), null);
			return this.queryForRecord(sql.getSQL(), sql.getParams(), null);
		} else {
			return new JSONObject();
		}
	}
	
	@WebControl(name = "findOrderInfo", type = Types.OTHER)
	public JSONObject findOrderInfo() {
		String qcId = param.getString("qcId");
		if (StringUtils.isNotBlank(qcId)) {
			EasySQL sql = this.getEasySQL("select CONTACT_ORDER_ID,CONTACT_ORDER_CODE, ORG_CODE, CUSTOMER_MOBILEPHONE1, CONTACT_ORDER_STATUS from C_NO_CONTACT t1 where exists (");
			sql.append("select 1 from V_PF_SESSION_RECORD t2 join C_PF_QC_RECORD t3 on t2.ID = t3.SESSION_RECORD_ID where ");
			sql.append(qcId, "t3.ID = ? and t1.SESSION_ID = t2.ID ");
			sql.append(") ");
			try {
				logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取工单id的SQL:" + sql.getSQL()+", qcId:"+qcId);
				JSONObject orderInfo = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
				return EasyResult.ok(orderInfo);
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				logger.error(CommonUtil.getClassNameAndMethod(this)+ "获取工单id异常:" + e.getMessage(), e);
				return EasyResult.error();
			}
		} else {
			return EasyResult.error();
		}
	}
	@WebControl(name = "findZxOrderInfo", type = Types.OTHER)
	public JSONObject findZxOrderInfo() {
		String vid = param.getString("vid");
		if (StringUtils.isNotBlank(vid)) {
			EasySQL sql = new EasySQL();
			sql.append(vid, "select ID from C_OL_CONSULT_ORDER t1 where t1.SESSION_ID=?");
			try {
				logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取咨询工单id的SQL:" + sql.getSQL()+", vid:"+vid);
				JSONObject orderInfo = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
				return EasyResult.ok(orderInfo);
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				logger.error(CommonUtil.getClassNameAndMethod(this)+ "获取咨询工单id异常:" + e.getMessage(), e);
				return EasyResult.error();
			}
		} else {
			return EasyResult.error();
		}
	}
	
	@WebControl(name = "findPL", type = Types.RECORD)
	public JSONObject findPL() {
		String qcId = param.getString("qcId");
		if (StringUtils.isNotBlank(qcId)) {
			EasySQL sql = this.getEasySQL("select t1.ORG_CODE from C_OL_CONSULT_ORDER t1 ");
			sql.append("left join V_PF_SESSION_RECORD t2 on t2.ID = t1.SESSION_ID ");
			sql.append("left join C_PF_QC_RECORD t3 on t2.ID = t3.SESSION_RECORD_ID ");
			sql.append(qcId, "where t3.ID = ? ");
			try {
				logger.info(CommonUtil.getClassNameAndMethod(this)+ "获取工单产品主体的SQL:" + sql.getSQL()+", qcId:"+qcId);
				JSONObject orderInfo = this.getQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
				return EasyResult.ok(orderInfo);
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				logger.error(CommonUtil.getClassNameAndMethod(this)+ "获取工单产品主体异常:" + e.getMessage(), e);
				return EasyResult.error();
			}
		} else {
			return EasyResult.error();
		}
	}
	
	
	private JSONArray appendWeComOSSFileCode(JSONArray data) {
		String code = "";
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject();
		request.put("command", "getWeComOSSFileCode");
		JSONObject params = new JSONObject();
		request.put("params", params);
		try {
			IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
			result = service.invoke(request);
			logger.info("请求oss访问凭证返回结果：" + result);
			if(!result.isEmpty()) {
				code = result.getString("certification");
			}
			
		} catch (ServiceException e) {
			logger.error("请求oss访问凭证失败" + e.getMessage(), e);
		}
		for(int i=0;i<data.size();i++) {
			JSONObject obj = data.getJSONObject(i);
			String content = obj.getString("CONTENT");
			if(content.startsWith("http")&& content.contains("/auth/userDownload")) {
				content += "?certification=" + code;
				obj.put("CONTENT",content);
			}
		}
		return data;
	}

	/**
	 * 处理消息数据
	 * 1.大文本读取：MSG_CONTENT，ROBOT_DATA，TEMP_CONFIG，QUOTE_DATA
	 * 2.机器人消息格式化
	 * @param data
	 */
	private void setMsgData(JSONArray data) {
		String dbName = "ycbusi";
		for(int i=0;i<data.size();i++) {
			JSONObject obj = data.getJSONObject(i);
			String serialId = obj.getString("RECORD_DETAIL_ID");

			//读取缓存中的超长内容 begin
			String robotData = obj.getString("ROBOT_DATA");
			if(StringUtils.isNotBlank(robotData) && robotData.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
				String str1 = CacheLongTxtUtil.get(dbName, serialId, "ROBOT_DATA",robotData);
				if(StringUtils.isNotBlank(str1)){
					obj.put("ROBOT_DATA",str1);
				}
			}

			String msgContent = obj.getString("CONTENT");
			if(StringUtils.isNotBlank(msgContent) && msgContent.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
				String str2 = CacheLongTxtUtil.get(dbName, serialId, "MSG_CONTENT",msgContent);
				if(StringUtils.isNotBlank(str2)){
					obj.put("CONTENT",str2);
				}
			}

			String tempConfig = obj.getString("TEMP_CONFIG");
			if(StringUtils.isNotBlank(tempConfig) && tempConfig.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
				String str3 = CacheLongTxtUtil.get(dbName, serialId, "TEMP_CONFIG",tempConfig);
				if(StringUtils.isNotBlank(str3)){
					obj.put("TEMP_CONFIG",str3);
				}
			}
			String quoteData = obj.getString("QUOTE_DATA");
			if(StringUtils.isNotBlank(quoteData) && quoteData.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
				String str4 = CacheLongTxtUtil.get(dbName, serialId, "QUOTE_DATA",quoteData);
				if(StringUtils.isNotBlank(str4)){
					obj.put("QUOTE_DATA",str4);
				}
			}
			//读取缓存中的超长内容 end

			//处理其他类型消息
			parseMsg(obj);

		}
	}

	/**
	 * 处理全媒体在线客服消息
	 * @param obj
	 * @return
	 */
	private void parseMsg(JSONObject obj){
		String content = obj.getString("CONTENT");
		String robotDataStr = obj.getString("ROBOT_DATA");
		JSONObject robotData = null;
		if(StringUtils.isNotBlank(robotDataStr)){
			robotData = JSONObject.parseObject(robotDataStr);
		}
		if(robotData == null){
			return;
		}
		//移除"welcomeCardList"，"bottomNavList"
		robotData.remove("welcomeCardList");
		robotData.remove("bottomNavList");
		obj.put("ROBOT_DATA",robotData.toJSONString());
		if("{}".equals(content)){
			//是否引导问
			JSONObject answerList = robotData.getJSONObject("answerList");
			if(answerList != null && answerList.containsKey("beforeWord")&& answerList.containsKey("itemList")){
				JSONArray itemList = answerList.getJSONArray("itemList");
				if (itemList !=null&&itemList.size()>0) {
					String beforeWord = answerList.getString("beforeWord");
					String afterWord = (String) answerList.getOrDefault("afterWord","请点击上方问题获取答案，或重新详细描述您的问题");
					StringBuilder contentSbd = new StringBuilder(beforeWord + "\n");
					for (int i = 0; i <itemList.size(); i++) {
						JSONObject questionObj = itemList.getJSONObject(i);
						contentSbd.append(questionObj.getString("seq")).append(".").append((String) questionObj.getOrDefault("question",questionObj.getString("title"))).append("\n");
					}
					contentSbd.append(afterWord);
					obj.put("CONTENT",contentSbd.toString());
				}
			}
			return;
		}

		//css工单信息
		String msgType = obj.getString("TYPE");
		JSONObject cssData = robotData.getJSONObject("cssData");
		if("cssData".equals(msgType)&&cssData!=null){
			JSONArray orderList = cssData.getJSONArray("list");
			if(orderList!=null && !orderList.isEmpty()){
				StringBuilder contentSbd = new StringBuilder(cssData.getString("title") + "\n");
				for (int i = 0; i < orderList.size(); i++) {
					JSONObject order = orderList.getJSONObject(i);
					contentSbd.append("服务单号：").append(order.getString("serviceOrderNo"))
							.append("，服务类型：").append(order.getString("serviceTypeName"))
							.append("，创建时间：").append(order.getString("contactTime")).append("\n");
					JSONArray userDemandList = order.getJSONArray("userDemandList");
					if(userDemandList!=null && !userDemandList.isEmpty()){
						contentSbd.append("		产品：").append("\n");
						for (int j = 0; j < userDemandList.size(); j++) {
							JSONObject userDemand = userDemandList.getJSONObject(j);
							contentSbd.append(userDemand.getString("brandName")).append((String) userDemand.getOrDefault("subAttribute4",userDemand.getString("prodName"))).append("\n");
						}
					}
				}
				obj.put("CONTENT",contentSbd.toString());
			}
		}
	}
}