<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="PerformExamWeb" name="质检模块" package-by="DELL" package-time="2025-09-11 16:03:38" version="2.5.3#20250911-2">
    <datasources>
        <datasource description="yw数据源" isnull="true" name="yw-ds"/>
        <datasource description="mars数据源" isnull="true" name="mars-ds"/>
        <datasource description="YCBUSI数据源" isnull="true" name="ycbusi-ds"/>
        <datasource description="顺德genesys数据源" isnull="true" name="genesys-sd"/>
        <datasource description="合肥genesys数据源" isnull="true" name="genesys-hf"/>
        <datasource description="自动质检数据源" isnull="true" name="ROBOT_DS"/>
    </datasources>
    <description>
        2.5.3#20250814-1
            1.优化超长消息文本读取逻辑。
    </description>
</application>
