<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>回收</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="recycleForm" data-mars="" method="post" autocomplete="off" data-mars-prefix="recycle.">
		<table class="table  table-vzebra mt-10">
			<tbody>
				<tr>
					<td>坐席账号</td>
					<td colspan="3">
						<div class="input-group input-group-sm" style="width: 100%">
							<input class="hidden" type="text" name="recycle.USER_ACC"
								id="rUserAcc" class="form-control input-sm">
							<input name="recycle.USER_NAME" id="rUserName" class="form-control input-sm" readonly="readonly" onchange="recycle.getTaskNum()">
							<span class="input-group-addon" onclick="recycle.user()"><i class="glyphicon glyphicon-search"></i></span>
						</div>
					</td>
				</tr>
                 <tr>
                	 <td class="required">需求方</td>
                     <td>
		    			<input type="text" name="recycle.DEMAND_SIDE" value="${param.demandSide}" id="rDemandSide" data-rules="required" class="form-control input-sm" onchange="recycle.getTaskNum()"/>
                     </td>
                     <td class="required">任务名称</td>
                     <td>
                     	<input type="text" name="recycle.TASK_NAME" value="${param.taskName}" id="rTaskName" data-rules="required" class="form-control input-sm" onchange="recycle.getTaskNum()"/>
                     </td>
                 </tr>
				<tr>
					<td class="required">数量</td>
					<td><input type="text" name="recycle.TASK_NUM" data-rules="required" class="form-control input-sm">
					</td>
					<td class="required">折算类型</td>
					<td>
						<select class="form-control input-sm" name="recycle.CONVERSION_TYPE" data-mars="common.getConvert" onchange="recycle.getTaskNum()">
							<option value="">请选择</option>
						</select>
					</td>
				</tr>
				<tr>
					<td class="required">同步开始时间</td>
					<td><input type="text" name="recycle.SYN_TIME_START" id="rSynStart"
						class="form-control input-sm" onchange="recycle.getTaskNum()" data-rules="required"
						onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})"></td>
					<td class="required">同步结束时间</td>
					<td><input type="text" name="recycle.SYN_TIME_END" id="rSynEnd"
						class="form-control input-sm" onchange="recycle.getTaskNum()" data-rules="required"
						onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})"></td>
				</tr>
				<tr>
					<td>发布开始时间</td>
					<td><input type="text" name="recycle.PUB_TIME_START"
						class="form-control input-sm" onchange="recycle.getTaskNum()"
						onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})"></td>
					<td>发布结束时间</td>
					<td><input type="text" name="recycle.PUB_TIME_END"
						class="form-control input-sm" onchange="recycle.getTaskNum()"
						onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})"></td>
				</tr>
				<tr>
					<td>回访结果</td>
					<td>
						<select class="form-control input-sm" name="recycle.REVISIT_RESULT" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_REVISIT_RESULT')" onchange="recycle.getTaskNum()">
							<option value="">请选择</option>
						</select>
					</td>
					<td>回访状态</td>
					<td>
						<select class="form-control input-sm" name="recycle.REVISIT_STATUS" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_REVISIT_STATUS1')"  onchange="recycle.getTaskNum()">
							<option value="">全部</option>
						</select>
					</td>
				</tr>
				<!-- 统计数 -->
				<tr class="">
					<th colspan="4">
						<div>
							<div style="padding-right: 10px">
								任务数：<span style="color: red" id="rTaskNum"></span>
							</div>
						</div>
					</th>
				</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button"
				onclick="recycle.ajaxSubmitForm()">确认</button>
			<button class="btn btn-sm btn-default ml-20" type="button"
				onclick="layer.closeAll();">关闭</button>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript">
		jQuery.namespace("recycle");
		requreLib.setplugs("wdate");
		$(function(){
			var startDate = getTodayDate(-7);
   	    	var endDate = getTodayDate();
	    	$("#rSynStart").val(startDate + " 00:00:00");
	    	$("#rSynEnd").val(endDate + " 23:59:59");
	    	$("#recycleForm").custRender();
	    	$("#recycleForm").render({success:function(result){
	    		
	    	}});
	    })
	    recycle.ajaxSubmitForm = function(){
	    	if(form.validate("#recycleForm")){
	    		var data = form.getJSONObject("recycleForm");
	    		ajax.remoteCall("${ctxPath}/servlet/revisit?action=recycle",data,function(result) { 
					if(result.state == 1){
						layer.msg(result.msg,{icon:1,time:1200},function(){
							window.location.reload();
						});
					}else{
						layer.alert(result.msg,{icon:5});
					}
				})
	    	}
	    }
		recycle.getTaskNum = function(){
	    	if($("#rDemandSide").val()||$("#rTaskName").val()||$("rSynStart").val()||$("rSynEnd").val()){
	    		var data = form.getJSONObject("recycleForm");
				ajax.remoteCall("${ctxPath}/servlet/revisit?action=getRecycleNum",data,function(result) { 
					if(result.state == 1){
						$("#rTaskNum").html(result.data.NUM)
					}
				})
	    	}else{
	    		$("#rTaskNum").html("")
	    	}
	    }
		var olduser="";
		recycle.user = function() {
	 		var oldIds="";
	 		var oldName="";
	 		if($("#rUserAcc").val()!=""){
	 			oldIds=$("#rUserAcc").val();
	 			oldName=$("#rUserName").val();
	 			olduser=oldIds;
	 		}
 			popup.layerShow({
 				type : 2,
 				title : "用户选择",
 				offset : '20px',
 				area : [ '1000px', '650px' ]
 			}, "${ctxPath}/servlet/User?action=user", {enable:true,oldIds:'',oldName:''});
		}
		function setUser(acc,name){
			$("#rUserAcc").val(acc);
			$("#rUserName").val(name);
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp"%>