	<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企划通回访单查询</title>
	<link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">
		<style>
			#menuContent {
				display:none;position: absolute;border:1px solid rgb(170,170,170);max-width: 220px; 
				max-height: 350px;z-index:10;overflow: auto;background-color: #f4f4f4;top:31px
				}
			.pagination pagination-sm pageNumV{float:right}
		</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form  autocomplete="off" action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 企划通回访查询</h5>
	             		 </div>
	             		 <hr style="margin:5px -15px">
	             			<div class="form-group">

								<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">用户号码</span>	
									<input type="text" name="CUSTOMER_PHONE" class="form-control input-sm" >
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
								      <span class="input-group-addon" style="width: 80px;">回访状态</span>	
									  <select class="form-control input-sm" name="REVISIT_STATUS" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_REVISIT_STATUS')">
                                          <option value="">请选择</option>
                                      </select>
								</div>
								<div class="input-group input-group-sm">
									<span class="input-group-addon" style="width: 80px;">同步时间</span>	
									<input type="text" name="SYN_TIME_START" id="SynStart" class="form-control input-sm Wdate" onclick="WdatePicker({onpicked:function(){this.blur();maxTime1(this)},dateFmt:'yyyy-MM-dd HH:mm:ss'})">
									<span class="input-group-addon" >-</span>	
									<input type="text" name="SYN_TIME_END" id="SynEnd" class="form-control input-sm Wdate" onclick="WdatePicker({
									minDate:'#F{$dp.$D(\'SynStart\')}',
									dateFmt:'yyyy-MM-dd HH:mm:ss',
									onpicked:function(){this.blur();maxTime1(this)}  // 新增结束时间选择回调
									})">
								</div>   
								<div class="input-group input-group-sm">
				          		    <span class="input-group-addon" style="width: 80px;">发布时间</span>	
									<input type="text" name="PUB_TIME_START" class="form-control input-sm Wdate" value="" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
									<span class="input-group-addon" >-</span>	
									<input type="text" name="PUB_TIME_END" class="form-control input-sm Wdate" value=""   onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">									  
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">事业部</span>
									<input type="text" name="ORG_CODE" class="form-control input-sm" >
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">跟进状态</span>
									<select class="form-control input-sm" id="follow_status" name="FOLLOW_STATUS" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_FOLLOW_STATUS')">
										<option value="">请选择</option>
									</select>
								</div>
								<div class="hidden">
									<span class="input-group-addon" style="width: 80px;">人群包ID</span>
									<input type="text" name="TASK_ID" id="taskId" class="form-control input-sm" value="${param.taskId}" >
									<input type="text" name="TASK_NAME" id="taskName" class="form-control input-sm" value="${param.taskName}" >
									<input type="text" name="DEMAND_SIDE" id="demandSide" class="form-control input-sm" value="${param.demandSide}" >
								</div>
								<div class="input-group input-group-sm">
									<span class="input-group-addon" style="width: 80px;">任务包名称</span>
									<!-- <select class="form-control input-sm" data-mars="revisit.getPlanningTaskNameQuery">
										<option value="">请选择</option>
									</select> -->
									<div id="taskNameSelect" class="xm-select" style="width:420px"></div>
								</div>
								<div class="input-group input-group-sm" id="demandSideDiv" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">需求方</span>
									<!-- <select class="form-control input-sm" data-mars="revisit.getPlanningDemandSideQuery">
										<option value="">请选择</option>
									</select> -->
									<div id="demandSideSelect" class="xm-select " style="width:230px"></div>
								</div>
								<div class="hidden">
								      <span class="input-group-addon" style="width:80px">折算类型</span>	
									  <select name="CONVERSION_TYPE" data-rules="required" class="form-control input-sm" data-mars="common.getConvert">
			                                <option value="">请选择</option>
			                          </select>
							    </div>
						  </div>
						 <div class="form-group">
							  <div class="input-group input-group-sm pull-left mr-10">
								  <button type="button" id="neworder_visit_resetting" class="btn btn-sm btn-success" onclick="resetting()">重置</button>
						  	  </div>
					 		   <EasyTag:res resId="planning_publish">
			             			<div class="input-group input-group-sm pull-left mr-10">
										<button type="button" class="btn btn-sm btn-success" onclick="revisit.publish()">发布</button>
									</div>
							   </EasyTag:res>
								<EasyTag:res resId="planning_appoit">
							  		 <div class="input-group input-group-sm pull-left mr-10">
									  	<button type="button" class="btn btn-sm btn-success" onclick="revisit.distribute()">指派</button>
							  		 </div>
						  	    </EasyTag:res>
								<EasyTag:res resId="planning_recycle">
							      <div class="input-group input-group-sm pull-left mr-10">
									  <button type="button" class="btn btn-sm btn-success" onclick="revisit.recycle()">回收</button>
							 	  </div>
								</EasyTag:res>
							    <div class="input-group input-group-sm pull-right">
									  <button type="button" class="btn btn-sm btn-default" onclick="reload()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
	             		 </div>
             	    </div>  
             	  	<div class="ibox-content">
	              		<div class="row table-responsive">
		           	     	<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" >
                             	<thead>
	                         		 <tr>
		                         	      <th class="text-c">选择</th>
		                         	      <th class="text-c">回访状态</th>
									      <th class="text-c">需求方</th>
									      <th class="text-c">需求方岗位</th>
										  <th class="text-c">人群包ID</th>
									      <th class="text-c">任务包</th>
									      <th class="text-c">用户姓名</th>
									      <th class="text-c">用户号码</th>
									      <th class="text-c">查看</th>
									      <th class="text-c">事业部
											  <i class="layui-icon layui-icon-about tips" style="color: #1E9FFF;"
												 title="在天猫渠道，该字段数值为媒体数据。"></i>
										  </th>
										 <th class="text-c">跟进状态
											 <i class="layui-icon layui-icon-about tips" style="color: #1E9FFF;"
												title="待跟进：线索初始状态，无跟进行为。
跟进中：线索产生过打电话，回访处理提交等跟进行为。
跟进结束：修改线索阶段为“转商机”或“无效”后，跟进状态会被标记为“跟进结束”。"></i>
										 </th>
									      <th class="text-c">领取人</th>
									      <th class="text-c">折算类型</th>
									      <th class="text-c">发布人</th>
									      <th class="text-c">发布时间</th>
			   						 </tr>
                            	 </thead>
                             <tbody id="dataList">
                             </tbody>
                           </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td class="text-c"><label class="checkbox checkbox-info"><input type="checkbox" data-phone="{{:CUSTOMER_PHONE}}" data-id="{{:ID}}" data-status="{{:REVISIT_STATUS}}"><span></span></label></td>
                                      		<td>{{dictFUN:REVISIT_STATUS 'PLANNING_REVISIT_STATUS'}}</td>
                                     		<td>{{:DEMAND_SIDE}}</td>
                                      		<td>{{:DEMAND_SIDE_DEPT}}</td>
                                      		<td>{{:TASK_ID}}</td>
                                      		<td>{{:TASK_NAME}}</td>
                                      		<td>{{:CUSTOMER_NAME}}</td>
                                      		<td>{{call:CUSTOMER_PHONE CUSTOMER_PHONE_data #index+1 'phone' fn='getData2'}}</td>
                                      		<td style="width:50px;max-width:50px;min-width:50px" rowspan="{{:rowspan}} "title="查看">
												<span onclick="showDetail('{{:#index+1}}','{{:CUSTOMER_PHONE_data}}')" class="glyphicon glyphicon-eye-open" id="show{{:#index+1}}" style="color: rgb(255, 140, 60);"></span>
											</td>
                                     		<td>{{:ORG_CODE}}</td>
                                     		<td>{{dictFUN:FOLLOW_STATUS 'PLANNING_FOLLOW_STATUS'}}</td>
                                   		    <td>{{:RECEIVE_ACC}}</td>
											<td>{{CONVERSION_TYPE:CONVERSION_TYPE}}</td>
											<td>{{:PUB_ACC}}</td>
											<td>{{:PUB_TIME}}</td>
									    </tr>
								   {{/for}}					         
						 	</script>
	              		 </div> 
	              		 <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="10" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	                  </div>
              	</div>
        </form>
        
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/activeService/static/js/xm-select.js"></script>
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("revisit");
		requreLib.setplugs('wdate')//加载时间控件
		var conversion_type;
		revisit.newreload=function(val){
			$("#tableHead").data("mars","revisit.getRevisitList");
			$("#searchForm").searchData();
		}
		function reload(){
			$("#tableHead").data("mars","revisit.getRevisitList");
			$("#searchForm").searchData();
		}
		$(function(){
			if('${param.synStart}'&&'${param.synEnd}'){
				$("#SynStart").val("${param.synStart}");
				$("#SynEnd").val("${param.synEnd}");
			}else{
				var startDate = getTodayDate(-7);
				var endDate = getTodayDate();
				$("#SynStart").val(startDate+" 00:00:00");
				$("#SynEnd").val(endDate+" 23:59:59");
			}
	    	$("#searchForm").render({success : function(result){
	    		//折算类型
	    		if(result["common.getConvert"]){
    				conversion_type = result["common.getConvert"].data;
    			}
    		}});
			reload();
	    });
		//发布
		revisit.publish = function(){
			var start=$("#SynStart").val();
			var end=$("#SynEnd").val();
			var taskId=$("#taskId").val();
			var taskName=$("#taskName").val();
			var demandSide=$("#demandSide").val();
			// 获取选中记录的ID集合
			var planningId = $('#dataList input[type="checkbox"]:checked').map(function() {
				if($(this).data("status")!="1"){
					return;
				}
				return $(this).data('id');
			}).get().join(',');
			popup.layerShow({type:1,title:'发布',area:['700px','360px'],offset:'20px'},"${ctxPath}/pages/planning/revisit-publish.jsp",
					{taskId:taskId,taskName:taskName,demandSide:demandSide,synTimeStart:start,synTimeEnd:end,planningId:planningId});
		}
		//指派
		revisit.distribute = function(){
			var start=$("#SynStart").val();
			var end=$("#SynEnd").val();
			var taskId=$("#taskId").val();
			var taskName=$("#taskName").val();
			var demandSide=$("#demandSide").val();
			// 获取选中记录的ID集合
			var planningId = $('#dataList input[type="checkbox"]:checked').map(function() {
				if($(this).data("status")!="1"){
					return;
				}
				return $(this).data('id');
			}).get().join(',');
			popup.layerShow({type:1,title:'指派',area:['700px','360px'],offset:'20px'},"${ctxPath}/pages/planning/revisit-appoint.jsp",
					 {taskId:taskId,taskName:taskName,demandSide:demandSide,synTimeStart:start,synTimeEnd:end,planningId:planningId});
		}
		//回收
		revisit.recycle = function(){
			var taskId=$("#taskId").val();
			var taskName=$("#taskName").val();
			var demandSide=$("#demandSide").val();
			 popup.layerShow({type:1,title:'回收',area:['700px','440px'],offset:'20px'},"${ctxPath}/pages/planning/revisit-recycle.jsp",
					 {taskId:taskId,taskName:taskName,demandSide:demandSide});
		}
		$.views.converters("CONVERSION_TYPE", function(val) {
			var req = conversion_type;
			if (typeof (req) == "undefined") {
				return val;
			} else {
				return req[val];
			}
		});
		function resetting(){
			document.searchForm.reset();
			if('${param.synStart}'&&'${param.synEnd}'){
				$("#SynStart").val("${param.synStart}");
				$("#SynEnd").val("${param.synEnd}");
			}else{
				var startDate = getTodayDate(-7);
				var endDate = getTodayDate();
				$("#SynStart").val(startDate+" 00:00:00");
				$("#SynEnd").val(endDate+" 23:59:59");
			}
	    	$("#tableHead").data("mars","revisit.getRevisitList");
			$("#searchForm").searchData();
		}
		function cleanVal(data,id){//如果值为空则清空某个id的值
			if($(data).val()==""){
				$("#"+id).val('');
			}
		}
		function maxTime1(e){
			var start=$("#SynStart").val();
			var end=$("#SynEnd").val();
			if(start!=""&&end!=""&&start>end){
				layer.msg("开始时间应小于结束时间",{icon: 5});
				var time =end.substring(0,10)+start.substring(10);
				$("#SynStart").val(time);
			}

			// 新增一年时间范围校验
			if(start && end){
				var startDate = new Date(start.replace(/-/g,'/'));
				var endDate = new Date(end.replace(/-/g,'/'));
				var diffYear = endDate.getFullYear() - startDate.getFullYear();
				var diffMonth = endDate.getMonth() - startDate.getMonth();
				var diffDay = endDate.getDate() - startDate.getDate();

				if(diffYear > 1 || (diffYear === 1 && diffMonth >= 1) || (diffYear === 1 && diffMonth === 0 && diffDay > 0)){
					layer.msg("时间范围不能超过一年",{icon: 5});
					// 自动调整为一年后的日期
					if(e.id=='SynStart'){
						var minStart = new Date(endDate);
						minStart.setFullYear(minStart.getFullYear() - 1);
						$("#SynStart").val(minStart.format("yyyy-MM-dd") + " 00:00:00");
					}else{
						var maxEnd = new Date(startDate);
						maxEnd.setFullYear(maxEnd.getFullYear() + 1);
						$("#SynEnd").val(maxEnd.format("yyyy-MM-dd") + " 23:59:59");
					}

				}
			}
		}

		Date.prototype.format = function(fmt) {
			var o = {
				"M+": this.getMonth() + 1,
				"d+": this.getDate(),
				"H+": this.getHours(),
				"m+": this.getMinutes(),
				"s+": this.getSeconds()
			};
			if(/(y+)/.test(fmt)){
				fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));
			}
			for(var k in o){
				if(new RegExp("("+ k +")").test(fmt)){
					fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
				}
			}
			return fmt;
		}
		
		function  getData(val,valData,index){
			return "<span id='"+index+"-"+valData+"' data-val='"+val+"'>"+val+"</span>"
		}
		    
		var showId = {};
		revisit.showDetail = function(id,phone,name,address){
			if($("#show"+id).hasClass("glyphicon-eye-close")){
				showId[id]='';
				var phoneId= document.getElementById(id+"-"+phone)
				phoneId.innerHTML= phoneId.getAttribute("data-val");
				$("#show"+id).addClass("glyphicon-eye-open");
				$("#show"+id).removeClass("glyphicon-eye-close");
			}else{//显示
				var phoneId= document.getElementById(id+"-"+phone)
				decrypt({"value":phone},function(value){ phone=value});
				phoneId.innerHTML= phone;
				$("#show"+id).removeClass("glyphicon-eye-open");
				$("#show"+id).addClass("glyphicon-eye-close");
				showId[id]='show';
				auditLog(JSON.stringify({
					"model":"planning",
					"url":"/planning/pages/planning/revisit-handle-list.jsp",
					"action":"query",
					"describe":"用户查询[企划通回访处理列表]数据，查看客户["+name+"]敏感信息：[用户号码："+phone+"]"}));
			}
		}

		function auditLog(data){
			$.post('/iccportal5/servlet/auditLog', {action: 'Log', data: data}, function (result) {
				console.log(result);
			}, 'json');
		}
		function decrypt(data,returnVal){
			ajax.remoteCall("/iccportal5/servlet/auditLog?action=decrypt",data,function(result) {
	    		if(result.state == 1){
	    			returnVal(result.data)
				}
	    	},{async:false});
		}
		function showDetail(id,customerTel1){
			showDetailCommon({
				"model":"planning",
				"url":"/planning/pages/planning/revisit-list-sub.jsp",
				"action":"query",
				"describe":"用户查询[企划通回访处理列表]数据，査看敏感信息:[用户号码：{{customerTel1}}]"},id,null,null,customerTel1);
		}

		// 任务包名称选择器
		var taskNameList;
		var taskNameData = [];
		var demandSideList;
		var demandSideData = [];
		var firstTaskNameIn = true;
		var firstDemandSideIn = true;

		function initTaskName(){
			taskNameList = xmSelect.render({
				el: '#taskNameSelect',
				name: "taskId",
				filterable: true,
				paging: true,
				pageSize: 10,
				toolbar: { show: false },
				placeholder: '输入任务包名称',
				pageRemote: true,
				radio: true,
				searchTips: '输入任务包名称',
				on: function(data){
					var arr = data.arr;
					var change = data.change;
					var isAdd = data.isAdd;
					
					// 当任务包选择变化时，清空并重新加载需求方
					if(change.length > 0){
						if(isAdd){
							var taskId = change[0].taskId;
							$("#taskId").val(taskId);
							var name = change[0].name;
							if(name){
								//这里将taskId加上--替换的name里面的相同数据
								var taskName = name.replace(taskId+"--","");
								if(taskName.length>1){
									$("#taskName").val(taskName);
								}else{
									$("#taskName").val('');
								}
							}else{
								$("#taskName").val('');
							}
							// 重新初始化需求方选择器
							initDemandSide(change[0].taskId);
						} else {
							$("#taskId").val('');
							$("#taskName").val('');
							// 清空需求方选择器
							if(demandSideList){
								demandSideList.update({data: []});
							}
						}
					}

				},
				remoteMethod: function(val, cb, show, pageIndex){
					// 保存原始pageIndex用于前端逻辑判断
					var originalPageIndex = pageIndex;
					// 只在传递给后端时转换pageIndex
					var backendPageIndex = pageIndex;
					if(pageIndex == 1){
						backendPageIndex = -1;
					}
					if(firstTaskNameIn){
						val = '${param.taskName}';
						firstTaskNameIn = false;
					}
					ajax.remoteCall("${ctxPath}/webcall?action=revisit.getPlanningTaskNameQuery", {
						name: val,
						pageIndex: backendPageIndex,
						pageSize: 10,
						pageType: 3,
						SYN_TIME_START: $("#SynStart").val(),
						SYN_TIME_END: $("#SynEnd").val(),
						TASK_NAME: val
					}, function(result) {
						if(result.data && result.data.length > 0){
							var dataArr = [];
							for(var i in result.data){
								var item = {};
								var taskName = result.data[i].TASK_NAME;
								var taskId = result.data[i].TASK_ID;
								var id = result.data[i].ID;
								item.name = taskId+"--"+taskName;
								item.value = id;
								item.taskId = taskId;
								dataArr.push(item);
							}
							var totalPage = 0;
							if(result.totalPage > 0){
								totalPage = result.totalPage;
							}
							cb(dataArr, totalPage);
							if(taskNameData.length > 0){
								taskNameList.setValue(taskNameData);
								taskNameData = [];
							} else if(originalPageIndex == 1 && dataArr.length > 0) {
								// 只在真正的第一页时默认选中第一个任务包并加载需求方
								taskNameList.setValue([dataArr[0]]);
								$("#taskId").val(dataArr[0].taskId);
								initDemandSide(dataArr[0].taskId);
							}
						} else {
							cb([], 0);
						}
					});
				}
			});
		}

		function initDemandSide(taskId){
			if(!taskId) return;

			// 立即调用接口获取需求方数据
			ajax.remoteCall("${ctxPath}/webcall?action=revisit.getPlanningDemandSideQuery", {
				TASK_ID: taskId,
				pageIndex: 1,
				pageSize: 100,
				SYN_TIME_START: $("#SynStart").val(),
				SYN_TIME_END: $("#SynEnd").val()
			}, function(result) {
				console.log('需求方接口调用成功:', result);

				// 处理返回的数据
				var demandSideData = [];
				if(result.data && result.data.length > 0){
					for(var i in result.data){
						var item = {};
						item.name = result.data[i].DEMAND_SIDE;
						item.value = result.data[i].DEMAND_SIDE;
						demandSideData.push(item);
					}
				}

				// 创建或更新需求方选择器
				if(demandSideList){
					demandSideList.update({data: demandSideData});
					// 如果有数据，默认选中第一个
					if(demandSideData.length > 0){
						demandSideList.setValue([demandSideData[0]]);
						$("#demandSide").val(demandSideData[0].value);
					}
				} else {
					createDemandSideSelect(demandSideData);
					// 如果有数据，默认选中第一个
					if(demandSideData.length > 0){
						setTimeout(function(){
							demandSideList.setValue([demandSideData[0]]);
							$("#demandSide").val(demandSideData[0].value);
						}, 100);
					}
				}
			});
		}

		function createDemandSideSelect(data){
			demandSideList = xmSelect.render({
				el: '#demandSideSelect',
				name: "demandSide",
				filterable: true,
				paging: true,
				pageSize: 10,
				pageRemote: true,
				toolbar: { show: false },
				placeholder: '选择需求方',
				data: data,
				radio: true,
				searchTips: '输入需求方名称',
				on: function(data){
					debugger
					var arr = data.arr;
					var change = data.change;
					var isAdd = data.isAdd;
					
					if(change.length > 0){
						if(isAdd){
							$("#demandSide").val(change[0].value);
						} else {
							$("#demandSide").val('');
						}
					}
				},
				remoteMethod: function(val, cb, show, pageIndex){
					// 保存原始pageIndex用于前端逻辑判断
					var originalPageIndex = pageIndex;
					// 只在传递给后端时转换pageIndex
					var backendPageIndex = pageIndex;
					if(pageIndex == 1){
						backendPageIndex = -1;
					}
					if(firstDemandSideIn){
						val = '${param.demandSide}';
						firstDemandSideIn = false;
					}

					// 获取当前选中的任务ID
					var currentTaskId = $("#taskId").val();
					if(!currentTaskId) {
						cb([], 0);
						return;
					}

					ajax.remoteCall("${ctxPath}/webcall?action=revisit.getPlanningDemandSideQuery", {
						TASK_ID: currentTaskId,
						DEMAND_SIDE: val,
						name: val,
						pageIndex: backendPageIndex,
						pageSize: 10,
						pageType: 3,
						SYN_TIME_START: $("#SynStart").val(),
						SYN_TIME_END: $("#SynEnd").val()
					}, function(result) {
						if(result.data && result.data.length > 0){
							var dataArr = [];
							for(var i in result.data){
								var item = {};
								item.name = result.data[i].DEMAND_SIDE;
								item.value = result.data[i].DEMAND_SIDE;
								dataArr.push(item);
							}
							var totalPage = 0;
							if(result.totalPage > 0){
								totalPage = result.totalPage;
							}
							cb(dataArr, totalPage);
							if(demandSideData.length > 0){
								demandSideList.setValue(demandSideData);
								demandSideData = [];
							}else if(originalPageIndex == 1 && dataArr.length > 0){
								// 只在真正的第一页时默认选中第一个需求方
								demandSideList.setValue([dataArr[0]]);
								$("#demandSide").val(dataArr[0].value);
							}
						} else {
							cb([], 0);
						}
					});
				}
			});
		}

		// 在页面初始化时调用
		$(function(){
			if('${param.synStart}'&&'${param.synEnd}'){
				$("#SynStart").val("${param.synStart}");
				$("#SynEnd").val("${param.synEnd}");
			}else{
				var startDate = getTodayDate(-7);
				var endDate = getTodayDate();
				$("#SynStart").val(startDate+" 00:00:00");
				$("#SynEnd").val(endDate+" 23:59:59");
			}
			$("#searchForm").render({success : function(result){
				//折算类型
				if(result["common.getConvert"]){
					conversion_type = result["common.getConvert"].data;
				}
			}});
			initTaskName();
			// 需求方选择器会在任务包选择后初始化
		});
	</script>
	<script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
