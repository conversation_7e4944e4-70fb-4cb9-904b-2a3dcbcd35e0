	<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>回访结果查询</title>
	<link href="/easitline-static/lib/layui/css/layui.css" rel="stylesheet">
		<style>
			.pagination pagination-sm pageNumV{float:right}
		</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form  autocomplete="off" action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-mars="revisit.checkRole">
             	<input type="hidden" id="check" name="check" value="0">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 企划通回访结果查询</h5>
	             		 </div>
	             		 <hr style="margin:5px -15px">
	             			<div class="form-group">
	             				<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">回访人</span>	
									<input type="text" name="CREATE_ACC" class="form-control input-sm" >
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">用户号码</span>	
									<input type="text" name="CUSTOMER_PHONE" class="form-control input-sm" >
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">人群包ID</span>
									<input type="text" name="TASK_ID" class="form-control input-sm" >
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">任务包名称</span>	
									<input type="text" name="TASK_NAME" class="form-control input-sm" >
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
								      <span class="input-group-addon" style="width: 80px;">回访结果</span>
									<%--data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_KF_PROCESSING_RESULT')"--%>
									  <select class="form-control input-sm" id="revisit_result" name="REVISIT_RESULT">
                                          <option value="">请选择</option>
                                      </select>
								</div>
								<div class="input-group input-group-sm" style="width: 200px;">
								      <span class="input-group-addon" style="width: 80px;">资料来源</span>	
									  <select class="form-control input-sm" name="DATA_SOURCE" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_DATA_SOURCE')">
                                          <option value="">请选择</option>
                                      </select>
								</div>
								<div class="input-group input-group-sm">
									<span class="input-group-addon" style="width: 80px;">回访时间</span>	
									<input type="text" name="REV_TIME_START" id="RevStart" class="form-control input-sm Wdate" value="" onclick="WdatePicker({onpicked:function(){this.blur();maxTime1()},dateFmt:'yyyy-MM-dd HH:mm:ss'})">
									<span class="input-group-addon" >-</span>	
									<input type="text" name="REV_TIME_END" id="RevEnd" class="form-control input-sm Wdate" value=""   onclick="WdatePicker({minDate:'#F{$dp.$D(\'RevStart\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">									  
								</div>
								<div class="input-group input-group-sm hidden" style="width: 200px;">
									<span class="input-group-addon" style="width: 80px;">通话状态</span>
									<select class="form-control input-sm" name="CALL_STATUS" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PLANNING_CALL_STATUS')">
										<option value="">请选择</option>
									</select>
								</div>
						  </div>
						 <div class="form-group">
							  <div class="input-group input-group-sm pull-left mr-10">
								  <button type="button" id="neworder_visit_resetting" class="btn btn-sm btn-default" onclick="resetting()"><span class="glyphicon glyphicon-repeat"></span>重置</button>
						  	  </div>
							 <div class="pull-right">
								 <div class="input-group input-group-sm mr-10">
									 <button type="button" class="btn btn-sm btn-success" onclick="downloadExl()">
										 <span class="glyphicon glyphicon-export"></span> 导出
									 </button>
								 </div>
								 <div class="input-group input-group-sm ">
									 <button type="button" class="btn btn-sm btn-default" onclick="loadData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								 </div>
							 </div>

	             		 </div>
             	    </div>  
             	  	<div class="ibox-content">
	              		<div class="row table-responsive">
		           	     	<table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" >
                             	<thead>
	                         		 <tr>
		                         	      <th class="text-c">回访结果</th>
									      <th class="text-c">回访人</th>
									      <th class="text-c">回访时间</th>
									      <th class="text-c">用户姓名</th>
									      <th class="text-c">用户号码</th>
									      <th class="text-c">查看</th>
									      <th class="text-c">任务包</th>
									      <th class="text-c">通话状态
											  <i class="layui-icon layui-icon-about tips" style="color: #1E9FFF;"
												 title="待联系：市场推广搜集到线索后，线索的初始通话状态。
未接通：给用户拨打过电话，但是电话未接通。
已接通：给用户拨打过电话，电话已接通但是通话时间小于10s。
有效沟通：给用户拨打过电话，电话已接通目通话时间不小于10s且不大于30s。
深度沟通：给用户拨打过电话，电话已接通且通话时间大于30s。"></i>
										  </th>
										  <th class="text-c">人群包ID</th>
									      <th class="text-c">需求方</th>
									      <th class="text-c">操作</th>
			   						 </tr>
                            	 </thead>
                             <tbody id="dataList">
                             </tbody>
                           </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
                                      		<%--<td>{{dictFUN:REVISIT_RESULT 'PLANNING_KF_PROCESSING_RESULT'}}</td>--%>
                                      		<td>{{call:REVISIT_RESULT fn='revertRevisitResultData'}}</td>
                                     		<td>{{:CREATE_ACC}}</td>
                                      		<td>{{:CREATE_TIME}}</td>
                                      		<td>{{:CUSTOMER_NAME}}</td>
                                      		<td>{{call:CUSTOMER_PHONE CUSTOMER_PHONE_data #index+1 'phone' fn='getData2'}}</td>
                                      		<td style="width:50px;max-width:50px;min-width:50px" rowspan="{{:rowspan}} "title="查看">
												<span onclick="showDetail('{{:#index+1}}','{{:CUSTOMER_PHONE_data}}')" class="glyphicon glyphicon-eye-open" id="show{{:#index+1}}" style="color: rgb(255, 140, 60);"></span>
											</td>
											<td>{{:TASK_NAME}}</td>
											<td>{{dictFUN:CALL_STATUS 'PLANNING_CALL_STATUS'}}</td>
											<td>{{:TASK_ID}}</td>
											<td>{{:DEMAND_SIDE}}</td>
											<td>
												{{if DATA_SOURCE == '3'}}
													<a href="javascript:;" onclick="result.toHandle3('{{:C_PLANNING_ID}}','{{:DATA_SOURCE}}','{{:ID}}')">查看</a>
												{{/if}}
												{{if DATA_SOURCE == '2'}}
													<a href="javascript:;" onclick="result.toHandle2('{{:C_PLANNING_ID}}','{{:DATA_SOURCE}}','{{:ID}}')">查看</a>
												{{/if}}
												{{if DATA_SOURCE == '1'}}
													<a href="javascript:;" onclick="result.watchResult('{{:C_PLANNING_ID}}','{{:DATA_SOURCE}}')">查看</a>
													<EasyTag:res resId="planning_result_tohandle">
													<a href="javascript:;" onclick="result.toHandle('{{:C_PLANNING_ID}}','{{:DATA_SOURCE}}')">编辑</a>
												</EasyTag:res>
												{{/if}}
												
											</td>
									    </tr>
								   {{/for}}					         
						 	</script>
	              		 </div> 
	              		 <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="10" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	                  </div>
              	</div>
        </form>
        
</EasyTag:override>

<EasyTag:override name="script">
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
		jQuery.namespace("result");
		requreLib.setplugs('wdate')//加载时间控件
		var conversion_type;
		var totalRevisitResultDict = {};
		function loadData(){
			$("#tableHead").data("mars","revisit.getRevisitResultList");
			$("#searchForm").searchData();
		}
		$(function(){
			var startDate = getTodayDate(-7);
   	    	var endDate = getTodayDate();
	    	$("#RevStart").val(startDate+" 00:00:00");
	    	$("#RevEnd").val(endDate+" 23:59:59");
	    	$("#searchForm").render({success : function(result){
	    		
	    		if(result["revisit.checkRole"]){
	    			var check = result["revisit.checkRole"].check
	    			$("#check").val(check)
	    		}
    		}});
			result.getRevisitResultData();
	    });

		$.views.converters("CONVERSION_TYPE", function(val) {
			var req = conversion_type;
			if (typeof (req) == "undefined") {
				return val;
			} else {
				return req[val];
			}
		});

		//这里发出2个字典请求，PLANNING_REVISIT_RESULT和PLANNING_REVISIT_RESULT1，再将它们的返回数据组成一个对象，存储在totalRevisitResultDict中
		result.getRevisitResultData = function(){
			// 并行发起两个独立请求
			const request1 = new Promise((resolve) => {
				ajax.remoteCall("/yq_common/webcall",
						{params: {}, controls: ["dict.getDictList('PLANNING_REVISIT_RESULT1')"]},
						(result) => resolve(result)
				);
			});

			const request2 = new Promise((resolve) => {
				ajax.remoteCall("/yq_common/webcall",
						{params: {}, controls: ["dict.getDictList('PLANNING_REVISIT_RESULT')"]},
						(result) => resolve(result)
				);
			});

			// 等待所有请求完成（无论成功失败）
			Promise.allSettled([request1, request2]).then((results) => {
				results.forEach((result) => {
					if(result.status === 'fulfilled' && result.value) {
						const data = Object.values(result.value)[0]?.data;
						if(data) {
							totalRevisitResultDict = $.extend(totalRevisitResultDict, data);
						}
					}
				});
				$('#revisit_result').empty().append('<option value="">请选择</option>');
				$.each(totalRevisitResultDict, function(code, name) {
					$('#revisit_result').append($('<option>', {
						value: code,  // 使用键作为选项值
						text: name    // 使用值作为显示文本
					}));
				});
			});
		}

		result.watchResult = function(pId){
			ajax.remoteCall("${ctxPath}/servlet/revisit?action=jumpResult",{pId:pId},function(result){
				if(result.state == 1){
					var sStyle="width=1200px,height=680px,scrollbars=yes,resizable=no,status=1";
					window.open(result.data,"",sStyle);
				}else{
					layer.alert(result.msg,{icon:5});
				}
			})
		}
		
		result.toHandle = function(pId,type,resultId){
			
				ajax.remoteCall("${ctxPath}/servlet/revisit?action=jumpResultHandle",{pId:pId,resultId:resultId},function(result){
					if(result.state == 1){
						var sStyle="width=1200px,height=680px,scrollbars=yes,resizable=no,status=1";
						window.open(result.data,"",sStyle);
					}else{
						layer.alert(result.msg,{icon:5});
					}
				})
		}
		result.toHandle2 = function(pId,type,resultId){
			
				ajax.remoteCall("${ctxPath}/servlet/revisit?action=jumpResultHandle",{pId:pId,resultId:resultId},function(result){
					if(result.state == 1){
						popup.layerShow({
							type:2,
							title:'快反处理',
							shadeClose:false,
							scrollbar: false,
							area:['1200px','700px'],
							end:function(){
								reload()
							},
							offset:'20px'},result.data);
					}else{
						layer.alert(result.msg,{icon:5});
					}
				})
		}
		result.toHandle3 = function(pId,type,resultId){
			
				ajax.remoteCall("${ctxPath}/servlet/revisit?action=jumpResultHandle",{pId:pId,resultId:resultId},function(result){
					if(result.state == 1){
						popup.layerShow({
							type:2,
							title:'快反处理',
							shadeClose:false,
							scrollbar: false,
							area:['1200px','700px'],
							end:function(){
								reload()
							},
							offset:'20px'},result.data);
					}else{
						layer.alert(result.msg,{icon:5});
					}
				})
		}
		
		function resetting(){
			document.searchForm.reset(); 
			var startDate = getTodayDate(-7);
   	    	var endDate = getTodayDate();
	    	$("#RevStart").val(startDate+" 00:00:00");
	    	$("#RevEnd").val(endDate+" 23:59:59");
			loadData();
		}
		function cleanVal(data,id){//如果值为空则清空某个id的值
			if($(data).val()==""){
				$("#"+id).val('');
			}
		}
		
		function  getData(val,valData,index){
			return "<span id='"+index+"-"+valData+"' data-val='"+val+"'>"+val+"</span>"
		}
		    
		var showId = {};
		result.showDetail = function(id,phone,name,address){
			if($("#show"+id).hasClass("glyphicon-eye-close")){
				showId[id]='';
				var phoneId= document.getElementById(id+"-"+phone)
				phoneId.innerHTML= phoneId.getAttribute("data-val");
				$("#show"+id).addClass("glyphicon-eye-open");
				$("#show"+id).removeClass("glyphicon-eye-close");
			}else{//显示
				var phoneId= document.getElementById(id+"-"+phone)
				decrypt({"value":phone},function(value){ phone=value});
				phoneId.innerHTML= phone;
				$("#show"+id).removeClass("glyphicon-eye-open");
				$("#show"+id).addClass("glyphicon-eye-close");
				showId[id]='show';
				auditLog(JSON.stringify({
					"model":"planning",
					"url":"/planning/pages/planning/revisit-handle-list.jsp",
					"action":"query",
					"describe":"用户查询[企划通回访处理列表]数据，查看客户["+name+"]敏感信息：[用户号码："+phone+"]"}));
			}
		}

		function auditLog(data){
			$.post('/iccportal5/servlet/auditLog', {action: 'Log', data: data}, function (result) {
				console.log(result);
			}, 'json');
		}
		function decrypt(data,returnVal){
			ajax.remoteCall("/iccportal5/servlet/auditLog?action=decrypt",data,function(result) {
	    		if(result.state == 1){
	    			returnVal(result.data)
				}
	    	},{async:false});
		}
		function showDetail(id,customerTel1){
			showDetailCommon({
				"model":"planning",
				"url":"/planning/pages/planning/revisit-result-list.jsp",
				"action":"query",
				"describe":"用户查询[企划通回访处理列表]数据，査看敏感信息:[用户号码：{{customerTel1}}]"},id,null,null,customerTel1);
		}

		function downloadExl() {
			location.href = "${ctxPath}/servlet/revisit?action=exportRevisitResult&"
					+ $("#searchForm").serialize();
		}

		function revertRevisitResultData(content){
			return totalRevisitResultDict[content];
		}
	</script>
	<script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>