package com.yunqu.cc.planning.service;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.planning.base.CommLogger;
import com.yunqu.cc.planning.base.Constants;
import com.yunqu.cc.planning.enums.DataSourceEnum;
import com.yunqu.cc.planning.enums.MeiyunxiaoBusinessTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;

public class DataParseService {

    public static EasyQuery getQuery() {
        EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.ORDER_DS);
        query.setMaxRow(100000);
        return query;
    }

    private static Logger logger = CommLogger.logger;

    /**
     * 对于美云销的天猫留资 的存储，将来源渠到媒体数据，转为事业部字段存储
     * @param record
     * @param media
     */
    public static void parseMediaToOrgCode(EasyRecord record, String media) {
        if(record==null || StringUtils.isBlank(media)){
            logger.info("record为null 或 media为空，不进行存储, media:"+media);
            return;
        }
        String dataSource = record.getString("DATA_SOURCE");
        String businessType = record.getString("BUSINESS_TYPE");
        logger.info("parseMediaToOrgCode方法转换-->DATA_SOURCE："+dataSource+" | BUSINESS_TYPE:"+businessType+" | media:"+media);
        //美云销     天猫留资
        if(DataSourceEnum.DATA_SOURCE3.getCode().equals(dataSource)
                && MeiyunxiaoBusinessTypeEnum.TMALL_LEAD_GENERATION.getType().equals(businessType)){
            record.put("ORG_CODE", media);
        }
    }
}
