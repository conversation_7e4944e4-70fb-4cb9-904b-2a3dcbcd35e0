package com.yunqu.cc.planning.inf;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.planning.base.CommLogger;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;


public class ConvergeTmailPassDataService extends IService {

    public Logger logger = CommLogger.logger;


    @Override
    public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
        logger.info("定时任务开始，接收参数jsonObject:"+jsonObject);
        ConvergeTmailDataService convergeTmailDataService = new ConvergeTmailDataService();

        String currentDateStr = DateUtil.getCurrentDateStr("yyyy-MM-dd");
        //拿到过往的31天的日期
        for(int i=0;i<31;i++){
            String syncDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, currentDateStr, -i);
            convergeTmailDataService.getTmailDataToStat(syncDate);
        }
        logger.info("定时任务结束");
        return EasyResult.ok();
    }

}
