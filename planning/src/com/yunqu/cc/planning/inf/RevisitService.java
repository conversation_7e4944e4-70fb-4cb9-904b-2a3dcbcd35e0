package com.yunqu.cc.planning.inf;

import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.*;
import java.util.Base64.Decoder;

import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.UserModel;
import com.yunqu.cc.planning.enums.DataSourceEnum;
import com.yunqu.cc.planning.service.DataParseService;
import com.yunqu.cc.planning.service.InitTableDataService;
import com.yunqu.openapi.utils.OpenApiUserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yunqu.cc.planning.base.CommLogger;
import com.yunqu.cc.planning.base.Constants;
import com.yunqu.cc.planning.utils.StringUtil;

import javax.servlet.http.HttpServletRequest;

public class RevisitService extends IService {

	private static Logger apiLogger = CommLogger.getCommLogger("apiLog");
	private static Logger decLogger = CommLogger.getCommLogger("decrypt");
	private static Logger logger = CommLogger.getCommLogger();
	private static EasyCache cache = CacheManager.getMemcache();

	protected static EasyQuery getQuery(){
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.ORDER_DS);
	}

	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		if("PLANNING_SYN".equals(command)) {
			return synRevisitDataTemp(json,1);
		}if("PLANNING_SYN_UNSAFE".equals(command)) {
			return synRevisitDataTemp(json,2);
		}else if("PLANNING_RESULT_SYN".equals(command)){
			return updateTask(json);
		}else if("PLANNING_DECRYPT_DATA".equals(command)) {
			String cacheKey = "JOB_PLANNING_DECRYPT_DATA";
			String value = cache.get(cacheKey);
			if(value==null||"".equals(value)){
				cache.put(cacheKey,"1",60 * 15);
				saveRevisitData();
				cache.delete(cacheKey);
			}
			return EasyResult.ok();
		}else if("PLANNING-TIME-TO-DISTRIBUTE".equals(command)){
			return DistributeRevisitService.getInstance().invoke();
		}else {
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "error：不存在的command！");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 不存在的command,请检查:" + command);
			return result;
		}
	}

	/**
	 * 1.将同步数据写入临时表
	 * @param json
	 * @param type 1 需要解密 先入临时表 2不需要解密 直接入主库 默认1
	 * @return
	 */
	private JSONObject synRevisitDataTemp(JSONObject json,int type) {
		JSONObject result = JsonUtil.createInfRespJson(json);
		JSONArray data = json.getJSONArray("data");
		if(data==null||data.size()==0) {
			result.put("respDesc","error：参数缺失");
			result.put("respCode",GWConstants.RET_CODE_MSG_FORMAT_ERROR);
			return result;
		}
		apiLogger.info("同步的数据，数据数量：" + data.size() + "，数据"+json.toJSONString());//入参日志
		String currentTime = EasyCalendar.newInstance().getDateTime("-");
		try {
			EasyRecord record = new EasyRecord("CC_PLANNING_TEMP");
			if(type==2){
				record = new EasyRecord("CC_PLANNING");
				record.set("REVISIT_STATUS",Constants.NO_PUBLISH);
				record.set("IS_DONE",Constants.NOT_DONE);
			}
			
			for(int i=0;i<data.size();i++) {
				JSONObject obj = data.getJSONObject(i);
				record.set("ID", RandomKit.uniqueStr());
				if(StringUtils.isNotBlank(obj.getString("taskId"))){
					record.set("TASK_ID",obj.getString("taskId"));
				}else{
					record.set("TASK_ID",obj.getString("touchTaskId"));
				}
				if(StringUtils.isNotBlank(obj.getString("taskName"))){
					record.set("TASK_NAME",obj.getString("taskName"));
				}else{
					record.set("TASK_NAME",obj.getString("touchTaskName"));
				}
				record.set("QUESTION_ID",obj.getString("questionId"));
				record.set("QUESTION_URL",obj.getString("questionUrl"));
				record.set("DEMAND_SIDE",obj.getString("demandSide"));
				record.set("DEMAND_SIDE_DEPT",obj.getString("demandSideDept"));
				record.set("ORG_CODE",obj.getString("orgCode"));
				record.set("CUSTOMER_NAME",obj.getString("customerName"));
				record.set("CUSTOMER_PHONE",obj.getString("customePhone"));
				record.set("SYN_TIME",obj.getString("synTime"));
				record.set("USER_INFO_ID",obj.getString("userInfoId"));
				record.set("USER_INFO_URL",obj.getString("userInfoUrl"));
				//record.set("DATA_SOURCE",StringUtils.isNotBlank(obj.getString("dataSource"))?obj.getString("dataSource"):"1");//默认企划通
				record.set("DATA_SOURCE",StringUtils.isNotBlank(obj.getString("dataSource"))?obj.getString("dataSource"): DataSourceEnum.DATA_SOURCE1.getCode());//默认企划通

				record.set("BUSINESS_TYPE",StringUtils.isNotBlank(obj.getString("businessType"))?obj.getString("businessType"):"");//默认企划通

				//remark:美云销新增商机备注
				record.set("CONTENT", StringUtils.isNotBlank(obj.getString("remark"))?obj.getString("remark"):obj.getString("content"));
				record.set("DATA_SOURCE_NOTE",obj.getString("dataSourceNote"));
				
				record.set("CREATE_TIME",currentTime);
				record.set("PUT_MEDIA", obj.getString("putMedia"));

				if(type==2){//不用加解密数据
					InitTableDataService.savePlanningTaskInfo(record);////存储任务信息
					DataParseService.parseMediaToOrgCode(record, obj.getString("putMedia"));//对于美云销的天猫留资 的存储，将来源渠到媒体数据，转为事业部字段存储
				}
				getQuery().save(record);
			}
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "操作成功");
			apiLogger.info("同步数据成功");
		} catch (Exception e) {
			apiLogger.error(CommonUtil.getClassNameAndMethod(this) + "企划通同步数据出错：" + e.getMessage(),e);
			result.put("respDesc","操作失败：系统出错");
			result.put("respCode",GWConstants.RET_CODE_OTHER_EXCEPTION);
		}
		return result;
	}


	private JSONObject updateTask(JSONObject json) {
		JSONObject result = JsonUtil.createInfRespJson(json);
		JSONArray data = json.getJSONArray("data");
		if(data==null||data.size()==0) {
			result.put("respDesc","error：参数缺失");
			result.put("respCode",GWConstants.RET_CODE_MSG_FORMAT_ERROR);
			return result;
		}
		EasyQuery query = getQuery();
		try {
			for(int i=0;i<data.size();i++) {
				JSONObject obj = data.getJSONObject(i);
				EasySQL sql = new EasySQL("UPDATE CC_PLANNING SET");
				sql.append(obj.getString("joinActivityId"),"JOIN_ACTIVITY_ID = ?,");
				sql.append(obj.getString("submitTime"),"SUBMIT_TIME = ? WHERE 1=1");
				sql.append(obj.getString("questionId"),"AND QUESTION_ID = ?");
				sql.append(obj.getString("userInfoId"),"AND USER_INFO_ID = ?");
				sql.append(obj.getString("customePhone"),"AND CUSTOMER_PHONE = ?");
				sql.append("AND SUBMIT_TIME IS NULL AND JOIN_ACTIVITY_ID IS NULL");
				query.executeUpdate(sql.getSQL(), sql.getParams());
			}
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "操作成功");
		} catch (Exception e) {
			apiLogger.error(CommonUtil.getClassNameAndMethod(this) + "企划通任务更新出错：" + e.getMessage(),e);
			result.put("respDesc","操作失败：系统出错");
			result.put("respCode",GWConstants.RET_CODE_OTHER_EXCEPTION);
		}
		return result;
	}

	/**
	 * 2.定时任务将临时表写入主表
	 * 解密手机号
	 * @return
	 */
	public void saveRevisitData() {
		decLogger.info("------------------开始从临时表查询数据解密企划通数据入库主表------------------");
			EasyQuery query = getQuery();
			JSONArray decrypts = new JSONArray();
			List<JSONArray> decryptPhoneGroup = new ArrayList<JSONArray>();
			List<EasyRow> list = null;
			try {
				list = query.queryForList("select * from CC_PLANNING_TEMP",null);
			} catch (SQLException e) {
				decLogger.error("查询临时表数据异常:"+e.getMessage(),e);
			}
			if(list!=null&&list.size()>0) {
				for (int i = 0; i < list.size(); i++) {
					EasyRow easyRow = list.get(i);
					String custPhone = easyRow.getColumnValue("CUSTOMER_PHONE");
					if(StringUtils.isBlank(custPhone)) {
						continue;
					}
					JSONObject param = new JSONObject();
					param.put("action", "Encrypt");
					param.put("ciphertextBlob", custPhone);
					param.put("dataType", "mobile");
					decrypts.add(param);
					if(i==list.size()-1||(i+1)%10000==0) {//分组解密，最多一次解密10000条
						decryptPhoneGroup.add(decrypts);
						decrypts = new JSONArray();
					}
				}
				Map<String, String> decryptPhoneMap = decryptPhone(decryptPhoneGroup);
				EasyRecord record = new EasyRecord("CC_PLANNING");
				for (int i=0;i<list.size();i++) {
					EasyRow easyRow = list.get(i);
					String tempId = easyRow.getColumnValue("ID");
					String encryptCustPhone = easyRow.getColumnValue("CUSTOMER_PHONE");
					String decryptCustPhone = decryptPhoneMap.get(encryptCustPhone);

					if(StringUtils.isNotBlank(decryptCustPhone)) {
						record.set("ID", RandomKit.uniqueStr());
						record.set("TASK_ID",easyRow.getColumnValue("TASK_ID"));
						record.set("TASK_NAME",easyRow.getColumnValue("TASK_NAME"));
						record.set("QUESTION_ID",easyRow.getColumnValue("QUESTION_ID"));
						record.set("QUESTION_URL",easyRow.getColumnValue("QUESTION_URL"));
						record.set("DEMAND_SIDE",easyRow.getColumnValue("DEMAND_SIDE"));
						record.set("DEMAND_SIDE_DEPT",easyRow.getColumnValue("DEMAND_SIDE_DEPT"));
						record.set("ORG_CODE",easyRow.getColumnValue("ORG_CODE"));
						record.set("CUSTOMER_NAME",easyRow.getColumnValue("CUSTOMER_NAME"));
						record.set("SYN_TIME",easyRow.getColumnValue("SYN_TIME"));
						record.set("DATA_SOURCE",easyRow.getColumnValue("DATA_SOURCE"));
						record.set("USER_INFO_ID",easyRow.getColumnValue("USER_INFO_ID"));
						record.set("USER_INFO_URL",easyRow.getColumnValue("USER_INFO_URL"));
						record.set("CONTENT",easyRow.getColumnValue("CONTENT"));
						record.set("DATA_SOURCE_NOTE",easyRow.getColumnValue("DATA_SOURCE_NOTE"));
						record.set("CUSTOMER_PHONE",decryptCustPhone);
						record.set("CREATE_TIME",DateUtil.getCurrentDateStr());
						record.set("REVISIT_STATUS",Constants.NO_PUBLISH);
						record.set("IS_DONE",Constants.NOT_DONE);
						DataParseService.parseMediaToOrgCode(record, easyRow.getColumnValue("PUT_MEDIA"));//对于美云销的天猫留资 的存储，将来源渠到媒体数据，转为事业部字段存储
						try {
							query.save(record);
							InitTableDataService.savePlanningTaskInfo(record); //存储任务信息
							query.execute("delete from CC_PLANNING_TEMP where id=?", tempId);
						} catch (SQLException e) {
							decLogger.error("保存db异常:"+e.getMessage(),e);
						}
					}
				}
			}else {
				decLogger.info("待解密数量为0");
			}
		decLogger.info("----------------------------解密流程完毕----------------------------");
	}

	/**
	 * 手机号通过接口解密
	 * @param encryptPhoneList
	 * @return
	 */
	private Map<String,String> decryptPhone(List<JSONArray> encryptPhoneList) {
		HashMap<String, String> hashMap = new HashMap<String,String>();
		for(int i=0;i<encryptPhoneList.size();i++) {
			JSONArray jsonArray = encryptPhoneList.get(i);
			JSONObject resp = null;
			try {
				IService service = ServiceContext.getService("MIXGW_DSMP_INTEFACE");
				JSONObject queryParams = new JSONObject();
				queryParams.put("command","decrypt");
				queryParams.put("params",jsonArray);
				resp = service.invoke(queryParams);
			} catch (ServiceException e) {
				decLogger.error("查询手机号码接口异常="+jsonArray.toJSONString()+"e:"+e.getMessage(),e);
				continue;
			}
			String respCode = resp.getString("respCode");
			if ("000".equals(respCode)) {// "000" 成功，查询接口
				JSONObject respData = resp.getJSONObject("respData");
				JSONArray data = respData.getJSONArray("result");
				Decoder decoder = Base64.getDecoder();
				for(int j=0;j<data.size();j++) {
					JSONObject jsonObject = null;
					try {
						jsonObject = data.getJSONObject(j);
						String plaintext = jsonObject.getString("plaintext");
						String ciphertextBlob = jsonObject.getString("ciphertextBlob");
						String phone = new String(decoder.decode(plaintext), StandardCharsets.UTF_8);
						hashMap.put(ciphertextBlob, phone);
					} catch (Exception e) {
						//todo 保底逻辑(可以进一步优化)
						decLogger.error("此条数据无法解密 jsonObject:" + jsonObject);
					}
				}
			}else {
				decLogger.info("查询手机号码失败，resp="+resp);
			}
		}
		return hashMap;
	}

	public EasySQL getPlanningReportSql(JSONObject param){
		EasySQL sql = new EasySQL("SELECT T2.TASK_ID, T2.DEMAND_SIDE,T2.DEMAND_SIDE_DEPT,T2.TASK_NAME,COUNT(1) AS SYN_NUM,"
				+ "SUM(CASE WHEN (T1.REVISIT_RESULT IS NOT NULL AND REGEXP_LIKE(T1.REVISIT_RESULT, '^\\d+$') AND T1.REVISIT_RESULT = 1) THEN 1 ELSE 0 END) AS SUCCESS_NUM,"
				+ "SUM(CASE WHEN (T1.REVISIT_RESULT IS NOT NULL AND REGEXP_LIKE(T1.REVISIT_RESULT, '^\\d+$') AND T1.REVISIT_RESULT = 2) THEN 1 ELSE 0 END) AS NOBODY_NUM,"
				+ "SUM(CASE WHEN (T1.REVISIT_RESULT IS NOT NULL AND REGEXP_LIKE(T1.REVISIT_RESULT, '^\\d+$') AND T1.REVISIT_RESULT = 3) THEN 1 ELSE 0 END) AS NO_RESPONSE_NUM,"
				+ "SUM(CASE WHEN (T1.REVISIT_RESULT IS NOT NULL AND REGEXP_LIKE(T1.REVISIT_RESULT, '^\\d+$') AND T1.REVISIT_RESULT = 4) THEN 1 ELSE 0 END) AS REFUSE_NUM,"
				+ "SUM(CASE WHEN (T1.REVISIT_RESULT IS NOT NULL AND REGEXP_LIKE(T1.REVISIT_RESULT, '^\\d+$') AND T1.REVISIT_RESULT = 5) THEN 1 ELSE 0 END) AS NUMBER_ERROR_NUM,"
				+ "SUM(CASE WHEN (T1.REVISIT_RESULT IS NOT NULL AND REGEXP_LIKE(T1.REVISIT_RESULT, '^\\d+$') AND T1.REVISIT_RESULT = 6) THEN 1 ELSE 0 END) AS EXIST_NUM,"
				+ "SUM(CASE WHEN (T1.REVISIT_RESULT IS NOT NULL AND REGEXP_LIKE(T1.REVISIT_RESULT, '^\\d+$') AND T1.REVISIT_RESULT = 7) THEN 1 ELSE 0 END) AS BUSI_NUM,"
				+ "SUM(CASE WHEN (T1.REVISIT_RESULT IS NOT NULL AND REGEXP_LIKE(T1.REVISIT_RESULT, '^\\d+$') AND T1.REVISIT_RESULT = 8) THEN 1 ELSE 0 END) AS NOT_CURR_NUM,"
				+ "SUM(CASE WHEN (T1.REVISIT_RESULT IS NOT NULL AND REGEXP_LIKE(T1.REVISIT_RESULT, '^\\d+$') AND T1.REVISIT_RESULT = 9) THEN 1 ELSE 0 END) AS SHUTDWON_NUM,"
				+ "SUM(CASE WHEN (T1.REVISIT_RESULT IS NOT NULL AND REGEXP_LIKE(T1.REVISIT_RESULT, '^\\d+$') AND T1.REVISIT_RESULT = 10) THEN 1 ELSE 0 END) AS OTHER_NUM"

				+ " FROM CC_PLANNING_RESULT T1 RIGHT JOIN CC_PLANNING T2 ON T1.C_PLANNING_ID = T2.ID WHERE 1=1");
		sql.append(param.getString("SYN_TIME_START"),"AND T2.SYN_TIME >= ?");
		sql.append(param.getString("SYN_TIME_END"),"AND T2.SYN_TIME <= ?");
		sql.append(param.getString("REVISIT_TIME_START"),"AND T2.DONE_TIME >= ?");
		sql.append(param.getString("REVISIT_TIME_END"),"AND T2.DONE_TIME <= ?");
		sql.appendLike(param.getString("DEMAND_SIDE"),"AND T2.DEMAND_SIDE LIKE ?");
		sql.appendLike(param.getString("DEMAND_SIDE_DEPT"),"AND T2.DEMAND_SIDE_DEPT LIKE ?");
		sql.appendLike(param.getString("TASK_NAME"),"AND T2.TASK_NAME LIKE ?");
		sql.append(param.getString("TASK_ID"),"AND T2.TASK_ID = ?");

		//天猫留资的数据隔离条件
		sql.append(param.getString("DATA_SOURCE"), "AND T2.DATA_SOURCE = ? ");
		sql.append(param.getString("BUSINESS_TYPE"), "AND T2.BUSINESS_TYPE = ? ");

		sql.append("GROUP BY T2.TASK_ID,T2.TASK_NAME,T2.DEMAND_SIDE,T2.DEMAND_SIDE_DEPT");
		return sql;
	}

	/**
	 * 计算	已回访=所有的回访结果相加，从有效到其他全部相加。
	 * 		有效率=有效回访/已回访。
	 * 		接通率=（有效回访+用户拒访+不方便接听+非当事人不明情况+其他）/已回访。
	 * @param dataItem
	 * @return
	 */
	public JSONObject getPlanningReportRate(JSONObject dataItem) {
		int successNum = dataItem.getIntValue("SUCCESS_NUM");
		int nobodyNum = dataItem.getIntValue("NOBODY_NUM");
		int noResponseNum = dataItem.getIntValue("NO_RESPONSE_NUM");
		int refuseNum = dataItem.getIntValue("REFUSE_NUM");
		int numberErrorNum = dataItem.getIntValue("NUMBER_ERROR_NUM");
		int existNum = dataItem.getIntValue("EXIST_NUM");
		int busiNum = dataItem.getIntValue("BUSI_NUM");
		int notCurrNum = dataItem.getIntValue("NOT_CURR_NUM");
		int shutdwonNum = dataItem.getIntValue("SHUTDWON_NUM");
		int otherNum = dataItem.getIntValue("OTHER_NUM");
		//已回访=所有的回访结果相加，从有效到其他全部相加。
		int visitNum = successNum + nobodyNum + noResponseNum + refuseNum + numberErrorNum
				+ existNum + busiNum + notCurrNum + shutdwonNum + otherNum;
		String visitRateStr = "0.0";
		String answerRateStr = "0.0";
		if(visitNum > 0){
			//有效率=有效回访/已回访。
			logger.info("successNum:"+successNum+" / visitNum:"+visitNum);
			float visitRate = visitNum == 0 ? 0 : ((float) successNum / (float) visitNum * 100);
			visitRateStr = String.format("%.1f", visitRate);
		}
		if(visitNum > 0){
			//接通率=（有效回访+用户拒访+不方便接听+非当事人不明情况+其他）/已回访。
			logger.info("(successNum+refuseNum+busiNum+notCurrNum+otherNum):"+(successNum+refuseNum+busiNum+notCurrNum+otherNum)+" / visitNum:"+visitNum);
			float answerRate = visitNum == 0 ? 0 : ((float) (successNum+refuseNum+busiNum+notCurrNum+otherNum) / (float) visitNum * 100);
			answerRateStr = String.format("%.1f", answerRate);
		}
		dataItem.put("VISIT_NUM", visitNum);
		dataItem.put("VISIT_RATE", visitRateStr+"%");
		dataItem.put("ANSWER_RATE", answerRateStr+"%");
		return dataItem;
	}

	/**
	 * 这里获取用户所属部门名称，对应部门名称拿到所属的字典数据TMALL_RETENTION_FOLLOW-UP_MAPPING里面的dataSource=3&businessType=6
	 * 美云销数据来源dataSource为3不变，新增一个字段留资渠道类型businessType：1客服中心;2精准引流;3售后工程师;4老客激活;5设计师;6天猫新零售;（已确定数据类型）
	 * 系统以dataSource与businessType为条件，对天猫留资进行数据权限隔离。
	 *
	 * @return
	 */
	public static JSONObject getTMallDataFilterConditions(HttpServletRequest request){
		if(request == null){
			return null;
		}
		UserModel userModel = OpenApiUserUtil.getUser(request);
		String epCode = userModel.getEpCode();
		String deptName = userModel.getDeptName();
		//1、这里拿到配置的字典
		JSONObject tmallRetentionMapping = DictCache.getJsonEnableDictListByGroupCode(epCode, "TMALL_RETENTION_FOLLOW-UP_MAPPING");
		if(tmallRetentionMapping == null){
			return null;
		}
		JSONObject dictDataJson = tmallRetentionMapping.getJSONObject("data");
		//2、根据部门名称作为值，获取到名称：dataSource与businessType
		String dictStr = dictDataJson.getString(deptName);  //字典配置：值：天猫留资 | 名称：dataSource=3&businessType=6


		if(StringUtils.isNotBlank(dictStr)){
			//将dictStr 转换为json对象
			JSONObject jsonMap = new JSONObject();
			String[] pairs = dictStr.split("&");
			for (String pair : pairs) {
				String[] keyValue = pair.split("=", 2);
				if (keyValue.length == 2) {
					String key = keyValue[0];
					String value = keyValue[1];
					jsonMap.put(key, value);
				}
			}
			return jsonMap;
		}
		return null;
	}

}
