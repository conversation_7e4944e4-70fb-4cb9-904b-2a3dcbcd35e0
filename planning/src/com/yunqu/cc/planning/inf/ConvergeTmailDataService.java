package com.yunqu.cc.planning.inf;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.planning.base.CommLogger;
import com.yunqu.cc.planning.base.Constants;
import com.yunqu.cc.planning.enums.DataSourceEnum;
import com.yunqu.cc.planning.enums.DataSourceNoteEnum;
import com.yunqu.cc.planning.enums.MeiyunxiaoBusinessTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;

import java.util.List;


public class ConvergeTmailDataService extends IService {

    public Logger logger = CommLogger.logger;

    private static EasyCache cache = CacheManager.getMemcache();

    public EasyQuery getQuery() {
        EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.ORDER_DS);
        query.setMaxRow(100000);
        return query;
    }

    @Override
    public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
        logger.info("定时任务开始，接收参数jsonObject:"+jsonObject);
        String syncDate = jsonObject.getString("syncDate");
        this.getTmailDataToStat(syncDate);
        logger.info("定时任务结束");
        return null;
    }


    /**
     * 汇聚到stat表
     * @param syncDate: yyyy-MM-dd 同步日期
     */
    public void getTmailDataToStat(String syncDate){
        EasyQuery query = getQuery();
        try{
            String statDate = DateUtil.getCurrentDateStr(DateUtil.TIME_FORMAT_YMD);
            String createTime = DateUtil.getCurrentDateStr();
            if(StringUtils.isNotBlank(syncDate) && !StringUtils.equals("null", syncDate)){
                statDate = syncDate;
            }
            logger.info("处理日期："+statDate);
            String startTime = statDate+" 00:00:00";
            String endTime = statDate+" 23:59:59";
            long start = System.currentTimeMillis();

            logger.info("开始进行数据统计getTmailDataToStat-->statDate:"+statDate+" | startTime:"+startTime+" | endTime:"+endTime);
            //对美云销，天猫留资进行数据统计处理。
            EasySQL querySql = new EasySQL("SELECT ");
            querySql.append("t1.RECEIVE_ACC AS AGENT_ACC,");
            querySql.append("SUM(CASE WHEN t1.OUTBOUND_COUNT IS NOT NULL THEN TO_NUMBER(t1.OUTBOUND_COUNT) ELSE 0 END) AS OUTBOUND_NUM,");//外呼通话量
            querySql.append("SUM(CASE WHEN t1.OUTBOUND_LEN IS NOT NULL THEN TO_NUMBER(t1.OUTBOUND_LEN) ELSE 0 END) AS CALL_TIME_NUM,");//通话总时长
            querySql.append("SUM(CASE WHEN t1.CALL_STATUS IN (2, 3, 4) THEN 1 ELSE 0 END) AS SUC_CUST_NUM,");//接通客资量
            querySql.append("SUM(CASE WHEN t1.CALL_STATUS IN (1, 2, 3, 4) THEN 1 ELSE 0 END) AS TOTAL_CUST_NUM,");//全部接通客资量
            querySql.append("SUM(CASE WHEN t1.REVISIT_RESULT = 1 THEN 1 ELSE 0 END) AS EFFECT_CUST_NUM,");//有效客资量
            querySql.append("SUM(CASE WHEN t1.REVISIT_RESULT = -2 THEN 1 ELSE 0 END) AS CALL_AGAIN_NUM,");//待再次沟通量
            querySql.append("SUM(CASE WHEN t1.REVISIT_RESULT = -1 THEN 1 ELSE 0 END) AS CALL_NEW_CUST,");//新客资/新线索
            querySql.append("SUM(CASE WHEN t1.REVISIT_RESULT IN (3,4,6,9,20,21,-3) THEN 1 ELSE 0 END) AS CALL_INVALID,");//无效
            querySql.append("SUM(CASE WHEN t2.IS_CENTRAL_AIR = 1 THEN 1 ELSE 0 END) AS CENTRAL_AIR_NUM,");//中央空调有效客资量
            querySql.append("SUM(CASE WHEN t2.IS_ARRIVE_CUST = 1 THEN 1 ELSE 0 END) AS ARRIVE_CUST_NUM,");//到店客资量
            querySql.append("COUNT(1) AS ALLOC_CUST_NUM,");                                              //分配客资量
            querySql.append("SUM(CASE WHEN t1.FOLLOW_STATUS = 0 THEN 1 ELSE 0 END) AS TO_BE_FOLLOWED,");//待跟进
            querySql.append("SUM(CASE WHEN t1.FOLLOW_STATUS = 1 THEN 1 ELSE 0 END) AS FOLLOWING_UP,");//跟进中
            querySql.append("SUM(CASE WHEN t1.FOLLOW_STATUS = 2 THEN 1 ELSE 0 END) AS FOLLOW_UP_COMPLETED");//跟进结束

            querySql.append("FROM CC_PLANNING t1");
            querySql.append("LEFT JOIN CC_PLANNING_RESULT t2 ON t1.ID = t2.C_PLANNING_ID AND t2.IS_DONE = 'Y'");
            querySql.append("WHERE 1=1");
            querySql.append(startTime, "AND t1.SYN_TIME >= ?", false);
            querySql.append(endTime, " AND t1.SYN_TIME <= ?", false);
            querySql.append(DataSourceEnum.DATA_SOURCE3.getCode(), "AND t1.DATA_SOURCE = ?");
            querySql.append(MeiyunxiaoBusinessTypeEnum.TMALL_LEAD_GENERATION.getType(),"AND t1.BUSINESS_TYPE = ?");
            querySql.append(DataSourceNoteEnum.FIRST_REVISIT.getCode() ,"AND t1.DATA_SOURCE_NOTE = ?");
            querySql.append("GROUP BY RECEIVE_ACC");
            logger.info("查询sql:"+querySql.getSQL()+" | params:"+JSONObject.toJSONString(querySql.getParams()));
            List<JSONObject> tmailList = query.queryForList(querySql.getSQL(), querySql.getParams(), new JSONMapperImpl());

            for(JSONObject tmailRow : tmailList){
                String agentAcc = tmailRow.getString("AGENT_ACC");
                if(StringUtils.isBlank(agentAcc)){
                    query.execute("DELETE FROM CC_PLANNING_TMAIL_DATA_STAT WHERE DATE_ID = ? AND AGENT_ACC IS NULL", new Object[]{statDate});
                }else if(query.queryForExist("SELECT COUNT(1) FROM CC_PLANNING_TMAIL_DATA_STAT WHERE DATE_ID = ? AND AGENT_ACC = ?", new Object[]{statDate, agentAcc})){
                    query.execute("DELETE FROM CC_PLANNING_TMAIL_DATA_STAT WHERE DATE_ID = ? AND AGENT_ACC = ?", new Object[]{statDate, agentAcc});
                }

                EasyRecord record = new EasyRecord("CC_PLANNING_TMAIL_DATA_STAT", "ID").setPrimaryValues(RandomKit.randomStr());
                record.set("DATE_ID", statDate);
                record.set("AGENT_ACC", agentAcc);
                record.set("OUTBOUND_NUM", tmailRow.getLong("OUTBOUND_NUM"));
                record.set("CALL_TIME_NUM", tmailRow.getLong("CALL_TIME_NUM"));
                record.set("SUC_CUST_NUM", tmailRow.getLong("SUC_CUST_NUM"));
                record.set("TOTAL_CUST_NUM", tmailRow.getLong("TOTAL_CUST_NUM"));
                record.set("EFFECT_CUST_NUM", tmailRow.getLong("EFFECT_CUST_NUM"));
                record.set("CALL_AGAIN_NUM", tmailRow.getLong("CALL_AGAIN_NUM"));
                record.set("CALL_NEW_CUST", tmailRow.getLong("CALL_NEW_CUST"));
                record.set("CALL_INVALID", tmailRow.getLong("CALL_INVALID"));
                record.set("CENTRAL_AIR_NUM", tmailRow.getLong("CENTRAL_AIR_NUM"));
                record.set("ARRIVE_CUST_NUM", tmailRow.getLong("ARRIVE_CUST_NUM"));
                record.set("ALLOC_CUST_NUM", tmailRow.getLong("ALLOC_CUST_NUM"));
                record.set("TO_BE_FOLLOWED", tmailRow.getLong("TO_BE_FOLLOWED"));
                record.set("FOLLOWING_UP", tmailRow.getLong("FOLLOWING_UP"));
                record.set("FOLLOW_UP_COMPLETED", tmailRow.getLong("FOLLOW_UP_COMPLETED"));
                record.set("CREATE_TIME", createTime);
                query.save(record);
            }
            logger.info("数据统计getTmailDataToStat-->结束,统计耗时时长:"+ (System.currentTimeMillis()-start));
        }catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }
}
