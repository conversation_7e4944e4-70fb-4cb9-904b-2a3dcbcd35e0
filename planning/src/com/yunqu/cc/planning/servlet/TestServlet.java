package com.yunqu.cc.planning.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.planning.base.AppBaseServlet;
import com.yunqu.cc.planning.base.CommLogger;
import com.yunqu.cc.planning.inf.ConvergeTmailDataService;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;

import javax.servlet.annotation.WebServlet;


@WebServlet("/servlet/test")
public class TestServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private Logger logger = CommLogger.logger;

	public JSONObject actionForAppoint() {

		logger.info("接收参数jsonObject:"+getJSONObject());
		ConvergeTmailDataService convergeTmailDataService = new ConvergeTmailDataService();

		String currentDateStr = DateUtil.getCurrentDateStr("yyyy-MM-dd");
		//拿到过往的31天的日期
		for(int i=0;i<31;i++){
			String syncDate = DateUtil.addDay(DateUtil.TIME_FORMAT_YMD, currentDateStr, -i);
			convergeTmailDataService.getTmailDataToStat(syncDate);
		}
		logger.info("定时任务结束");
		return EasyResult.ok();
	}

}
