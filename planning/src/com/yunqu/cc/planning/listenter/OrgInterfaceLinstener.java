package com.yunqu.cc.planning.listenter;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yunqu.cc.planning.base.Constants;


@WebListener
public class OrgInterfaceLinstener extends ServiceContextListener{

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		
		
		ServiceResource resource = new ServiceResource();
		resource.appName = Constants.APP_NAME;
		resource.className = "com.yunqu.cc.planning.inf.RevisitService"	;
		resource.description = "企划通接口";
		resource.serviceId = "PLANNING-INTEFACE";
		resource.serviceName = "企划通接口";
		list.add(resource);
		
//		ServiceResource resource1 = new ServiceResource();
//		resource1.appName = Constants.APP_NAME;
//		resource1.className = "com.yunqu.cc.planning.inf.DistributeRevisitService";
//		resource1.description = "回访分配任务定时器";
//		resource1.serviceId = "PLANNING-DISTRIBUTE";
//		resource1.serviceName = "回访分配任务定时器";
//		list.add(resource1);

		ServiceResource resource2 = new ServiceResource();
		resource2.appName = Constants.APP_NAME;
		resource2.className = "com.yunqu.cc.planning.inf.ConvergeTmailDataService"	;
		resource2.description = "企划通天猫留资汇聚数据接口";
		resource2.serviceId = "PLANNING-TMAIL-DATA-INTEFACE";
		resource2.serviceName = "企划通天猫留资汇聚数据接口";
		list.add(resource2);

		ServiceResource resource3 = new ServiceResource();
		resource3.appName = Constants.APP_NAME;
		resource3.className = "com.yunqu.cc.planning.inf.ConvergeTmailPassDataService"	;
		resource3.description = "0点统计企划通天猫留资汇聚数据接口";
		resource3.serviceId = "PLANNING-TMAIL-DATA-INTEFACE-PASS";
		resource3.serviceName = "0点统计企划通天猫留资汇聚数据接口";
		list.add(resource3);

		return list;

	}

}
