package com.yunqu.cc.planning.dao;

import com.alibaba.fastjson.JSON;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.planning.base.AppDaoContext;
import com.yunqu.cc.planning.base.CommLogger;
import com.yunqu.cc.planning.enums.DataSourceEnum;
import com.yunqu.cc.planning.enums.DataSourceNoteEnum;
import com.yunqu.cc.planning.enums.MeiyunxiaoBusinessTypeEnum;
import com.yunqu.cc.planning.utils.StreamUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 6.外呼总体工作量报表
 * 7.客服外呼工作量报表
 */
@WebObject(name = "outboundReport")
public class OutboundReportDao extends AppDaoContext {

	private Logger logger = CommLogger.logger;

	/**
	 * 外呼报表数据
	 */
	@WebControl(name = "getOutboundReportNum",type = Types.RECORD)
	public JSONObject getOutboundReportNum() {
		JSONObject paramObj = this.param;
		logger.info("企划通回访报表--查询参数：paramObj:"+paramObj.toJSONString());
		//选择日期和比较日期
		String selectStartTime = paramObj.getString("selectStartTime");
		String selectEndTime = paramObj.getString("selectEndTime");
		String compareStartTime = paramObj.getString("compareStartTime");
		String compareEndTime = paramObj.getString("compareEndTime");

		EasyQuery query = getQuery();

		//2025-08-05
		if(StringUtils.isAnyBlank(selectStartTime, selectEndTime, compareStartTime, compareEndTime)){
			return EasyResult.fail("请选择时间范围");
		}
		int selectBwday = DateUtil.bwDays(selectStartTime, selectEndTime, "yyyy-MM-dd");
		int compareBwday = DateUtil.bwDays(compareStartTime, compareEndTime, "yyyy-MM-dd");
		if(selectBwday>31 || selectBwday<0 || compareBwday>31 || compareBwday<0){
			return EasyResult.fail("时间范围不能超过31天");
		}
		if (selectBwday != compareBwday) {
			return EasyResult.fail("选择时间范围和比较时间范围必须相同");
		}

		String queryAgentAcc = paramObj.getString("queryAgentAcc");
		//String queryAgentTeam = paramObj.getString("queryAgentTeam");//班组


		StringBuilder periodBuiler = new StringBuilder().append(selectStartTime.substring(5, selectStartTime.length())).append("~").append(selectEndTime.substring(5, selectEndTime.length()));
		String period = periodBuiler.toString();

		JSONObject resultObj = new JSONObject();

		//构建返回参数
		try {
			String[] agentAccArray = null;

			if(StringUtils.isNotBlank(queryAgentAcc)){
				agentAccArray = queryAgentAcc.split(",");
			}
			logger.info("queryAgentAcc："+JSON.toJSONString(agentAccArray));

			EasySQL newCustSql = new EasySQL("SELECT COUNT(1) FROM CC_PLANNING WHERE SYN_TIME >= ? AND SYN_TIME <= ? " +
					"AND DATA_SOURCE = ? AND BUSINESS_TYPE = ? AND DATA_SOURCE_NOTE = ?");
			//增加queryAgentAcc的条件
			if(agentAccArray!=null&&agentAccArray.length>0){
				newCustSql.append("AND RECEIVE_ACC IN (");
				for(int i=0;i<agentAccArray.length;i++){
					newCustSql.append("'"+agentAccArray[i]+"'");
					if(i<agentAccArray.length-1){
						newCustSql.append(",");
					}
				}
				newCustSql.append(")");
			}

			logger.info("查询新客户sql："+newCustSql.getSQL());
			logger.info("selectCount--> params:"+JSONObject.toJSONString(new Object[]{selectStartTime, selectEndTime,
					DataSourceEnum.DATA_SOURCE3.getCode(),MeiyunxiaoBusinessTypeEnum.TMALL_LEAD_GENERATION.getType(),DataSourceNoteEnum.FIRST_REVISIT.getCode()}));
			logger.info("compareCount--> params:"+JSONObject.toJSONString(new Object[]{compareStartTime, compareEndTime,
					DataSourceEnum.DATA_SOURCE3.getCode(),MeiyunxiaoBusinessTypeEnum.TMALL_LEAD_GENERATION.getType(),DataSourceNoteEnum.FIRST_REVISIT.getCode()}));

			int selectCount = query.queryForInt(newCustSql.getSQL(),
					new Object[]{selectStartTime, selectEndTime,
							DataSourceEnum.DATA_SOURCE3.getCode(),MeiyunxiaoBusinessTypeEnum.TMALL_LEAD_GENERATION.getType(),DataSourceNoteEnum.FIRST_REVISIT.getCode()});

			int compareCount = query.queryForInt(newCustSql.getSQL(),
					new Object[]{compareStartTime, compareEndTime,
							DataSourceEnum.DATA_SOURCE3.getCode(),MeiyunxiaoBusinessTypeEnum.TMALL_LEAD_GENERATION.getType(),DataSourceNoteEnum.FIRST_REVISIT.getCode()});

			logger.info("selectCount："+selectCount);
			logger.info("compareCount："+compareCount);

			EasySQL selectNumSql = new EasySQL("SELECT ");
			selectNumSql.append("SUM(TO_NUMBER(OUTBOUND_NUM)) AS OUTBOUND_NUM,");
			selectNumSql.append("SUM(TO_NUMBER(SUC_CUST_NUM)) AS SUC_CUST_NUM,");
			selectNumSql.append("SUM(TO_NUMBER(TOTAL_CUST_NUM)) AS TOTAL_CUST_NUM,");
			selectNumSql.append("SUM(TO_NUMBER(EFFECT_CUST_NUM)) AS EFFECT_CUST_NUM,");

			selectNumSql.append("SUM(TO_NUMBER(CALL_AGAIN_NUM)) AS CALL_AGAIN_NUM,");
			selectNumSql.append("SUM(TO_NUMBER(CENTRAL_AIR_NUM)) AS CENTRAL_AIR_NUM,");
			selectNumSql.append("SUM(TO_NUMBER(ARRIVE_CUST_NUM)) AS ARRIVE_CUST_NUM,");
			selectNumSql.append("SUM(TO_NUMBER(CALL_TIME_NUM)) AS CALL_TIME_NUM");

			selectNumSql.append("FROM CC_PLANNING_TMAIL_DATA_STAT WHERE 1=1");
			selectNumSql.append("AND DATE_ID >= ?");
			selectNumSql.append("AND DATE_ID <= ?");
			//TODO: 增加queryAgentAcc的条件
			if(agentAccArray!=null&&agentAccArray.length>0){
				selectNumSql.append("AND AGENT_ACC IN (");
				for(int i=0;i<agentAccArray.length;i++){
					selectNumSql.append("'"+agentAccArray[i]+"'");
					if(i<agentAccArray.length-1){
						selectNumSql.append(",");
					}
				}
				selectNumSql.append(")");
			}

			logger.info("查询新客户sql："+selectNumSql.getSQL());
			logger.info("selectNumSql--> params:"+JSONObject.toJSONString(new Object[]{selectStartTime, selectEndTime}));
			logger.info("compareNumSql--> params:"+JSONObject.toJSONString(new Object[]{compareStartTime, compareEndTime}));

			JSONObject selectNumRow = query.queryForRow(selectNumSql.getSQL(), new Object[]{selectStartTime, selectEndTime}, new JSONMapperImpl());
			JSONObject compareNumRow = query.queryForRow(selectNumSql.getSQL(), new Object[]{compareStartTime, compareEndTime}, new JSONMapperImpl());

			logger.info("selectNumRow："+selectNumRow);
			logger.info("compareNumRow："+compareNumRow);

			JSONObject newCustJson = this.getCompareData(selectCount, compareCount, period);

			JSONObject outboundJson = this.getCompareData(selectNumRow.getIntValue("OUTBOUND_NUM"), compareNumRow.getIntValue("OUTBOUND_NUM"), period);
			JSONObject sucCustJson = this.getCompareData(selectNumRow.getIntValue("SUC_CUST_NUM"), compareNumRow.getIntValue("SUC_CUST_NUM"), period);
			//JSONObject totalCustJson = this.getCompareData(selectNumRow.getIntValue("TOTAL_CUST_NUM"), compareNumRow.getIntValue("TOTAL_CUST_NUM"), period);
			JSONObject effectCustJson = this.getCompareData(selectNumRow.getIntValue("EFFECT_CUST_NUM"), compareNumRow.getIntValue("EFFECT_CUST_NUM"), period);

			JSONObject callAgainJson = this.getCompareData(selectNumRow.getIntValue("CALL_AGAIN_NUM"), compareNumRow.getIntValue("CALL_AGAIN_NUM"), period);
			JSONObject centralAirJson = this.getCompareData(selectNumRow.getIntValue("CENTRAL_AIR_NUM"), compareNumRow.getIntValue("CENTRAL_AIR_NUM"), period);
			JSONObject arriveCustJson = this.getCompareData(selectNumRow.getIntValue("ARRIVE_CUST_NUM"), compareNumRow.getIntValue("ARRIVE_CUST_NUM"), period);
			JSONObject callTimeJson = this.getCompareData(selectNumRow.getIntValue("CALL_TIME_NUM"), compareNumRow.getIntValue("CALL_TIME_NUM"), period);

			JSONObject sucCustRateJson = getCompareDataRate(selectNumRow.getIntValue("SUC_CUST_NUM"), selectNumRow.getIntValue("TOTAL_CUST_NUM"),
					compareNumRow.getIntValue("SUC_CUST_NUM"), compareNumRow.getIntValue("TOTAL_CUST_NUM"), period);
			JSONObject effectCustRateJson = getCompareDataRate(selectNumRow.getIntValue("EFFECT_CUST_NUM"), selectCount,
					compareNumRow.getIntValue("EFFECT_CUST_NUM"), compareCount, period);
			JSONObject centralAirRateJson = getCompareDataRate(selectNumRow.getIntValue("CENTRAL_AIR_NUM"), selectCount,
					compareNumRow.getIntValue("CENTRAL_AIR_NUM"), compareCount, period);
			JSONObject arriveCustRateJson = getCompareDataRate(selectNumRow.getIntValue("ARRIVE_CUST_NUM"), selectCount,
					compareNumRow.getIntValue("ARRIVE_CUST_NUM"), compareCount, period);

			resultObj.put("NEWCUST_NUM", newCustJson);//新增客资量

			resultObj.put("OUTBOUND_NUM", outboundJson);//外呼通话量
			resultObj.put("SUC_CUST_NUM", sucCustJson);//接通客资量
			resultObj.put("SUC_CUST_RATE", sucCustRateJson);//客资接通率

			resultObj.put("EFFECT_CUST_NUM", effectCustJson);//有效客资量
			resultObj.put("EFFECT_CUST_RATE", effectCustRateJson);//客资有效率

			resultObj.put("CALL_AGAIN_NUM", callAgainJson);//待再次沟通量
			resultObj.put("CALL_TIME_NUM", callTimeJson);//通话总时长

			resultObj.put("CENTRAL_AIR_NUM", centralAirJson);//中央空调有效客资量
			resultObj.put("CENTRAL_AIR_RATE", centralAirRateJson);//中央空调客资有效率

			resultObj.put("ARRIVE_CUST_NUM", arriveCustJson);//到店客资量
			resultObj.put("ARRIVE_CUST_RATE", arriveCustRateJson);//客资到店率

			/*String inputWord = "{" +
				"\"NEWCUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}," + //新增客资量
				"\"OUTBOUND_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}," + //外呼通话量
				"\"SUC_CUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}," +//接通客资量
				"\"SUC_CUST_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}," +//客资接通率 ******
				"\"EFFECT_CUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}," +//有效客资量
				"\"EFFECT_CUST_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}," +//客资有效率 ******
				"\"CALL_AGAIN_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}," +//待再次沟通量
				"\"CALL_TIME_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}," +//通话总时长
				"\"CENTRAL_AIR_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":-1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}," +//中央空调有效客资量

				"\"CENTRAL_AIR_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":-1,\"INCREASE_NUM\":20,\"INCREASE_RATE\":\"4.3%\"}," +//中央空调客资有效率 ******
				"\"ARRIVE_CUST_NUM\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":0,\"INCREASE_RATE\":\"0.0%\"}," +//到店客资量
				"\"ARRIVE_CUST_RATE\":{\"MAIN_NUM\":100,\"PERIOD\":\"06-06~06-07\",\"INCREASE\":0,\"INCREASE_NUM\":0,\"INCREASE_RATE\":\"0.0%\"}" +//客资到店率 ******
				"}";
		resultObj = JSON.parseObject(inputWord);*/
		} catch (SQLException e) {
			logger.error(e.getMessage(), e);
		}
		return resultObj;
	}

	private JSONObject getCompareDataRate(int selectInt, int selectTotal, int compareInt, int compareTotal, String period){
		JSONObject json = new JSONObject();
		double selectRate = 0.0;
		double compareRate = 0.0;
		if(selectTotal!=0){
			selectRate = ((double)selectInt/(double)selectTotal)*100;
		}
		if(compareTotal!=0){
			compareRate = ((double)compareInt/(double)compareTotal)*100;
		}
		double i = selectRate - compareRate;
		int increase = 0;
		double increaseNum = i;
		if(i>0){
			increase = 1;
		}else if(i<0) {
			increase = -1;
			increaseNum = (-i);
		}
		String increaseRate = "";
		if(selectRate==0.0){
			increaseRate = "0.0%";
		}else{
			increaseRate = String.format("%.1f", (increaseNum/selectRate)*100) +"%";
		}
		json.put("INCREASE", increase);
		json.put("INCREASE_NUM", String.format("%.1f", increaseNum));
		json.put("INCREASE_RATE", increaseRate);
		json.put("MAIN_NUM", String.format("%.1f", selectRate));
		json.put("PERIOD", period);
		return json;
	}

	private JSONObject getCompareData(int selectInt, int compareInt, String period){
		JSONObject json = new JSONObject();
		int i = selectInt - compareInt;
		int increase = 0;
		int increaseNum = i;
		if(i>0){
			increase = 1;
		}else if(i<0) {
			increase = -1;
			increaseNum = (-i);
		}
		String increaseRate = "";
		if(selectInt==0){
			increaseRate = "0.0%";
		}else{
			increaseRate = String.format("%.1f", ((double)increaseNum/(double)selectInt)*100) +"%";
		}
		json.put("INCREASE", increase);
		json.put("INCREASE_NUM", increaseNum);
		json.put("INCREASE_RATE", increaseRate);
		json.put("MAIN_NUM", selectInt);
		json.put("PERIOD", period);
		return json;
	}

	/**
	 * 客服外呼工作量报表数据
	 */
	@WebControl(name = "getOutboundWorkReportData",type = Types.RECORD)
	public JSONObject getOutboundWorkReportData() {
		EasyQuery query  = getQuery();
		JSONObject paramObj = this.param;
		logger.info("企划通回访报表--查询参数：paramObj:"+paramObj.toJSONString());

		//选择日期和比较日期
		String startTime = paramObj.getString("startTime");
		String endTime = paramObj.getString("endTime");
		//2025-08-05
		if(StringUtils.isAnyBlank(startTime, endTime)){
			return EasyResult.fail("请选择时间范围");
		}
		int bwday = DateUtil.bwDays(startTime, endTime, "yyyy-MM-dd");
		if(bwday>31){
			return EasyResult.fail("时间范围不能超过31天");
		}

		try{
			//从统计表中拿出数据
			EasySQL sql = new EasySQL("SELECT t1.AGENT_ACC, sum(t1.ALLOC_CUST_NUM) AS ALLOC_CUST_NUM, sum(t1.OUTBOUND_NUM) AS OUTBOUND_NUM, ");
			sql.append("sum(t1.TO_BE_FOLLOWED) AS TO_BE_FOLLOWED, sum(t1.FOLLOWING_UP) AS FOLLOWING_UP, sum(t1.FOLLOW_UP_COMPLETED) AS FOLLOW_UP_COMPLETED, ");
			sql.append("sum(t1.EFFECT_CUST_NUM) AS CALL_TRANSFER_SUC, sum(t1.CALL_AGAIN_NUM) AS CALL_AGAIN_NUM, sum(t1.CALL_NEW_CUST) AS CALL_NEW_CUST, sum(t1.CALL_INVALID) AS CALL_INVALID, ");
			sql.append("sum(t1.SUC_CUST_NUM) AS SUC_CUST_NUM, sum(t1.CENTRAL_AIR_NUM) AS CENTRAL_AIR_NUM, sum(t1.ARRIVE_CUST_NUM) AS ARRIVE_CUST_NUM, sum(t1.CALL_TIME_NUM) AS CALL_TIME_NUM, ");
			sql.append("sum(t1.TOTAL_CUST_NUM) AS TOTAL_CUST_NUM ");
			sql.append("FROM CC_PLANNING_TMAIL_DATA_STAT t1");
			sql.append(startTime, " WHERE DATE_ID >= ? ");
			sql.append(endTime, " AND DATE_ID <= ? ");
			sql.append("AND t1.AGENT_ACC IS NOT NULL");
			sql.append("GROUP BY t1.AGENT_ACC");
			logger.info("sql.getSQL()："+sql.getSQL()+" | sql.getParams()："+JSONObject.toJSONString(sql.getParams()));
			JSONObject queryJson = queryForPageList(sql.getSQL(), sql.getParams());
			logger.info("初始查询得到queryJson:"+queryJson.toJSONString());

			JSONArray pageData = queryJson.getJSONArray("data");
			if(pageData!=null && pageData.size()>0) {
				String[] agentAccArr = new String[pageData.size()];
				for (int i = 0; i < pageData.size(); i++) {
					JSONObject pageRow = pageData.getJSONObject(i);
					String agentAcc = pageRow.getString("AGENT_ACC");
					agentAccArr[i] = agentAcc;
				}
				logger.info("查询得到agentAccArr："+JSONObject.toJSONString(agentAccArr));
				//根据坐席账号，查询客服坐席的名称
				EasySQL sql2 = new EasySQL("SELECT t1.USER_ACCT, t2.USERNAME");
				sql2.append("FROM mars.EASI_USER_LOGIN t1");
				sql2.append("LEFT JOIN mars.EASI_USER t2 ON t1.USER_ID = t2.USER_ID");
				sql2.append("WHERE t1.USER_ACCT IN (");
				for (int i = 0; i < agentAccArr.length; i++) {
					sql2.append("'" + agentAccArr[i] + "'");
					if (i < agentAccArr.length - 1) {
						sql2.append(",");
					}
				}
				sql2.append(")");
				logger.info("sql2.getSQL()："+sql2.getSQL()+" | sql2.getParams()："+JSONObject.toJSONString(sql2.getParams()));
				List<JSONObject> queryUserList = query.queryForList(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
				logger.info("queryUserList:" + JSONObject.toJSONString(queryUserList));

				//将查询到的AGENT_ACC，USERNAME通过流的遍历方式，存储成一个map
				Map<String, String> agentAccNameMap = StreamUtils.stream(queryUserList).collect(Collectors.toMap(j->j.getString("USER_ACCT"), j->j.getString("USERNAME")));

				for (int i = 0; i < pageData.size(); i++) {
					JSONObject pageRow = pageData.getJSONObject(i);
					String agentAcc = pageRow.getString("AGENT_ACC");
					//分配用户的名称，计算客资的有效率，接通率，中央空调客资有效率，到店客资率
					pageRow.put("AGENT_NAME", agentAccNameMap.get(agentAcc));

					int allocCustNum = pageRow.getIntValue("ALLOC_CUST_NUM");
					String effectCustRate = allocCustNum==0?"0.0%":String.format("%.1f", (double)pageRow.getIntValue("CALL_TRANSFER_SUC")/(double)allocCustNum*100)+"%";
					pageRow.put("EFFECT_CUST_RATE", effectCustRate);

					int totalCustNum = pageRow.getIntValue("TOTAL_CUST_NUM");
					String sucCustRate = totalCustNum==0?"0.0%":String.format("%.1f", (double)pageRow.getIntValue("SUC_CUST_NUM")/(double)totalCustNum*100)+"%";
					pageRow.put("SUC_CUST_RATE", sucCustRate);

					String centralAirRate = allocCustNum==0?"0.0%":String.format("%.1f", (double)pageRow.getIntValue("CENTRAL_AIR_NUM")/(double)allocCustNum*100)+"%";
					pageRow.put("CENTRAL_AIR_RATE", centralAirRate);

					String arriveCustRate = allocCustNum==0?"0.0%":String.format("%.1f", (double)pageRow.getIntValue("ARRIVE_CUST_NUM")/(double)allocCustNum*100)+"%";
					pageRow.put("ARRIVE_CUST_RATE", arriveCustRate);
				}
				logger.info("计算后得到pageData："+JSONObject.toJSONString(pageData));
			}
			return queryJson;


					/*JSONObject resultObj = new JSONObject();
					resultObj.put("msg", "请求成功!");
					resultObj.put("state", 1);
					resultObj.put("pageSize", 10);
					resultObj.put("pageNumber", 1);
					resultObj.put("pageType", 3);

					String inputWord = "[{" +
							"\"AGENT_NAME\":\"欧阳旭\",\"AGENT_ACC\":\"ex_xuyx14\"," +

							"\"ALLOC_CUST_NUM\":80," + //分配客资量
							"\"OUTBOUND_NUM\":80," +//外呼量

							"\"TO_BE_FOLLOWED\":50," +//待跟进
							"\"FOLLOWING_UP\":10," +//跟进中
							"\"FOLLOW_UP_COMPLETED\":20," +//跟进结束

							"\"CALL_NEW_CUST\":20," +//新客资
							"\"CALL_AGAIN_NUM\":40," +//待再次沟通
							"\"CALL_TRANSFER_SUC\":10," +//转商机
							"\"CALL_INVALID\":10," +//无效
							"\"EFFECT_CUST_RATE\":\"30.0%\"," +//客资有效率

							"\"SUC_CUST_NUM\":50," +//接通客资量
							"\"SUC_CUST_RATE\":\"70.0%\"," +//客资接通率

							"\"CENTRAL_AIR_NUM\":20," +//中央空调有效客资量
							"\"CENTRAL_AIR_RATE\":\"10.0%\"," +//中央空调客资有效率

							"\"ARRIVE_CUST_NUM\":30," +//到店客资量
							"\"ARRIVE_CUST_RATE\":\"50%\"," +//客资到店率

							"\"CALL_TIME_NUM\":\"11:30:59\"" +//通话时长
							"},{" +
							"\"AGENT_NAME\":\"张澎湃\",\"AGENT_ACC\":\"zhangpp59\"," +

							"\"ALLOC_CUST_NUM\":50," + //分配客资量
							"\"OUTBOUND_NUM\":50," +//外呼量

							"\"TO_BE_FOLLOWED\":50," +//待跟进
							"\"FOLLOWING_UP\":10," +//跟进中
							"\"FOLLOW_UP_COMPLETED\":20," +//跟进结束

							"\"CALL_NEW_CUST\":20," +//新客资
							"\"CALL_AGAIN_NUM\":40," +//待再次沟通
							"\"CALL_TRANSFER_SUC\":10," +//转商机
							"\"CALL_INVALID\":10," +//无效
							"\"EFFECT_CUST_RATE\":\"30.0%\"," +//客资有效率

							"\"SUC_CUST_NUM\":50," +//接通客资量
							"\"SUC_CUST_RATE\":\"70.0%\"," +//客资接通率

							"\"CENTRAL_AIR_NUM\":20," +//中央空调有效客资量
							"\"CENTRAL_AIR_RATE\":\"10.0%\"," +//中央空调客资有效率

							"\"ARRIVE_CUST_NUM\":30," +//到店客资量
							"\"ARRIVE_CUST_RATE\":\"50%\"," +//客资到店率

							"\"CALL_TIME_NUM\":\"11:30:59\"" +//通话时长
							"}]";
					resultObj.put("data", JSON.parse(inputWord));*/

		}catch (Exception e){
			logger.error(e.getMessage(), e);
		}
		return EasyResult.fail();
	}

}
