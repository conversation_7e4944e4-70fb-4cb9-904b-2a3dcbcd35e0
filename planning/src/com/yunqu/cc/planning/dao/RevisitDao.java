package com.yunqu.cc.planning.dao;

import java.sql.SQLException;

import com.alibaba.fastjson.JSONArray;
import com.yunqu.cc.planning.inf.RevisitService;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.PrivacyUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.planning.base.AppDaoContext;
import com.yunqu.cc.planning.base.CommLogger;
import com.yunqu.cc.planning.base.Constants;
import com.yunqu.cc.planning.utils.StringUtil;

@WebObject(name = "revisit")
public class RevisitDao extends AppDaoContext {

	private Logger logger = CommLogger.logger;
	
	private static EasyQuery getmarQuery(){
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.FRAME_DS);
	 } 
	
	/**
	 * 回访数据查询
	 * @return
	 */
	@WebControl(name = "getRevisitList",type = Types.LIST)
	public JSONObject getRevistList() {

		//获取用户所属部门名称，对应部门名称拿到所属的字典数据对应的值
		JSONObject conditionJson = RevisitService.getTMallDataFilterConditions(this.request);

		EasySQL sql = new EasySQL("SELECT * FROM CC_PLANNING WHERE 1=1");
		sql.append(param.getString("REVISIT_STATUS"),"AND REVISIT_STATUS = ?");
		sql.append(param.getString("SYN_TIME_START"),"AND SYN_TIME >= ?");
		sql.append(param.getString("SYN_TIME_END"),"AND SYN_TIME <= ?");
		sql.append(param.getString("PUB_TIME_START"),"AND PUB_TIME >= ?");
		sql.append(param.getString("PUB_TIME_END"),"AND PUB_TIME <= ?");
		sql.appendLike(param.getString("TASK_NAME"),"AND TASK_NAME LIKE ?");
		sql.appendLike(param.getString("CUSTOMER_PHONE"),"AND CUSTOMER_PHONE LIKE ?");
		sql.append(param.getString("ORG_CODE"),"AND ORG_CODE = ?");
		sql.appendLike(param.getString("DEMAND_SIDE"),"AND DEMAND_SIDE LIKE ?");
		sql.append(param.getString("TASK_ID"),"AND TASK_ID = ?");
		if(conditionJson != null){
			sql.append(conditionJson.getString("dataSource"), "AND DATA_SOURCE = ? ");
			sql.append(conditionJson.getString("businessType"), "AND BUSINESS_TYPE = ? ");
		}

		sql.append("ORDER BY SYN_TIME DESC");
		logger.info("sql:"+sql.getSQL()+" | param:"+JSONObject.toJSONString(sql.getParams()));
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		JSONObject object = new JSONObject();
		object.put("CUSTOMER_PHONE", PrivacyUtil.phone);

		return PrivacyUtil.desensitization(result,object);
	}

	/**
	 * 回访处理列表
	 * @return
	 */
	@WebControl(name = "getHandleList",type = Types.LIST)
	public JSONObject getHandleList() {
		
		String userAcc = UserUtil.getUser(request).getUserAcc();
		int checkRole = StringUtil.getInt(param.getString("check"));

		JSONObject conditionJson = RevisitService.getTMallDataFilterConditions(this.request);

		EasySQL sql = new EasySQL("SELECT * FROM CC_PLANNING WHERE 1=1");
		sql.append(param.getString("REVISIT_STATUS"),"AND REVISIT_STATUS = ?");
		sql.append(param.getString("CONVERSION_TYPE"),"AND CONVERSION_TYPE = ?");
		sql.append(param.getString("SYN_TIME_START"),"AND SYN_TIME >= ?");
		sql.append(param.getString("SYN_TIME_END"),"AND SYN_TIME <= ?");
		sql.append(param.getString("IS_DONE"),"AND IS_DONE = ?");
		if(checkRole > 0) {
			sql.appendLike(param.getString("USER_ACC"),"AND RECEIVE_ACC LIKE ?");
		}else {
			sql.append(userAcc,"AND RECEIVE_ACC = ?");
		}
		sql.appendLike(param.getString("TASK_NAME"),"AND TASK_NAME LIKE ?");
		sql.appendLike(param.getString("CUSTOMER_PHONE"),"AND CUSTOMER_PHONE LIKE ?");
		sql.appendLike(param.getString("DEMAND_SIDE"),"AND DEMAND_SIDE LIKE ?");
		sql.append(param.getString("TASK_ID"),"AND TASK_ID = ?");
		if(conditionJson != null){
			sql.append(conditionJson.getString("dataSource"), "AND DATA_SOURCE = ? ");
			sql.append(conditionJson.getString("businessType"), "AND BUSINESS_TYPE = ? ");
		}
		sql.append(param.getString("CALL_STATUS"),"AND CALL_STATUS = ? ");
		sql.append(param.getString("REVISIT_RESULT"),"AND REVISIT_RESULT = ? ");
		sql.append(param.getString("FOLLOW_STATUS"),"AND FOLLOW_STATUS = ? ");
		sql.append("ORDER BY RECEIVE_TIME DESC,ID");
		logger.info("sql:"+sql.getSQL()+" | param:"+JSONObject.toJSONString(sql.getParams()));
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		JSONObject object = new JSONObject();
		object.put("CUSTOMER_PHONE", PrivacyUtil.phone);

		JSONObject data = PrivacyUtil.desensitization(result, object);
		data.put("check", checkRole);
		data.put("userAcc", userAcc);
		return data;
	}
	
	@WebControl(name = "checkRole",type = Types.RECORD)
	public JSONObject checkRole() {
		String userAcc = UserUtil.getUser(request).getUserAcc();
		int checkRole = StringUtil.getInt(param.getString("check"));
		if(checkRole == 0) {
			EasySQL checkSql = new EasySQL("SELECT COUNT(1) FROM EASI_ROLE_USER T1,EASI_ROLE_RES T2 WHERE 1=1");
			checkSql.append("AND T1.ROLE_ID = T2.ROLE_ID");
			checkSql.append("planning_revisit_monitor","AND RES_ID = ?");//权限id
			checkSql.append(userAcc,"AND T1.USER_ID = (SELECT USER_ID FROM EASI_USER_LOGIN WHERE USER_ACCT = ?)");
			try {
				checkRole = getmarQuery().queryForInt(checkSql.getSQL(), checkSql.getParams());
			} catch (SQLException e) {
				logger.error(CommonUtil.getClassNameAndMethod(this) + "查询权限出错：" + e.getMessage(),e);
				checkRole = -1;
			}
		}
		JSONObject data = new JSONObject();
		data.put("check", checkRole);
		data.put("userAcc", userAcc);
		return data;
	}
	
	/**
	 * 回访结果
	 * @return
	 */
	@WebControl(name = "getRevisitResultList",type = Types.LIST)
	public JSONObject getRevisitResultList() {
		int checkRole = StringUtil.getInt(param.getString("check"));
		EasySQL sql = new EasySQL("SELECT T1.*,T2.TASK_ID,T2.CUSTOMER_NAME,T2.CUSTOMER_PHONE,T2.TASK_NAME,T2.DEMAND_SIDE,T2.QUESTION_URL,T2.QUESTION_ID,t2.DATA_SOURCE,t2.CALL_STATUS FROM CC_PLANNING_RESULT T1 ");
		sql.append("LEFT JOIN CC_PLANNING T2 ON T1.C_PLANNING_ID = T2.ID WHERE 1=1");
		sql.append(param.getString("CREATE_ACC")," AND T1.CREATE_ACC = ?"); 
		sql.append(param.getString("CUSTOMER_PHONE")," AND T2.CUSTOMER_PHONE = ?");
		sql.append(param.getString("REVISIT_RESULT")," AND T1.REVISIT_RESULT = ?");
		sql.append(param.getString("REV_TIME_START"),"AND T1.CREATE_TIME >= ?");
		if(checkRole <= 0) {
			sql.append(UserUtil.getUser(request).getUserAcc(),"AND T1.CREATE_ACC = ?");			
		}
		sql.append(param.getString("REV_TIME_END"),"AND T1.CREATE_TIME <= ?");

		JSONObject conditionJson = RevisitService.getTMallDataFilterConditions(this.request);
		if(conditionJson != null){
			sql.append(conditionJson.getString("dataSource"), "AND T2.DATA_SOURCE = ? ");
			sql.append(conditionJson.getString("businessType"), "AND T2.BUSINESS_TYPE = ? ");
		}else{
			sql.append(param.getString("DATA_SOURCE"),"AND T2.DATA_SOURCE = ?");
		}

		sql.appendLike(param.getString("TASK_NAME"), " AND T2.TASK_NAME LIKE ? ");
		sql.append(param.getString("TASK_ID"),"AND T2.TASK_ID = ?");
		sql.append("ORDER BY T1.CREATE_TIME DESC");
		JSONObject result = queryForPageList(sql.getSQL(), sql.getParams());
		JSONObject object = new JSONObject();
		object.put("CUSTOMER_PHONE", PrivacyUtil.phone);

		return PrivacyUtil.desensitization(result, object);
	}

	/**
	 * 发布记录
	 */
	@WebControl(name = "getPublishList",type = Types.LIST)
	public JSONObject getPublishList() {

		JSONObject conditionJson = RevisitService.getTMallDataFilterConditions(this.request);

		EasySQL sql = new EasySQL("SELECT DISTINCT T1.*,T3.TASK_ID,(SELECT COUNT(*) FROM CC_PLANNING_APPLY_USER T2 WHERE T2.C_PLANNING_PUB_ID=T1.ID) AS APPLY_NUM ");
		sql.append("FROM CC_PLANNING_PUBLISH T1 left join CC_PLANNING T3 on T3.C_PLANNING_PUB_ID = T1.ID WHERE 1=1 ");
		sql.append(param.getString("TASK_ID"),"AND T3.TASK_ID = ?");
		sql.append(param.getString("CONVERSION_TYPE"),"AND T1.CONVERSION_TYPE = ?");
		sql.append(param.getString("PUB_TIME_START"),"AND T1.PUB_TIME >= ?");
		sql.append(param.getString("PUB_TIME_END"),"AND T1.PUB_TIME <= ?");
		sql.appendLike(param.getString("TASK_NAME"),"AND T1.TASK_NAME LIKE ?");
		sql.appendLike(param.getString("PUB_ACC"),"AND T1.PUB_ACC LIKE ?");
		if(conditionJson != null){
			sql.append(conditionJson.getString("dataSource"), "AND T3.DATA_SOURCE = ? ");
			sql.append(conditionJson.getString("businessType"), "AND T3.BUSINESS_TYPE = ? ");
		}
		sql.append("ORDER BY T1.PUB_TIME DESC");
		logger.info("sql:"+sql.getSQL()+" | param:"+JSONObject.toJSONString(sql.getParams()));
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 申请记录
	 * @return
	 */
	@WebControl(name = "getApplyList",type = Types.LIST)
	public JSONObject getApplyList() {
		EasySQL sql = new EasySQL("SELECT * FROM CC_PLANNING_APPLY_USER WHERE 1=1")
		.append(param.getString("id1"),"AND C_PLANNING_PUB_ID = ? ORDER BY REVISIT_NUM");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 分配记录
	 * @return
	 */
	@WebControl(name = "getDisList",type = Types.LIST)
	public JSONObject getDisList() {
		EasySQL sql = new EasySQL("SELECT * FROM CC_PLANNING_RECEIVE_USER WHERE 1=1");
		sql.append(param.getString("id2"),"AND C_PLANNING_PUB_ID = ? ORDER BY CYCLE_NUM DESC");
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
	
	/**
	 * 获取回访数据详情
	 */
	@WebControl(name = "getRevisitInfo",type = Types.RECORD)
	public JSONObject getRevisitInfo() {
		if(StringUtils.isNotBlank(param.getString("resultId"))){
			EasySQL sql = new EasySQL("SELECT T1.*,T2.CUSTOMER_PHONE,T2.CUSTOMER_NAME,T2.ORG_CODE,T2.QUESTION_URL,T2.DATA_SOURCE,T2.USER_INFO_ID,T2.USER_INFO_URL,T3.COEFFICIENT,T2.REMARKS,T2.CONTENT ");
			sql.append("FROM CC_PLANNING_RESULT T1");
			sql.append("LEFT JOIN CC_PLANNING T2 ON T1.C_PLANNING_ID=T2.ID");
			sql.append("LEFT JOIN C_NO_CONVERT T3 ON T2.CONVERSION_TYPE = T3.ID");
			sql.append(param.getString("resultId"),"WHERE T1.ID = ?",false);
			return queryForRecord(sql.getSQL(),sql.getParams(),null);
		}else{
			EasySQL sql = new EasySQL("SELECT T1.CUSTOMER_PHONE,T1.CUSTOMER_NAME,T1.ORG_CODE,T1.QUESTION_URL," +
					"T1.DATA_SOURCE,T1.USER_INFO_ID,T1.USER_INFO_URL,T2.COEFFICIENT,T1.REMARKS,T1.CONTENT ");
			sql.append("FROM CC_PLANNING T1");
			sql.append("LEFT JOIN C_NO_CONVERT T2 ON T1.CONVERSION_TYPE = T2.ID");
			sql.append(param.getString("toHandle.pId"),"WHERE T1.ID = ?",false);
			return queryForRecord(sql.getSQL(),sql.getParams(),null);
		}
	}

	/**
	 * 企划通报表
	 */
	@WebControl(name = "getPlanningReport",type = Types.LIST)
	public JSONObject getPlanningReport() {
		JSONObject paramObj = this.param;
		JSONObject conditionJson = RevisitService.getTMallDataFilterConditions(this.request);
		if(conditionJson != null){
			paramObj.put("DATA_SOURCE", conditionJson.getString("dataSource"));
			paramObj.put("BUSINESS_TYPE", conditionJson.getString("businessType"));
		}
		logger.info("企划通回访报表--查询参数：paramObj:"+paramObj.toJSONString());
		RevisitService revisitService = new RevisitService();
		EasySQL sql = revisitService.getPlanningReportSql(paramObj);
		logger.info("企划通回访报表--查询sql:"+sql.getSQL()+" | param:"+JSONObject.toJSONString(sql.getParams()));
		JSONObject queryPageInfo = queryForPageList(sql.getSQL(), sql.getParams());

		if(queryPageInfo==null || queryPageInfo.isEmpty()){
			return queryPageInfo;
		}
		logger.info("queryPageInfo："+JSONObject.toJSONString(queryPageInfo));
		JSONArray dataArr = queryPageInfo.getJSONArray("data");
		if(!dataArr.isEmpty()){
			logger.info("dataArr:"+JSONObject.toJSONString(dataArr));
			for (int i = 0; i < dataArr.size(); i++) {
				JSONObject dataItem = dataArr.getJSONObject(i);
				revisitService.getPlanningReportRate(dataItem);

				dataItem.put("VISIT_NUM", dataItem.getIntValue("VISIT_NUM"));
				dataItem.put("VISIT_RATE", dataItem.getString("VISIT_RATE"));
				dataItem.put("ANSWER_RATE", dataItem.getString("ANSWER_RATE"));
			}
		}
		return queryPageInfo;
	}

	/**
	 * 企划通回访发布概览--查询sql:
	 */
	@WebControl(name = "getPlanningVisitOverview",type = Types.LIST)
	public JSONObject getPlanningVisitOverview() {
		String synTimeStart = param.getString("SYN_TIME_START");
		String synTimeEnd = param.getString("SYN_TIME_END");
		if(StringUtils.isBlank(synTimeStart) || StringUtils.isBlank(synTimeEnd)){
			return EasyResult.fail("同步时间不能为空");
		}

		EasySQL sql = new EasySQL("SELECT t1.TASK_ID, t1.TASK_NAME, t1.DEMAND_SIDE, t1.DEMAND_SIDE_DEPT, COUNT(1) AS TOTAL,"
				+ "SUM(CASE WHEN t1.REVISIT_STATUS = '1' THEN 1 ELSE 0 END) AS NO_PUBLISH, "
				+ "SUM(CASE WHEN t1.REVISIT_STATUS = '2' THEN 1 ELSE 0 END) AS HAS_PUBLISHED, "
				+ "SUM(CASE WHEN t1.REVISIT_STATUS = '3' THEN 1 ELSE 0 END) AS HAS_RECEIVED, "
				+ "SUM(CASE WHEN t1.REVISIT_STATUS = '4' THEN 1 ELSE 0 END) AS HAS_REVISITED "
				+ "FROM CC_PLANNING t1 WHERE 1=1");

		sql.append(param.getString("SYN_TIME_START"),"AND t1.SYN_TIME >= ?");
		sql.append(param.getString("SYN_TIME_END"),"AND t1.SYN_TIME <= ?");

		sql.append(param.getString("TASK_ID"),"AND t1.TASK_ID = ?");
		sql.appendLike(param.getString("TASK_NAME"),"AND t1.TASK_NAME LIKE ?");
		sql.appendLike(param.getString("DEMAND_SIDE"),"AND t1.DEMAND_SIDE LIKE ?");

		JSONObject conditionJson = RevisitService.getTMallDataFilterConditions(this.request);
		if(conditionJson != null){
			sql.append(conditionJson.getString("dataSource"), "AND DATA_SOURCE = ? ");
			sql.append(conditionJson.getString("businessType"), "AND BUSINESS_TYPE = ? ");
		}
		sql.append("GROUP BY TASK_ID,TASK_NAME,DEMAND_SIDE,DEMAND_SIDE_DEPT ");
		logger.info("企划通回访发布概览查询-->sql:"+sql.getSQL()+" | param:"+JSONObject.toJSONString(sql.getParams()));
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 任务包名称查询接口
	 */
	@WebControl(name = "getPlanningTaskNameQuery",type = Types.LIST)
	public JSONObject getPlanningTaskNameQuery() {
		EasySQL sql = new EasySQL("SELECT t1.ID, t1.TASK_ID, t1.TASK_NAME "
				+ "FROM CC_PLANNING_TASK_INFO t1 WHERE 1=1 AND t1.TASK_ID IS NOT NULL");

		sql.appendLike(param.getString("TASK_NAME"),"AND t1.TASK_NAME LIKE ?");

		JSONObject conditionJson = RevisitService.getTMallDataFilterConditions(this.request);
		if(conditionJson != null){
			sql.append(conditionJson.getString("dataSource"), "AND DATA_SOURCE = ? ");
			sql.append(conditionJson.getString("businessType"), "AND BUSINESS_TYPE = ? ");
		}
		logger.info("任务包名称查询接口-->sql:"+sql.getSQL()+" | param:"+JSONObject.toJSONString(sql.getParams()));
		return queryForPageList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 需求方查询接口
	 */
	@WebControl(name = "getPlanningDemandSideQuery",type = Types.LIST)
	public JSONObject getPlanningDemandSideQuery() {
		EasySQL sql = new EasySQL("SELECT DISTINCT t1.DEMAND_SIDE, t1.DEMAND_SIDE AS DEMAND_VALUE "
				+ "FROM CC_PLANNING_TASK_INFO t1 WHERE 1=1 AND t1.DEMAND_SIDE IS NOT NULL");

		sql.append(param.getString("TASK_ID"),"AND t1.TASK_ID = ?", false);
		sql.appendLike(param.getString("DEMAND_SIDE"),"AND t1.DEMAND_SIDE LIKE ?");

		/*JSONObject conditionJson = RevisitService.getDataFilterConditions(this.request);
		if(conditionJson != null){
			sql.append(conditionJson.getString("dataSource"), "AND DATA_SOURCE = ? ");
			sql.append(conditionJson.getString("businessType"), "AND BUSINESS_TYPE = ? ");
		}*/

		logger.info("需求方查询接口-->sql:"+sql.getSQL()+" | param:"+JSONObject.toJSONString(sql.getParams()));
		return queryForPageList(sql.getSQL(), sql.getParams());
	}
}
