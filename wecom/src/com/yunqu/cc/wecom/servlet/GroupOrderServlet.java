package com.yunqu.cc.wecom.servlet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.wecom.base.AppBaseServlet;
import com.yunqu.cc.wecom.base.CommonLogger;
import com.yunqu.cc.wecom.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;
import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@WebServlet("/servlet/GroupOrders")
public class GroupOrderServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;
    // 日志
    private Logger logger = CommonLogger.logger;
    // yw_db数据源
    private static final EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YW);

    protected EasyCache cache = CacheManager.getMemcache();

    /**
     * 提交群工单
     */
    public JSONObject actionForSubmit(){
        EasyQuery easyQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YW);
        JSONObject data = this.getJSONObject();
        logger.info("工单提交参数:" + data);
        EasyRecord record = new EasyRecord("C_NO_GROUP_ORDERS", "ID");
        EasyRecord checkRecord = new EasyRecord("C_NO_GROUP_ORDERS_CHECK", "ID");
        EasyRecord productRecord = new EasyRecord("C_NO_GROUP_ORDERS_PRODUCT", "ID");
        //参数判空
        if ("".equals(data.getString("chatId"))){
            return EasyResult.error(500, "无法获取群聊id,无法提交工单");
        }
        if (data.getJSONArray("productList").size() < 1){ //防止数组下标越界
            return EasyResult.error(500, "请选择对应的产品信息");
        }
        //增加缓存限制 五秒内只能提交一次 防止极端情况出现两次工单
        String chatId = data.getString("chatId");
        String submitKey = Constants.WECOM_SUBMIT_KEY + "_" + chatId;
        String value = cache.get(submitKey);
        //生成随机数
        String randomKey = RandomKit.randomStr();
        logger.info("生成的缓存随机key为：" + randomKey);
        cache.put(submitKey, randomKey, 5); //更新sendKey的值, 并设置5s过期时间
        if (!(value == null || value.isEmpty())) {//存在Key则不允许操作
            logger.info("提交群工单过于频繁，请稍等在提交");
            return EasyResult.error(500, "提交群工单过于频繁，请稍等在提交");
        }
        String id = data.getString("ID");//出现回滚异常 坐席再次提交拿不到工单id
        String orderId = "";
        //是否更新稽查状态 true 表示需要更新稽查状态
        boolean isUpdateCheck = true;
        //稽查时间标识 true 表示今天有稽查记录
        boolean checkTimeFlag = false;
        //稽查时间
        String checkTime = data.getString("CHECK_TIME");
        try {
            easyQuery.begin();
            //根据主键判断 新增/优化
            if ("".equals(id)) {
                orderId = RandomKit.uniqueStr();
                record.put("ID", orderId);
                record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
                record.put("CREATE_USER", this.getUserPrincipal().getUserInfo().getUserAcct());
                //同时新增C_NO_GROUP_ORDERS_CHECK表
                checkRecord.put("ID", RandomKit.uniqueStr());
                checkRecord.put("GROUP_ORDER_ID", orderId);
                checkRecord.put("CREATE_TIME", DateUtil.getCurrentDateStr());
                checkRecord.put("CREATE_USER", this.getUserPrincipal().getUserInfo().getUserAcct());
            } else {
                orderId = id; //赋值工单ID
                record.put("ID", id);
                record.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
                record.put("UPDATE_USER", this.getUserPrincipal().getUserInfo().getUserAcct());
                //判断是否更新稽查状态
                isUpdateCheck = getIsUpdateCheck(orderId, checkTime);

                //有提交稽查时间在更新稽查表
                if (!"".equals(checkTime)){
                    checkTimeFlag = getLatestCheckTime(id);
                    if (checkTimeFlag) {
                        //今天有稽查记录 同时更新C_NO_GROUP_ORDERS_CHECK表
                        checkRecord.put("ID",getGroupOrderCheckId(id)); //根据工单的id去获取稽查表主键id
                        checkRecord.put("GROUP_ORDER_ID", id);
                        checkRecord.put("UPDATE_TIME", DateUtil.getCurrentDateStr());
                        checkRecord.put("UPDATE_USER", this.getUserPrincipal().getUserInfo().getUserAcct());
                    }else {
                        //没有稽查记录 新增C_NO_GROUP_ORDERS_CHECK表
                        checkRecord.put("ID", RandomKit.uniqueStr());
                        checkRecord.put("GROUP_ORDER_ID", id);
                        checkRecord.put("CREATE_TIME", DateUtil.getCurrentDateStr());
                        checkRecord.put("CREATE_USER", this.getUserPrincipal().getUserInfo().getUserAcct());
                    }
                }

                //商品表的更新 先删除所有记录 在更新
                easyQuery.execute("DELETE FROM C_NO_GROUP_ORDERS_PRODUCT  WHERE GROUP_ORDER_ID=?", id);
            }
            //同时新增C_NO_GROUP_ORDERS_PRODUCT表
            productRecord.put("GROUP_ORDER_ID", orderId);
            productRecord.put("CHAT_ID", data.getString("chatId"));
            productRecord.put("CREATE_TIME", DateUtil.getCurrentDateStr());
            productRecord.put("CREATE_USER", this.getUserPrincipal().getUserInfo().getUserAcct());

            //C_NO_GROUP_ORDERS 数据库操作
            record.put("CHAT_ID", data.getString("chatId"));
            record.put("GROUP_CHAT_NAME", data.getString("GROUP_CHAT_NAME"));
            record.put("GROUP_CREATE_TIME", data.getString("GROUP_CREATE_TIME"));
            record.put("GROUP_CREATE_STEWARD", data.getString("GROUP_CREATE_STEWARD"));
            record.put("GROUP_CREATE_ACC", data.getString("GROUP_CREATE_ACC"));
            record.put("SITE_CODE", data.getString("SITE_CODE"));
            record.put("SITE_NAME", data.getString("SITE_NAME"));
            record.put("SITE_CONTACT", data.getString("SITE_CONTACT"));
            record.put("AREA_CODE", data.getString("AREA_CODE"));
            record.put("USER_NAME", data.getString("USER_NAME"));
            record.put("USER_PHONE", data.getString("USER_PHONE"));
            record.put("USER_ADDRESS", data.getString("USER_ADDRESS"));
            record.put("SALE_UNIT", data.getString("SALE_UNIT"));
            record.put("SALE_PEOPLE", data.getString("SALE_PEOPLE"));
            record.put("SALE_PHONE", data.getString("SALE_PHONE"));
            record.put("BACKUP", data.getString("BACKUP"));
            record.put("ORDER_SOURCE", data.getString("ORDER_SOURCE"));
            record.put("ORDER_STATE", data.getString("ORDER_STATE"));
            record.put("IS_NORMS", data.getString("IS_NORMS"));
            record.put("REASON", data.getString("REASON"));
            record.put("GROUP_STATE", data.getString("GROUP_STATE"));
            record.put("ORG_CODE", data.getJSONArray("productList").getJSONObject(0).getString("orgCode")); //主体
            record.put("BRAND_CODE", data.getJSONArray("productList").getJSONObject(0).getString("brandCode")); //品牌编码
            record.put("BRAND_NAME", data.getJSONArray("productList").getJSONObject(0).getString("brandName")); //品牌
            record.put("PROD_CODE", data.getJSONArray("productList").getJSONObject(0).getString("prodCode")); //品类编码
            record.put("PROD_NAME", data.getJSONArray("productList").getJSONObject(0).getString("prodName")); //品类
            record.put("PRODUCT_MODEL", data.getJSONArray("productList").getJSONObject(0).getString("productModel")); //产品
            record.put("PRODUCT_CODE", data.getJSONArray("productList").getJSONObject(0).getString("productCode")); //产品编码
            record.put("ENT_ID", "1000");
            record.put("SERVICE_ORDER_NUMBER", data.getString("SERVICE_ORDER_NUMBER"));
            if (isUpdateCheck){
                record.put("CHECK_TIME", data.getString("CHECK_TIME"));
                record.put("CHECK_NAME", data.getString("CHECK_NAME"));
            }

            //新增或更新数据
            if ("".equals(id)) {
                easyQuery.save(record);
            } else {
                easyQuery.update(record);
            }

            //有稽查时间在更新 C_NO_GROUP_ORDERS_CHECK表
            if (!"".equals(checkTime)){
                //C_NO_GROUP_ORDERS_CHECK 数据库操作
                checkRecord.put("CHAT_ID", data.getString("chatId"));
                checkRecord.put("GROUP_CHAT_NAME", data.getString("GROUP_CHAT_NAME"));
                checkRecord.put("ORDER_STATE", data.getString("ORDER_STATE"));
                checkRecord.put("IS_NORMS", data.getString("IS_NORMS"));
                checkRecord.put("REASON", data.getString("REASON"));
                checkRecord.put("GROUP_STATE", data.getString("GROUP_STATE"));
                if (isUpdateCheck){
                    checkRecord.put("CHECK_TIME", data.getString("CHECK_TIME"));
                    checkRecord.put("CHECK_NAME", data.getString("CHECK_NAME"));
                }
                if ("".equals(id)){
                    easyQuery.save(checkRecord);
                }else {
                    if (isUpdateCheck){
                        if (checkTimeFlag){
                            easyQuery.update(checkRecord);
                        }else {
                            easyQuery.save(checkRecord);
                        }
                    }
                }
            }

            //C_NO_GROUP_ORDERS_PRODUCT表数据库操作
            JSONArray productList = data.getJSONArray("productList");
            if(productList!=null) {
                for (int j = 0; j < productList.size(); j++) {
                    JSONObject product = productList.getJSONObject(j);
                    productRecord.put("ID", RandomKit.uniqueStr());
                    productRecord.put("ORG_CODE", product.getString("orgCode")); //主体
                    productRecord.put("BRAND_CODE", product.getString("brandCode")); //品牌编码
                    productRecord.put("BRAND_NAME", product.getString("brandName")); //品牌
                    productRecord.put("PROD_CODE", product.getString("prodCode")); //品类编码
                    productRecord.put("PROD_NAME", product.getString("prodName")); //品类
                    productRecord.put("PRODUCT_MODEL", product.getString("productModel")); //产品
                    productRecord.put("PRODUCT_CODE", product.getString("productCode")); //产品编码
                    easyQuery.save(productRecord);
                }
            }
            easyQuery.commit();
        } catch (Exception e) {
            try {
                easyQuery.roolback();//回滚出现异常 会导致数据入库两次
            } catch (SQLException e1) {
                logger.error(CommonUtil.getClassNameAndMethod(this)+ "回滚出现异常:"+ e1.getMessage(), e1);
            }
            logger.error(CommonUtil.getClassNameAndMethod(this)+ "群工单提交出现异常:"+ e.getMessage(), e);
            return EasyResult.error(500, "群工单保存失败");
        }
        //调用提醒接口
        UserModel model=UserUtil.getUser(getRequest());
        String groupChatName = data.getString("GROUP_CHAT_NAME");
        String remindTime = data.getString("REMIND_TIME");
        logger.info("当前群聊:" + groupChatName + "添加提醒时间:" + remindTime);
        if (StringUtils.isNotBlank(remindTime)) {
            notice(model,remindTime,groupChatName);
        }
        return EasyResult.ok();
    }


    /**
     * 根据群工单Id获取群工单稽查表id
     * @param orderId
     * @return
     * @throws SQLException
     */
    public String getGroupOrderCheckId(String orderId) {
        EasySQL sql = new EasySQL("SELECT ID FROM C_NO_GROUP_ORDERS_CHECK");
        sql.append(orderId," WHERE GROUP_ORDER_ID = ?");
        sql.append("ORDER BY CREATE_TIME DESC");
        String id = null;
        try {
            id = query.queryForString(sql.getSQL(), sql.getParams());
        } catch (SQLException e) {
            logger.info("根据群工单Id获取群工单稽查表id出现异常:" + e.getMessage());
        }
        return id;
    }

    /**
     * 根据稽查时间 判断是否更新稽查状态
     * checkTime与time相同，则不更新
     * checkTime与time不同，则更新
     * @param orderId
     * @return
     */
    public boolean getIsUpdateCheck(String orderId,String checkTime){
        EasySQL sql = new EasySQL("SELECT CHECK_TIME FROM C_NO_GROUP_ORDERS_CHECK");
        sql.append(orderId," WHERE GROUP_ORDER_ID = ? ");
        String time = "";
        try {
            time = query.queryForString(sql.getSQL(), sql.getParams());
        } catch (SQLException e) {
            logger.info("根据群工单Id获取对应群工单的最新稽查时间出现异常:" + e.getMessage());
        }
        logger.info("获取当前群聊："+ orderId + "的最新稽查时间：" + time);
        return !StringUtils.equals(time,checkTime);
    }

    /**
     * 根据群工单Id获取对应群工单的最新稽查时间 并判断是否为今天
     * @param orderId
     * @return
     */
    public boolean getLatestCheckTime(String orderId){
        String currentTime = DateUtil.getCurrentDateStr("yyyy-MM-dd");
        String startTime = currentTime + " 00:00:00";
        String endTime = currentTime + " 23:59:59";
        EasySQL sql = new EasySQL("SELECT CHECK_TIME FROM C_NO_GROUP_ORDERS_CHECK");
        sql.append(orderId," WHERE GROUP_ORDER_ID = ? ");
        sql.append(startTime,"and CREATE_TIME >= ?");
        sql.append(endTime,"and CREATE_TIME <= ?");
        String time = "";
        try {
            time = query.queryForString(sql.getSQL(), sql.getParams());
        } catch (SQLException e) {
            logger.info("根据群工单Id获取对应群工单的最新稽查时间出现异常:" + e.getMessage());
        }
        logger.info("获取当前群聊："+ orderId + "的最新稽查时间：" + time);
        return StringUtils.isNotBlank(time);
    }

    /**
     * 提交工单时弹屏通知
     *
     */
    public void notice(UserModel model,String remindTime,String groupChatName){
        JSONObject request = new JSONObject();
        try {
            logger.info("群工单提醒通知入库");
            IService service = ServiceContext.getService(ServiceID.NOTICE_INTERFACE);
            JSONObject json= new JSONObject();
            json.put("sender", "notice");
            json.put("password", "YQ_85521717");
            json.put("serialId", "");
            json.put("command", ServiceCommand.NOTICE_ADD_USER_NOTICE); // serviceId:  ServiceID.NOTICE_INTERFACE;
            json.put("receiverType", "01");//接收类型 01-个人  02-部门   03-所有人
            json.put("userAcc",model.getUserAcc());//接收通知的人员，如有多个，用;隔开
            json.put("deptCode", model.getDeptCode());
            json.put("createUserAcc", model.getUserAcc());
            json.put("createUserDeptCode",model.getDeptCode());
            json.put("type","群聊:" + groupChatName + ",你有一条群工单稽查通知");
            json.put("title", "群聊:" + groupChatName + ",你有一条群工单稽查通知");
            json.put("content","群聊:" + groupChatName + ",你有一条群工单稽查通知");
            json.put("module", Constants.APP_NAME);
            json.put("url","/");
            //添加提醒时间
            json.put("remindTime",remindTime);
            JSONObject data = service.invoke(json);
            logger.info("分配"+model.getUserAcc()+"通知返回结果:"+ data+ ",参数:"+ json);
        } catch (ServiceException e) {
            logger.error("IService请求失败,请求参数"+JSON.toJSONString(request)+",原因"+e.getMessage());
        }
    }

    public static void main(String[] args) {
        String time = "";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 解析时间字符串
        LocalDateTime parsedTime = LocalDateTime.parse(time, formatter);

        // 获取今天的日期和时间
        LocalDateTime today = LocalDateTime.now();
        // 比较年、月、日是否相同
        boolean isToday = parsedTime.toLocalDate().equals(today.toLocalDate());
        System.out.println(isToday);
    }


}
