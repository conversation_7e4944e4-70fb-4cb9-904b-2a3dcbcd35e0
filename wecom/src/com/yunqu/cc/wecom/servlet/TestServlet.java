/**
 * 
 */
package com.yunqu.cc.wecom.servlet;

import javax.servlet.annotation.WebServlet;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.wecom.base.AppBaseServlet;

/**
 * <AUTHOR>
 *
 */

@WebServlet("/servlet/TestServlet")
public class TestServlet extends AppBaseServlet{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public void actionForTest(){
		try {
			String startDate = getPara("startDate");
			JSONObject params = new JSONObject();
			params.put("command", "syncWechatUserInfo");
			if(StringUtils.isNotBlank(startDate)){
				params.put("startDate", startDate+" 00:00:00");
				params.put("endDate", startDate+" 23:59:59");
			}
			IService service = ServiceContext.getService("CC_WECOM_COMMON_INTERFACE");
			JSONObject invoke = service.invoke(params);
		} catch (ServiceException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	

}
