package com.yunqu.cc.wecom.servlet;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.utils.kit.RandomKit;

import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.wecom.base.AppBaseServlet;
import com.yunqu.cc.wecom.base.CommonLogger;
import com.yunqu.cc.wecom.base.Constants;

/**
 * <AUTHOR> ：tby
 * @version 创建时间：2022年5月6日 下午4:04:59
 */
public class IndexServlet extends AppBaseServlet {

	private static final long serialVersionUID = 1L;

	private static Logger logger = CommonLogger.getLogger();


	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		this.doPost(req, resp);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		CommonLogger.logger.info("==================进入");
		UserModel user = UserUtil.getUser(req);
		if (user == null) {
			//未获取到用户信息，重定向到登录页面
			CommonLogger.logger.info("==================未获取到用户信息，请求转发至登录页面");
			login(req, resp);
			return;
		} else {
			CommonLogger.logger.info("==================获取到用户信息" + user.getUserAcc() + "，重定向至工单页面");
			//已经登录
			String oauth2URL = Constants.WECOM_OAUTH2_URL;
			logger.info("==================oauth2URL1:" + oauth2URL);
			String redirectURI = getRedirectURI(req, user);
			logger.info("==================redirectURI:" + redirectURI);
			oauth2URL = oauth2URL.replace("CORPID", Constants.WECOM_CORPID)
					.replace("REDIRECT_URI", URLEncoder.encode(redirectURI, "UTF-8"));
			logger.info("==================oauth2URL2:" + oauth2URL);
			resp.sendRedirect(oauth2URL);

		}
	}

	/**
	 * 根据请求参数获取重定向URI
	 *
	 * @param req
	 * @param user
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	private String getRedirectURI(HttpServletRequest req, UserModel user) throws UnsupportedEncodingException {
		String type = req.getParameter("type");
		String chatGroupId = req.getParameter("chatGroupId");
		String redirectURI;

		if ("jump".equals(type)) {
			// 跳转到jumpPage.html页面
			redirectURI = Constants.WECOM_LOGIN_REDIRECT.replace("index.html", "jumpPage.html");
			redirectURI = redirectURI + "?chatGroupId=" + chatGroupId  + "&u=" + user.getUserAcc();
			CommonLogger.logger.info("==================type=jump，重定向至jumpPage页面：" + redirectURI);
		} else {
			// 默认跳转到index.html页面
			redirectURI = Constants.WECOM_LOGIN_REDIRECT + "?u=" + user.getUserAcc();
			CommonLogger.logger.info("==================默认重定向至index页面：" + redirectURI);
		}

		return redirectURI;
	}

	/**
	 * 转发到登录页面
	 *
	 * @param req
	 * @param resp
	 */
	private void login(HttpServletRequest req, HttpServletResponse resp) {
		try {
			String SSOLoginFlag = ConfigUtil.getString("iccportal5", "IS_OPEN_SSO");
			String CX_SSO_LOGIN_URL = ConfigUtil.getString("iccportal5", "CX_SSO_LOGIN_URL");

			// 根据type参数确定登录成功后的重定向地址
			String type = req.getParameter("type");
			String redirectURI;
			if ("jump".equals(type)) {
				redirectURI = Constants.WECOM_LOGIN_REDIRECT.replace("index.html", "jumpPage.html");
				String chatGroupId = req.getParameter("chatGroupId");
				redirectURI = redirectURI + "?chatGroupId=" + chatGroupId;
				CommonLogger.logger.info("==================登录成功后将重定向至jumpPage页面：" + redirectURI);
			} else {
				redirectURI = Constants.WECOM_LOGIN_REDIRECT;
				CommonLogger.logger.info("==================登录成功后将重定向至index页面：" + redirectURI);
			}
			logger.info("==================redirectURI:" + redirectURI);

			if (StringUtils.equals("Y", SSOLoginFlag)) {
				String redirectURIEncoded = URLEncoder.encode(redirectURI, "UTF-8");
				logger.info("==================redirectURI-UTF-8:" + redirectURIEncoded);
				String state = "wecom" + RandomKit.randomStr();
				CacheManager.getMemcache().put(state, redirectURIEncoded);
				String url = CX_SSO_LOGIN_URL + "?state=" + state;
				CommonLogger.logger.info("==================重定向地址：" + url);
				resp.sendRedirect(url);
			}else {
				req.setAttribute("j_url", redirectURI);
				req.getRequestDispatcher("/login.jsp").forward(req, resp);
			}
		} catch (ServletException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

}
