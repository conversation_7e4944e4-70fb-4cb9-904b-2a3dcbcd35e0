package com.yunqu.cc.wecom.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yunqu.cc.wecom.base.Constants;

@WebListener
public class InterfaceListener extends ServiceContextListener {

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		
		
		ServiceResource resource = new ServiceResource();
		resource.appName = Constants.APP_NAME;
		resource.className = "com.yunqu.cc.wecom.inf.CommonService"	;
		resource.description = "通用服务调用入口";
		resource.serviceId = "CC_WECOM_COMMON_INTERFACE";
		resource.serviceName = "通用服务";
		list.add(resource);
		
		ServiceResource resource1 = new ServiceResource();
		resource1.appName = Constants.APP_NAME;
		resource1.className = "com.yunqu.cc.wecom.inf.MessageService";
		resource1.description = "消息汇总服务";
		resource1.serviceId = "CC_WECOM_MESSAGE_INTERFACE";
		resource1.serviceName = "消息汇总服务";
		list.add(resource1);

		ServiceResource resource2 = new ServiceResource();
		resource2.appName = Constants.APP_NAME;
		resource2.className = "com.yunqu.cc.wecom.inf.WecomService";
		resource2.description = "企业微信外部服务,提供外部接口查询";
		resource2.serviceId = "CC_WECOM_OTHER_INTERFACE";
		resource2.serviceName = "企业微信外部通用服务,提供外部接口查询";
		list.add(resource2);

		return list;

	}
}
