package com.yunqu.cc.wecom.base;

import org.easitline.common.core.dao.DaoContext;
/**
 * dao base class
 *
 */
public class AppDaoContext extends DaoContext {

	@Override
	protected String getAppDatasourceName() {
		return Constants.DS_YW;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}

	/**
	 * 重写loginCheck兼容apicall中的查询方法不做鉴权校验
	 */
	@Override
	protected boolean loginCheck() {
		String requestURI = this.request.getRequestURI();
		if (requestURI.contains("apicall") || requestURI.contains("openServlet")) {
			return false;
		}
		return true;
	}

}
