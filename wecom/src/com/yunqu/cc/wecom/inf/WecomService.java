package com.yunqu.cc.wecom.inf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.wecom.base.CommonLogger;
import com.yunqu.cc.wecom.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.ArrayList;

public class WecomService extends IService {

    private static Logger logger = CommonLogger.getLogger("uint");

    private EasyQuery easyQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_YW);

    public static EasyCache cache = CacheManager.getMemcache();


    @Override
    public JSONObject invoke(JSONObject params) throws ServiceException {
        String command = params.getString("command");
        if ("getGroupOrderByServiceOrder".equals(command)) {
            return getGroupOrderByServiceOrder(params.getJSONObject("data"));
        } else if ("getGroupMesByChatId".equals(command)) {
            return getGroupMesByChatId(params.getJSONObject("data"));
        } else {
            JSONObject result = new JSONObject();
            result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
            result.put("respDesc", "不存在的command,请检查！");
            return result;
        }
    }

    /**
     * 根据服务单查询群工单基础信息接口
     */
    private JSONObject getGroupOrderByServiceOrder(JSONObject data) {
        String serviceOrder = data.getString("serviceOrder");
        if (StringUtils.isBlank(serviceOrder)) {
            return EasyResult.fail("serviceOrder参数不能为空");
        }
        //todo

        return null;
    }


    /**
     * 根据群ID查询群聊天记录接口
     */
    private JSONObject getGroupMesByChatId(JSONObject data) {
        //群ID
        String sessionId = data.getString("sessionId");
        String pageIndex = data.getString("pageIndex");
        String pageSize = data.getString("pageSize");
        String pageType = data.getString("pageType");
        //消息发送开始时间
        String getSendStartDate = data.getString("getSendStartDate");
        //消息发送结束时间
        String getSendEndDate = data.getString("getSendEndDate");
        if (StringUtils.isAnyBlank(sessionId, pageIndex, pageSize, pageType, getSendStartDate, getSendEndDate)) {
            return EasyResult.fail("必填参数不能为空");
        }
        return queryQWGroupMes(data, easyQuery);
    }

    public static JSONObject queryQWGroupMes(JSONObject data, EasyQuery easyQuery) {
        EasySQL sql = new EasySQL("");
        sql.append("select DISTINCT t2.MSG_ID as RECORD_DETAIL_ID, t2.MSG_TIME as BEGIN_TIME, t2.MSG_CONTENT CONTENT, '' as WITHDRAW,");
        sql.append("(CASE WHEN AGENT_ACC is null   THEN '02' else '01' END) as USER_TYPE, ");
        sql.append("t2.AGENT_ACC,'' as channel_id, ");
        //20221018 暂时新增"emotion"企微表情类型 视为图片类型、新增voice，类型暂时用07
        sql.append("(CASE t2.MSG_TYPE WHEN 'text' THEN '01'  WHEN 'image' THEN '02'  WHEN 'video' THEN '06'  WHEN 'file' THEN '09'  WHEN 'emotion' THEN '02' END) TYPE,");
        sql.append("t2.AGENT_ACC as AGENT_NAME,(CASE WHEN USER_NAME is not null   THEN USER_NAME else  SENDER END)  as CUSTOMER_NAME ");
        sql.append("from C_NO_WECOM_GROUP_RECORD t2   LEFT JOIN C_NO_WECOM_GROUP_USER_INFO t3 ON t2.ROOM_ID  = t3 .WECHAT_GROUP_ID AND t2.SENDER  = t3.WECHAT_USER_ID ");
        sql.append(data.getString("sessionId"), "where  ROOM_ID = ? ");
        sql.append(data.getString("getSendStartDate"), "and  MSG_TIME >= ? ");
        sql.append(data.getString("getSendEndDate"), "and  MSG_TIME <= ? ");
        sql.append("order by t2.MSG_TIME ");
        logger.info("根据群ID查询群聊天记录SQL:" + sql.getSQL() + ",查询参数:" + JSONObject.toJSONString(sql.getParams()));
        JSONObject result = queryForPageList(sql.getSQL(), sql.getParams(), new JSONMapperImpl(), false, data, easyQuery);
        //判断聊天内容是否来自企微渠道，如果来自企微需要对附件链接的消息处理
        if (result != null && result.getJSONArray("data") != null && result.getJSONArray("data").size() > 0) {
            JSONObject obj = result.getJSONArray("data").getJSONObject(0);
            result.put("data", appendWeComOSSFileCode(result.getJSONArray("data")));
        }
        return result;
    }


    public static JSONArray appendWeComOSSFileCode(JSONArray data) {
        String code = "";
        JSONObject result = new JSONObject();
        JSONObject request = new JSONObject();
        request.put("command", "getWeComOSSFileCode");
        JSONObject params = new JSONObject();
        request.put("params", params);
        try {
            IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
            result = service.invoke(request);
            if (!result.isEmpty()) {
                code = result.getString("certification");
            }

        } catch (ServiceException e) {
            CommonLogger.logger.error("请求oss访问凭证失败" + e.getMessage(), e);
        }
        for (int i = 0; i < data.size(); i++) {
            JSONObject obj = data.getJSONObject(i);
            String content = obj.getString("CONTENT");
            if (content.startsWith("http") && content.indexOf("/auth/userDownload") != -1) {
                content += "?certification=" + code;
                obj.put("CONTENT", content);
            }
        }
        return data;
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    private static JSONObject queryForPageList(String sql, Object[] params, EasyRowMapper<?> rowMapper, boolean emptyPage, JSONObject param, EasyQuery easyQuery) {
        if (rowMapper == null) {
            rowMapper = new MapRowMapperImpl();
        }
        int pageIndex = -1;
        int pageSize = 10;
        int total = -1;
        int pageType = 1;
        try {
            pageType = param.getIntValue("pageType");
            pageType = pageType == 0 ? 1 : pageType;
            pageIndex = param.getIntValue("pageIndex");
            pageSize = param.getIntValue("pageSize");
            pageSize = pageSize == 0 ? 1 : pageSize;
        } catch (Exception var12) {
            logger.warn("无法获取到当前分页的数据，对于Dao对象查询来说，[pageIndex,pageSize]这两个参数是必须的!", var12);
        }

        JSONObject resultJson = new JSONObject();
        Object list = new ArrayList();
        try {
            if (emptyPage) {
                total = 0;
            } else {
                if (pageIndex < 0 && pageType == 1 || pageType > 2) {
                    String countSql = "select count(1) from (" + sql + ") temp";
                    total = easyQuery.queryForInt(countSql, params);
                }
                list = easyQuery.queryForList(sql, params, pageIndex, pageSize, (EasyRowMapper) rowMapper);
            }
        } catch (SQLException var13) {
            logger.error("DaoContext.queryForPageList()->处理查询结果错误，原因：" + var13.getMessage(), var13);
            resultJson.put("msg", var13.getMessage());
            resultJson.put("state", 0);
            return resultJson;
        }

        if (pageType != 2) {
            resultJson.put("totalRow", total);
            resultJson.put("totalPage", (total + (pageSize - 1)) / pageSize);
        }

        resultJson.put("msg", "请求成功!");
        resultJson.put("state", 1);
        resultJson.put("pageSize", pageSize);
        resultJson.put("pageNumber", pageIndex);
        resultJson.put("data", list);
        resultJson.put("pageType", pageType);
        return resultJson;
    }

}
