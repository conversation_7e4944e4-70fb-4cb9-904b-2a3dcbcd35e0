-- 群登记工单记录表
CREATE TABLE "YWDB"."C_NO_GROUP_ORDERS"
(	"ID" VARCHAR2(64) NOT NULL ENABLE,
     "CHAT_ID" VARCHAR2(64) NOT NULL ENABLE,
     "GROUP_CHAT_NAME" VARCHAR2(500),
     "GROUP_CREATE_TIME" VARCHAR2(32),
     "GROUP_CREATE_STEWARD" VARCHAR2(64),
     "GROUP_CREATE_ACC" VARCHAR2(64),
     "SITE_CODE" VARCHAR2(128),
     "SITE_NAME" VARCHAR2(128),
     "SITE_CONTACT" VARCHAR2(64),
     "AREA_CODE" VARCHAR2(64),
     "USER_NAME" VARCHAR2(500),
     "USER_PHONE" VARCHAR2(64),
     "USER_ADDRESS" VARCHAR2(500),
     "ORG_CODE" VARCHAR2(64),
     "BRAND_CODE" VARCHAR2(64),
     "<PERSON>AND_NAME" VARCHAR2(64),
     "PROD_CODE" VARCHAR2(64),
     "PROD_NAME" VARCHAR2(200),
     "PRODUCT_CODE" VARCHAR2(64),
     "PRODUCT_MODEL" VARCHAR2(400),
     "SALE_UNIT" VARCHAR2(128),
     "SALE_PEOPLE" VARCHAR2(500),
     "SALE_PHONE" VARCHAR2(64),
     "BACKUP" VARCHAR2(2000),
     "ORDER_SOURCE" VARCHAR2(500),
     "ORDER_STATE" VARCHAR2(64),
     "IS_NORMS" VARCHAR2(32),
     "REASON" VARCHAR2(2000),
     "CHECK_TIME" VARCHAR2(64),
     "CHECK_NAME" VARCHAR2(32),
     "GROUP_STATE" VARCHAR2(64),
     "CREATE_TIME" VARCHAR2(32),
     "CREATE_USER" VARCHAR2(64),
     "UPDATE_TIME" VARCHAR2(32),
     "UPDATE_USER" VARCHAR2(64),
     "ENT_ID" VARCHAR2(32),
     "SERVICE_ORDER_NUMBER" VARCHAR2(512) DEFAULT NULL,
     "IS_TRIGGER_SENSITIVE" VARCHAR2(10) DEFAULT '0' NOT NULL ENABLE,
     CONSTRAINT "PK_C_NO_GROUP_ORDERS" PRIMARY KEY ("ID")
         USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS
         STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
         PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
         BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
         TABLESPACE "YWDBD"  ENABLE
) SEGMENT CREATION IMMEDIATE
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "YWDBD" ;

COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."ID" IS '编号';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."CHAT_ID" IS '群聊ID';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."GROUP_CHAT_NAME" IS '群名';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."GROUP_CREATE_TIME" IS '建群日期';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."GROUP_CREATE_STEWARD" IS '建群管家';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."GROUP_CREATE_ACC" IS '建群账号';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."SITE_CODE" IS '网点编码';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."SITE_NAME" IS '网点名称';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."SITE_CONTACT" IS '网点对接人';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."AREA_CODE" IS '所属运中';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."USER_NAME" IS '用户姓名';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."USER_PHONE" IS '用户电话';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."USER_ADDRESS" IS '用户地址';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."ORG_CODE" IS '产品主体';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."BRAND_CODE" IS '品牌编码';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."BRAND_NAME" IS '产品品牌';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."PROD_CODE" IS '品类编码';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."PROD_NAME" IS '产品品类';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."PRODUCT_CODE" IS '型号编码';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."PRODUCT_MODEL" IS '产品型号';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."SALE_UNIT" IS '销售单位';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."SALE_PEOPLE" IS '销售人员';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."SALE_PHONE" IS '销售人电话';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."BACKUP" IS '备注';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."ORDER_SOURCE" IS '单据来源';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."ORDER_STATE" IS '当前进度';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."IS_NORMS" IS '是否规范';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."REASON" IS '不规范原因';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."CHECK_TIME" IS '稽查时间';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."CHECK_NAME" IS '稽查人';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."GROUP_STATE" IS '群状态';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."CREATE_TIME" IS '创建时间';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."CREATE_USER" IS '创建人';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."UPDATE_TIME" IS '更新时间';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."UPDATE_USER" IS '更新人';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."ENT_ID" IS '所属企业编号';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."SERVICE_ORDER_NUMBER" IS '服务单号,支持存储多条,以;进行分割';
   COMMENT ON COLUMN "YWDB"."C_NO_GROUP_ORDERS"."IS_TRIGGER_SENSITIVE" IS '是否触发敏感词，0：未触发，1：已触发';
   COMMENT ON TABLE "YWDB"."C_NO_GROUP_ORDERS"  IS '群登记工单记录表';

-- 示例数据
INSERT INTO YWDB.C_NO_GROUP_ORDERS
(ID, CHAT_ID, GROUP_CHAT_NAME, GROUP_CREATE_TIME, GROUP_CREATE_STEWARD, GROUP_CREATE_ACC, SITE_CODE, SITE_NAME, SITE_CONTACT, AREA_CODE, USER_NAME, USER_PHONE, USER_ADDRESS, ORG_CODE, BRAND_CODE, BRAND_NAME, PROD_CODE, PROD_NAME, PRODUCT_CODE, PRODUCT_MODEL, SALE_UNIT, SALE_PEOPLE, SALE_PHONE, BACKUP, ORDER_SOURCE, ORDER_STATE, IS_NORMS, REASON, CHECK_TIME, CHECK_NAME, GROUP_STATE, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER, ENT_ID, SERVICE_ORDER_NUMBER, IS_TRIGGER_SENSITIVE)
VALUES('82408874580511374983507', 'wrhBi-DwAAjkJIpmWtZWoV5O25whIt0g', '中海云水观园6-2-501李总福宅美的群', '2025-09-29 10:21:32', '1+N-美的家中管家9', '18038875770', 'W6101048992', '陕西冷协中央空调技术服务有限公司', '杨朝晖', '西安中心', '李海明', '15934849801', '西安市雁塔区鱼化寨街道中海云水观园6-2-501', 'CS023', 'MIDEA', '美的', '1231', '家用多联式中央空调', NULL, NULL, '陕西美洲豹科技有限公司', '毛瑜嘉', '17391639318', '预计9.29进场', 'C2', '1', '未稽查', NULL, NULL, '邓燕玲ex_dengyl6', NULL, '2025-09-29 10:22:21', 'ex_dengyl6', NULL, NULL, '1000', 'FW250949807324', '0');
INSERT INTO YWDB.C_NO_GROUP_ORDERS
(ID, CHAT_ID, GROUP_CHAT_NAME, GROUP_CREATE_TIME, GROUP_CREATE_STEWARD, GROUP_CREATE_ACC, SITE_CODE, SITE_NAME, SITE_CONTACT, AREA_CODE, USER_NAME, USER_PHONE, USER_ADDRESS, ORG_CODE, BRAND_CODE, BRAND_NAME, PROD_CODE, PROD_NAME, PRODUCT_CODE, PRODUCT_MODEL, SALE_UNIT, SALE_PEOPLE, SALE_PHONE, BACKUP, ORDER_SOURCE, ORDER_STATE, IS_NORMS, REASON, CHECK_TIME, CHECK_NAME, GROUP_STATE, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER, ENT_ID, SERVICE_ORDER_NUMBER, IS_TRIGGER_SENSITIVE)
VALUES('82408881758661930086914', 'wrhBi-DwAAG2NAde2affR9rMinfstR0A', 'FW250949718963华凌集成水槽服务群', '2025-09-29 10:09:43', '1+N-美的厨热/冰箱/场景改造管家7', '18038874193', 'W4311009441', '永州市民华家电服务有限公司', '李莹', '长沙中心', '李禾青', '15973571088', '湖南省郴州市嘉禾县珠泉镇中港世纪嘉城7栋一单元402', 'CS023', 'HUALING', '华凌', '1333', '集成水槽洗碗机', NULL, NULL, NULL, NULL, NULL, NULL, 'D2', '1', '未稽查', NULL, NULL, '黄家慧ex_huangjh63', NULL, '2025-09-29 10:10:24', 'ex_huangjh63', NULL, NULL, '1000', 'FW250949718963', '0');
INSERT INTO YWDB.C_NO_GROUP_ORDERS
(ID, CHAT_ID, GROUP_CHAT_NAME, GROUP_CREATE_TIME, GROUP_CREATE_STEWARD, GROUP_CREATE_ACC, SITE_CODE, SITE_NAME, SITE_CONTACT, AREA_CODE, USER_NAME, USER_PHONE, USER_ADDRESS, ORG_CODE, BRAND_CODE, BRAND_NAME, PROD_CODE, PROD_NAME, PRODUCT_CODE, PRODUCT_MODEL, SALE_UNIT, SALE_PEOPLE, SALE_PHONE, BACKUP, ORDER_SOURCE, ORDER_STATE, IS_NORMS, REASON, CHECK_TIME, CHECK_NAME, GROUP_STATE, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER, ENT_ID, SERVICE_ORDER_NUMBER, IS_TRIGGER_SENSITIVE)
VALUES('82408881676313560659740', 'wrhBi-DwAAPcHMDpwIaeIBCVXafXYMCg', 'FW250949601154/FW250949601208美的洗碗机服务群', '2025-09-29 10:09:30', '1+N-美的厨热/冰箱/场景改造管家4', '18038874427', 'W4105089381', '内黄县鑫美家电维修中心（个体工商户）', '刘静波', '郑州中心', '张瑞雪', '16637259890', '河南省安阳市内黄县龙庆街道龙庆街道昆仑明珠小区3幢一单元1402', 'CS023', 'MIDEA', '美的', '1020', '洗碗机', NULL, NULL, NULL, NULL, NULL, NULL, '14', '1', NULL, NULL, NULL, '李雅晨ex_liyc35', NULL, '2025-09-29 10:10:32', 'ex_liyc35', '2025-09-29 10:10:50', 'ex_liyc35', '1000', 'FW250949601208;FW250949601154', '0');

-- 群工单与服务单关联表 C_NO_GROUP_ORDERS与C_NO_GROUP_ORDERS_MATCH是一对多的关系
CREATE TABLE YWDB.C_NO_GROUP_ORDERS_MATCH (
                                              ID VARCHAR2(64) PRIMARY KEY,
                                              GROUP_ORDER_ID VARCHAR2(64),
                                              CHAT_ID VARCHAR2(64),
                                              SERVICE_ORDER_NUMBER VARCHAR2(200),
                                              CREATE_TIME VARCHAR2(32),
                                              P_CREATE_TIME VARCHAR2(32),
                                              EX_1 VARCHAR2(32),
                                              EX_2 VARCHAR2(32)
);
COMMENT ON TABLE YWDB.C_NO_GROUP_ORDERS_MATCH IS '群工单与服务单关联表';
COMMENT ON COLUMN YWDB.C_NO_GROUP_ORDERS_MATCH.ID IS '主键ID';
COMMENT ON COLUMN YWDB.C_NO_GROUP_ORDERS_MATCH.GROUP_ORDER_ID IS '群工单ID';
COMMENT ON COLUMN YWDB.C_NO_GROUP_ORDERS_MATCH.CHAT_ID IS '群聊ID';
COMMENT ON COLUMN YWDB.C_NO_GROUP_ORDERS_MATCH.SERVICE_ORDER_NUMBER IS '服务单号';
COMMENT ON COLUMN YWDB.C_NO_GROUP_ORDERS_MATCH.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN YWDB.C_NO_GROUP_ORDERS_MATCH.P_CREATE_TIME IS '原群工单创建时间';
COMMENT ON COLUMN YWDB.C_NO_GROUP_ORDERS_MATCH.EX_1 IS '扩展字段1';
COMMENT ON COLUMN YWDB.C_NO_GROUP_ORDERS_MATCH.EX_2 IS '扩展字段2';

CREATE INDEX IDX_CNGOM_SERVICE_ORDER_CNGOM ON YWDB.C_NO_GROUP_ORDERS_MATCH(SERVICE_ORDER_NUMBER);
CREATE INDEX IDX_CNGOM_CREATE_TIME ON YWDB.C_NO_GROUP_ORDERS_MATCH(CREATE_TIME);
CREATE INDEX IDX_CNGOM_P_CREATE_TIME ON YWDB.C_NO_GROUP_ORDERS_MATCH(P_CREATE_TIME);

-- 示例数据
INSERT INTO YWDB.C_NO_GROUP_ORDERS_MATCH
(ID, CHAT_ID, SERVICE_ORDER_NUMBER, CREATE_TIME, EX_1, EX_2, GROUP_ORDER_ID, P_CREATE_TIME)
VALUES('82412070103922735049669', 'wrIp3YcQAAjeQ6lyCl63oM8ixTcK2ETg', 'FW250908679773', '2025-09-25 17:36:29', NULL, NULL, '82412070104082735330670', '2025-09-25 17:36:29');
INSERT INTO YWDB.C_NO_GROUP_ORDERS_MATCH
(ID, CHAT_ID, SERVICE_ORDER_NUMBER, CREATE_TIME, EX_1, EX_2, GROUP_ORDER_ID, P_CREATE_TIME)
VALUES('82412932994214275327197', 'wrIp3YcQAAtsNlLpCsEIcB757zbYgWlw', 'FW250908679779', '2025-09-24 17:38:20', NULL, NULL, '82412932994344275654266', '2025-09-24 17:38:20');
INSERT INTO YWDB.C_NO_GROUP_ORDERS_MATCH
(ID, CHAT_ID, SERVICE_ORDER_NUMBER, CREATE_TIME, EX_1, EX_2, GROUP_ORDER_ID, P_CREATE_TIME)
VALUES('82412958913234301294118', 'wrIp3YcQAArScon6k-MXCwaK6K72yZPg', 'FW250908679776', '2025-09-24 16:55:08', NULL, NULL, '82412958992734301930168', '2025-09-24 16:55:00');
