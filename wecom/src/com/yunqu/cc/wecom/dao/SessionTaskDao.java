package com.yunqu.cc.wecom.dao;

import com.yunqu.cc.wecom.inf.WecomService;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasySQL;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.wecom.base.AppDaoContext;
import com.yunqu.cc.wecom.base.CommonLogger;
import org.easitline.common.utils.string.StringUtils;

@WebObject(name = "sessionTask")
public class SessionTaskDao extends AppDaoContext{

	/**
	 * CC系统内部调用
	 */
	@WebControl(name = "findCallRecordDetal", type = Types.LIST)
	public JSONObject findCallRecordDetal() {
		String qcId = param.getString("qcId");
		String sessionId = param.getString("sessionId");
		String getSendStartDate = param.getString("getSendStartDate");
		String getSendEndDate = param.getString("getSendEndDate");
		JSONObject data = new JSONObject();
		EasySQL sql = this.getEasySQL("");
		sql.append("select DISTINCT t2.MSG_ID as RECORD_DETAIL_ID, t2.MSG_TIME as BEGIN_TIME, t2.MSG_CONTENT CONTENT, '' as WITHDRAW,");
		sql.append("(CASE WHEN AGENT_ACC is null   THEN '02' else '01' END) as USER_TYPE, ");
		sql.append("t2.AGENT_ACC,'' as channel_id, ");
		//20221018 暂时新增"emotion"企微表情类型 视为图片类型、新增voice，类型暂时用07
		sql.append("(CASE t2.MSG_TYPE WHEN 'text' THEN '01'  WHEN 'image' THEN '02'  WHEN 'video' THEN '06'  WHEN 'file' THEN '09'  WHEN 'emotion' THEN '02' END) TYPE,");
		sql.append("t2.AGENT_ACC as AGENT_NAME,(CASE WHEN USER_NAME is not null   THEN USER_NAME else  SENDER END)  as CUSTOMER_NAME ");
		sql.append("from C_NO_WECOM_GROUP_RECORD t2   LEFT JOIN C_NO_WECOM_GROUP_USER_INFO t3 ON t2.ROOM_ID  = t3 .WECHAT_GROUP_ID AND t2.SENDER  = t3.WECHAT_USER_ID ");
		sql.append(sessionId, "where  ROOM_ID = ? ");
		sql.append(getSendStartDate, "and  MSG_TIME >= ? ");
		sql.append(getSendEndDate, "and  MSG_TIME <= ? ");
		sql.append("order by t2.MSG_TIME ");
		CommonLogger.logger.info("详情接口" + sql.getSQL());
		data = this.queryForList(sql.getSQL(), sql.getParams(), null);
		//判断聊天内容是否来自企微渠道，如果来自企微需要对附件链接的消息处理
		if(data!=null&&data.getJSONArray("data")!=null&&data.getJSONArray("data").size()>0) {
			JSONObject obj = data.getJSONArray("data").getJSONObject(0);
			data.put("data", appendWeComOSSFileCode(data.getJSONArray("data")));
		}
		return data;
	}

	private JSONArray appendWeComOSSFileCode(JSONArray data) {
		String code = "";
		JSONObject result = new JSONObject();
		JSONObject request = new JSONObject();
		request.put("command", "getWeComOSSFileCode");
		JSONObject params = new JSONObject();
		request.put("params", params);
		try {
			IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
			result = service.invoke(request);
			if(!result.isEmpty()) {
				code = result.getString("certification");
			}
			
		} catch (ServiceException e) {
			CommonLogger.logger.error("请求oss访问凭证失败" + e.getMessage(), e);
		}
		for(int i=0;i<data.size();i++) {
			JSONObject obj = data.getJSONObject(i);
			String content = obj.getString("CONTENT");
			if(content.startsWith("http")&&content.indexOf("/auth/userDownload")!=-1) {
				content += "?certification=" + code;
				obj.put("CONTENT",content);
			}
		}
		return data;
	}

	/**
	 * 工作台2.0调用 支持分页
	 */
	@WebControl(name = "findCallRecordDetailNew", type = Types.LIST)
	public JSONObject findCallRecordDetailNew() {
		String sessionId = param.getString("sessionId");
		String pageIndex = param.getString("pageIndex");
		String pageSize = param.getString("pageSize");
		String pageType = param.getString("pageType");
		//消息发送开始时间
		String getSendStartDate = param.getString("getSendStartDate");
		//消息发送结束时间
		String getSendEndDate = param.getString("getSendEndDate");
		if (StringUtils.isAnyBlank(sessionId,pageIndex,pageSize,pageType,getSendStartDate,getSendEndDate)){
			return EasyResult.fail("必填参数不能为空");
		}
		return WecomService.queryQWGroupMes(param,getQuery());
	}
}
