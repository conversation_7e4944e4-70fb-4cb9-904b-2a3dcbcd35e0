package com.yunqu.cc.activeService.inf;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.yq.busi.common.util.CommonUtil;
import org.apache.camel.component.file.strategy.FileChangedExclusiveReadLockStrategy;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;

import cn.hutool.core.thread.ThreadUtil;

/**
 * 企业微信策略服务
 * <AUTHOR>
 *
 */
public class WechatWorkStrategyService extends IService{

	public Logger logger = CommLogger.getCommLogger("wechatWorkStrategy");

	//WechatWorkService wechatWorkService1 = new WechatWorkService();

	@Override
	public JSONObject invoke(JSONObject resqJson) throws ServiceException {
		long serialId = System.currentTimeMillis();
		logger.info("WechatWorkStrategyService.invoke["+serialId+"]====>>>>resqJson:"+resqJson);
		String command = resqJson.getString("command");
		JSONObject result = new JSONObject();
		if("asynDoStrategy".equals(command)){ //异步执行企微策略
			result = asynDoStrategy(resqJson.getJSONObject("params"),serialId);
		}else{
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "不存在的command,请检查！");
		}
		logger.info("WechatWorkStrategyService.invoke["+serialId+"]=====<<<<result:"+result);
		return result;
	}

	public synchronized JSONObject asynDoStrategy(JSONObject obj,long serialId){
		ThreadUtil.execute(() -> {
			doStrategy(obj, serialId);
		});
		JSONObject result = new JSONObject();
		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		result.put("respDesc", "请求成功，处理中");
		return result;
	}

	public synchronized JSONObject doStrategy(JSONObject obj,long serialId){
		logger.info("WechatWorkStrategyService.doStrategy["+serialId+"]=====BEGIN......");
		String strategyId = obj.getString("ID");
		JSONObject result = new JSONObject();
		try {
			if(StringUtils.isBlank(strategyId)){
				logger.info("WechatWorkStrategyService.doStrategy["+serialId+"]=====无策略ID");
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "处理失败，入参无策略ID");
				return result;
			}
			//修改策略处理状态为1，表示处理中，方式任务执行时间过长，重复执行
			EasyRecord record = new EasyRecord("C_NO_AS_WECHAT_STRATEGY", "ID");
			record.put("DEAL_STATUS", "1");
			record.put("ID", strategyId);
			getQuery().update(record);

			//查询策略的扩展配置
			EasySQL easySQL = new EasySQL("select t1.*,t2.*,t3.FILE_SIZE from C_NO_AS_WECHAT_STRATEGY_EX t1");
			easySQL.append("left join C_NO_AS_MATERIAL t2 on t1.CONFIG_TYPE=1 and t1.SCRIPT_TYPE=1 and t1.SCRIPT_REFER_ID = t2.ID");
			easySQL.append("left join C_CF_ATTACHMENT t3 on t2.M_FILEID = t3.ID");
			easySQL.append("where 1=1");
			easySQL.append(strategyId,"and t1.STRATEGY_ID=?");
			List<JSONObject> strategyExs = getQuery().queryForList(easySQL.getSQL(), easySQL.getParams(),new JSONMapperImpl());
			if(strategyExs==null || strategyExs.size()<=0){
				logger.info("WechatWorkStrategyService.doStrategy["+serialId+"]=====无策略配置信息");
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "处理失败，未查询到策略配置信息");
				return result;
			}
			//群发文本内容，用于后续群发使用
			String sendContent = "";
			//素材集合，用户后续上传素材
			JSONArray material = new JSONArray();
			//机构集合，用于后续群发
			JSONArray orgList = new JSONArray();
			//用户ID集合，用户后续群发使用
			JSONArray userId = new JSONArray();
			//用户信息集合，群发成功，记录本次策略的使用的用户信息
			JSONArray userJson = new JSONArray();
			//筛选标签ID集合，用于后续群发使用
			JSONArray searchLabel = new JSONArray();
			//排除标签ID集合，用于后续群发使用
			JSONArray excludeLabel = new JSONArray();
			//处理扩展配置，获取素材信息，发送人员信息，标签信息等
			for (JSONObject jsonObject : strategyExs) {
				String configType = jsonObject.getString("CONFIG_TYPE");
				String scriptType = jsonObject.getString("SCRIPT_TYPE");
				if(StringUtils.equals(configType, "1") && StringUtils.equals(scriptType, "1")){
					material.add(jsonObject);
				}else if(StringUtils.equals(configType, "1") && StringUtils.equals(scriptType, "2")){
					sendContent = jsonObject.getString("SEND_CONTENT");
				}else if(StringUtils.equals(configType, "2") && jsonObject.getJSONObject("SENDER_LIST")!=null){
					JSONObject org = new JSONObject();
					org.put("departmentId", jsonObject.getString("SEND_ORG_CODE"));
					//TODO:部门ID全路径
					//org.put("fullId", "");
					orgList.add(org);
					userId.addAll(jsonObject.getJSONObject("SENDER_LIST").getJSONArray("id"));
					userJson.addAll(jsonObject.getJSONObject("SENDER_LIST").getJSONArray("list"));
				}else if(StringUtils.equals(configType, "3") && jsonObject.getJSONObject("SEARCH_LABEL_LIST")!=null){
					searchLabel = jsonObject.getJSONObject("SEARCH_LABEL_LIST").getJSONArray("id");
				}else if(StringUtils.equals(configType, "4") && jsonObject.getJSONObject("EXCLUDE_LABEL_LIST")!=null){
					excludeLabel = jsonObject.getJSONObject("EXCLUDE_LABEL_LIST").getJSONArray("id");
				}
			}
			if(StringUtils.isBlank(sendContent)){
				logger.info("WechatWorkStrategyService.doStrategy["+serialId+"]=====群发发送内容为空");
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "处理失败，群发发送内容为空");
				return result;
			}

			//判断策略ID对应的是人群包还是人群组
			String packType = obj.getString("STRATEGY_TYPE");
			if(StringUtils.equals(packType, "2")){
				//类型为人群包组，循环处理人群包组内所有可执行的人群包
				JSONArray crowdPacks = obj.getJSONArray("crowdPacks");
				for(int i=0;i<crowdPacks.size();i++){
					dealData(obj, crowdPacks.getJSONObject(i).getString("ID"),crowdPacks.getJSONObject(i).getString("NAME")
							, material, orgList, userId, userJson, searchLabel, sendContent, excludeLabel, serialId);
				}
			}else{
				dealData(obj, obj.getString("CROWD_PACK_ID"),obj.getString("PACK_NAMES"),
						material, orgList, userId, userJson, searchLabel, sendContent, excludeLabel, serialId);
			}
			record = new EasyRecord("C_NO_AS_WECHAT_STRATEGY", "ID");
			record.put("DEAL_STATUS", "2");
			record.put("ID", strategyId);
			getQuery().update(record);
		} catch (Exception e) {
			logger.info("WechatWorkStrategyService.doStrategy["+serialId+"]=====策略执行失败，记录日志查看是否进入catch......");
			try{
				EasyRecord record = new EasyRecord("C_NO_AS_WECHAT_STRATEGY", "ID");
				record.put("DEAL_STATUS", "2");
				record.put("ID", strategyId);
				getQuery().update(record);
			}catch (Exception e1) {
				logger.info("WechatWorkStrategyService.doStrategy["+serialId+"]=====ERROR......"+e1.getMessage(),e1);
				logger.error("WechatWorkStrategyService.doStrategy["+serialId+"]=====ERROR......"+e1.getMessage(),e1);
			}
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理失败："+e.getMessage());
			logger.info("WechatWorkStrategyService.doStrategy["+serialId+"]=====ERROR......"+e.getMessage(),e);
			logger.error("WechatWorkStrategyService.doStrategy["+serialId+"]=====ERROR......"+e.getMessage(),e);
			return result;
		}
		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		result.put("respDesc", "处理完成");
		logger.info("WechatWorkStrategyService.doStrategy["+serialId+"]=====END......");
		return result;
	}

	/**
	 * 处理策略数据
	 * @param strategys 策略对象
	 * @param packId 人群包ID
	 * @param packName 人群包名称
	 * @param material 素材集合
	 * @param orgList 机构集合
	 * @param userId 用户Id集合
	 * @param userJson 用户集合
	 * @param searchLabel 搜索标签
	 * @param sendContent 发送内容
	 * @param excludeLabel 排除标签
	 * @param serialId 流水ID
	 * @return
	 * @throws Exception
	 */
	private JSONObject dealData(JSONObject strategys,String packId ,String packName,JSONArray material,
			JSONArray orgList,JSONArray userId,JSONArray userJson,JSONArray searchLabel,
			String sendContent,JSONArray excludeLabel,long serialId){
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealData["+serialId+"]=====异步处理策略开始......");
		try {
			//更新人群包的推送状态
			String priority = strategys.getString("PRIORITY");
			dealSendStatus(strategys.getString("ID"), packId);
			//批次号
			String crowdPackId = packId + RandomKit.randomStr();
			//处理人群包信息
			String pushTime = DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss");
			JSONObject result = saveRecord(strategys, crowdPackId, packId, packName, serialId);
			if (!StringUtils.equals(result.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
				CommLogger.getCommLogger("wechatWorkStrategy-" + packId).info("WechatWorkStrategyService.dealData[" + serialId + "]=====处理人群包失败......");
				dealUser(result, strategys, crowdPackId, packId, pushTime, false, priority, serialId);
				return result;
			}

			//处理素材信息
			String materialTime = DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss");
			JSONObject materialJSON = dealMaterial(material, serialId, packId);
			if (!StringUtils.equals(materialJSON.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
				CommLogger.getCommLogger("wechatWorkStrategy-" + packId).info("WechatWorkStrategyService.dealData[" + serialId + "]=====素材上传异常" + materialJSON);
				//更新发送记录表状态为：5，素材同步失败
				EasyRecord record = new EasyRecord("C_NO_AS_WECHAT_SEND_RECORD", "ID");
				record.put("ID", crowdPackId);
				record.put("PUSH_MATERIAL_TIME", materialTime);
				record.put("CROWD_PACK_STATUS", 5);
				//处理素材信息失败，进行删除人群包
				JSONObject infResult = deleteCrowdPack(crowdPackId);
				if (infResult == null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
					CommLogger.getCommLogger("wechatWorkStrategy-" + packId).info("WechatWorkStrategyService.dealData[" + serialId + "]=====处理素材信息失败后，删除人群包失败：" + infResult);
					record.put("CROWD_PACK_STATUS", 7);
				}
				getQuery().update(record);
				//群发失败，处理推送用户
				dealUser(result, strategys, crowdPackId, packId, pushTime, false, priority, serialId);
				return materialJSON;
			}
			JSONArray materials = materialJSON.getJSONArray("respData");
			CommLogger.getCommLogger("wechatWorkStrategy-" + packId).info("WechatWorkStrategyService.dealData[" + serialId + "]=====处理素材信息成功，获取素材ID集合为:" + materials);

			JSONObject infParam = new JSONObject();
			//infParam.put("departments", orgList);
			infParam.put("userIds", userId);
			infParam.put("content", sendContent);

			int sendAllCust = 1;
			infParam.put("crowdPackId", crowdPackId);
			if (StringUtils.isNotBlank(strategys.getString("ADD_START_TIME"))) {
				Date addTimeBegin = DateUtil.getDate("yyyy-MM-dd HH:mm:ss", strategys.getString("ADD_START_TIME"));
				infParam.put("addTimeBegin", addTimeBegin.getTime());
				sendAllCust = 2;
			}
			if (StringUtils.isNotBlank(strategys.getString("ADD_END_TIME"))) {
				Date addTimeEnd = DateUtil.getDate("yyyy-MM-dd HH:mm:ss", strategys.getString("ADD_END_TIME"));
				infParam.put("addTimeEnd", addTimeEnd.getTime());
				sendAllCust = 2;
			}
			infParam.put("fileIds", materials);
			if (excludeLabel != null && excludeLabel.size() > 0) {
				sendAllCust = 2;
			}
			if (searchLabel != null && searchLabel.size() > 0) {
				sendAllCust = 2;
			}
			infParam.put("sendAllCust", sendAllCust);
			infParam.put("excludeCustTags", excludeLabel);
			infParam.put("includeCustTags", searchLabel);
			//infParam.put("fileIds",file);
			String sendTime = DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss");
			//JSONObject infResult = wechatWorkService.createService(infParam);
			JSONObject obj = new JSONObject();
			obj.put("command", "createService");
			obj.put("params", infParam);
			JSONObject infResult = doIservice(obj);
			if (infResult == null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
				//更新发送记录表状态为：6，发送失败
				CommLogger.getCommLogger("wechatWorkStrategy-" + packId).info("WechatWorkStrategyService.dealData[" + serialId + "]=====调用群发接口异常：" + infResult);
				EasyRecord record = new EasyRecord("C_NO_AS_WECHAT_SEND_RECORD", "ID");
				record.put("ID", crowdPackId);
				record.put("PUSH_MATERIAL_TIME", materialTime);
				record.put("SEND_TIME", sendTime);
				record.put("CROWD_PACK_STATUS", 6);
				//处理群发信息失败，进行删除人群包
				infResult = deleteCrowdPack(crowdPackId);
				if (infResult == null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
					CommLogger.getCommLogger("wechatWorkStrategy-" + packId).info("WechatWorkStrategyService.dealData[" + serialId + "]=====处理群发信息失败后，删除人群包失败：" + infResult);
					record.put("CROWD_PACK_STATUS", 7);
				}
				getQuery().update(record);
				//发送失败，处理推送用户
				dealUser(result, strategys, crowdPackId, packId, pushTime, false, priority, serialId);
				return infResult;
			}
			JSONObject data = infResult.getJSONObject("respData").getJSONObject("data");
			String groupSendId = "";
			if (data != null) {
				groupSendId = data.getString("groupsendId");
			}
			CommLogger.getCommLogger("wechatWorkStrategy-" + packId).info("WechatWorkStrategyService.dealData[" + serialId + "]=====群发成功，群发ID为：" + groupSendId);
			EasyRecord record = new EasyRecord("C_NO_AS_WECHAT_SEND_RECORD", "ID");
			record.put("ID", crowdPackId);
			record.put("PUSH_MATERIAL_TIME", materialTime);
			record.put("SEND_TIME", sendTime);
			record.put("SEND_RECORD_ID", groupSendId);
			getQuery().update(record);
			//群发成功，处理推送用户
			dealUser(result, strategys, crowdPackId, packId, pushTime, true, priority, serialId);
			dealWechatUser(userJson, crowdPackId);
			CommLogger.getCommLogger("wechatWorkStrategy-" + packId).info("WechatWorkStrategyService.dealData[" + serialId + "]=====异步处理策略结束......");
			return infResult;
		} catch (Exception e) {
			CommLogger.getCommLogger("wechatWorkStrategy-" + packId).info("WechatWorkStrategyService.dealData["+serialId+"]=====异步处理策略异常："+e.getMessage(),e);
			CommLogger.getCommLogger("wechatWorkStrategy-" + packId).error("WechatWorkStrategyService.dealData["+serialId+"]=====异步处理策略异常："+e.getMessage(),e);
			JSONObject result = new JSONObject();
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理失败："+e.getMessage());
			CommLogger.getCommLogger("wechatWorkStrategy-" + packId).info("WechatWorkStrategyService.dealData[" + serialId + "]=====异步处理策略结束......");
			return result;
		}
	}

	/**
	 * 记录本次群发所有选择的微信用户，关联微信名称
	 * @param strategyId
	 * @param crowdPackId
	 * @throws Exception
	 */
	private void dealSendStatus(String strategyId, String crowdPackId) throws Exception{
		String sql = "update C_NO_AS_CROWDPACK_PRIORITY set SEND_STATUS='2' where STRATEGY_ID=? and CROWD_PACK_ID=?";
		getQuery().execute(sql, strategyId,crowdPackId);
	}


	/**
	 * 记录本次群发所有选择的微信用户，关联微信名称
	 * @param userJson
	 * @param crowdPackId
	 * @throws Exception
	 */
	private void dealWechatUser(JSONArray userJson,String crowdPackId) throws Exception{
		for(int i=0;i<userJson.size();i++){
			JSONObject user = userJson.getJSONObject(i);
			user.remove("id");
			user.remove("name");
			EasyRecord record =  new EasyRecord("C_NO_AS_WECHAT_USER_DETAIL", "ID");
			record.putAll(user);
			record.put("ID", RandomKit.randomStr());
			record.put("SEND_ID", crowdPackId);
			getQuery().save(record);
		}
	}

	/**
	 * 判断推送处理结果，记录处理用户明细
	 * @param crowdPackJSON 推送处理结果
	 * @param strategys 策略信息
	 * @param crowdPackId 本批人群包ID
	 * @param packId 本次人群包ID
	 * @param pushTime 推送时间
	 * @param isSucc 群发是否成功
	 * @throws Exception
	 */
	private void dealUser(JSONObject crowdPackJSON,JSONObject strategys
			,String crowdPackId,String packId,String pushTime,boolean isSucc,String priority,long serialId) throws Exception{
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>开始处理本此用户情况");
		JSONObject data = crowdPackJSON.getJSONObject("respData");
		//推送成功人群
		JSONArray pushSuccess = data.getJSONArray("pushSuccess");
		//推送失败人群
		JSONArray pushError = data.getJSONArray("pushError");
		int status = data.getIntValue("dealStatus");
		String operateAgent = strategys.getString("OPERATE_AGENT");
		if(StringUtils.isEmpty(operateAgent)){
			operateAgent = "system";
		}
		//处理推送用户信息
		if(isSucc){
			dealUser(status==3 ? "1":"2", pushSuccess, crowdPackId, packId, pushTime, operateAgent,priority,serialId);
		}else{
			//群发失败记录状态为3，表示推送成功，但是群发失败，不更新人群包里面的状态
			dealUser(status==3 ? "3":"2", pushSuccess, crowdPackId, packId, pushTime, operateAgent,priority,serialId);
		}
		dealUser("2", pushError, crowdPackId, packId, pushTime, operateAgent,priority,serialId);
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>结束处理本此用户情况");
	}

	/**
	 * 删除人群包
	 * @param crowdPackId 人群包ID
	 * @return
	 * @throws Exception
	 */
	private JSONObject deleteCrowdPack(String crowdPackId) throws Exception{
		JSONObject infParam = new JSONObject();
		infParam.put("crowdPackId", crowdPackId);
		//JSONObject infResult = wechatWorkService.deleteCrowdPack(infParam);
		JSONObject obj = new JSONObject();
		obj.put("command","deleteCrowdPack");
		obj.put("params", infParam);
		JSONObject infResult = doIservice(obj);
		return infResult;
	}

	/**
	 * 处理素材信息
	 * @param material 素材集合
	 * @param serialId 流水ID
	 * @return
	 */
	private JSONObject dealMaterial(JSONArray material,long serialId,String packId){
		JSONObject result = new JSONObject();
		try{
			if(material==null || material.size()<0){
				//推送素材库
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealMaterial["+serialId+"]=====无素材信息");
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				result.put("respDesc", "无素材信息！");
				return result;
			}

			JSONArray infParam = new JSONArray();
			String url = Constants.getSystemExtranetUrl();
			for(int i = 0;i<material.size();i++){
				JSONObject materialObj = material.getJSONObject(i);
				JSONObject infObj = new JSONObject();
				if(StringUtils.equals(materialObj.getString("M_TYPE"), "1")){
					//图片
					infObj.put("msgType", "image");
					infObj.put("fileTitle", materialObj.getString("M_FILENAME"));
					infObj.put("fileUrl", url+materialObj.getString("M_FILEPATH"));
					infObj.put("fileSize", materialObj.getString("FILE_SIZE"));
				}else if(StringUtils.equals(materialObj.getString("M_TYPE"), "2")){
					//图文
					infObj.put("msgType", "news");
					infObj.put("newsDesc", materialObj.getString("M_CONTENT"));
					infObj.put("coverUrl", url+materialObj.getString("M_FILEPATH"));
					infObj.put("fileTitle", materialObj.getString("M_TITLE"));
					infObj.put("fileUrl",materialObj.getString("M_LINK"));
					infObj.put("fileSize", materialObj.getString("FILE_SIZE"));
				}else if(StringUtils.equals(materialObj.getString("M_TYPE"), "3")){
					//视频
					infObj.put("msgType", "video");
					infObj.put("fileTitle", materialObj.getString("M_FILENAME"));
					infObj.put("fileUrl", url+materialObj.getString("M_FILEPATH"));
					infObj.put("fileSize", materialObj.getString("FILE_SIZE"));
				}else if(StringUtils.equals(materialObj.getString("M_TYPE"), "6")){
					//小程序
					infObj.put("msgType", "miniprogram");
					infObj.put("appId", materialObj.getString("M_APPID"));
					infObj.put("coverUrl", url+materialObj.getString("M_FILEPATH"));
					infObj.put("fileTitle", materialObj.getString("M_TITLE"));
					infObj.put("fileUrl", materialObj.getString("M_LINK"));
					infObj.put("fileSize", materialObj.getString("FILE_SIZE"));
				}
				infParam.add(infObj);
			}
			if(infParam==null || infParam.size()<=0){
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealMaterial["+serialId+"]=====无素材信息");
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				result.put("respDesc", "无素材信息！");
				return result;
			}
			//JSONObject infResult = wechatWorkService.uploadMaterial(infParam);
			JSONObject obj = new JSONObject();
			obj.put("command","uploadMaterial");
			JSONObject param = new JSONObject();
			param.put("arr", infParam);
			obj.put("params", param);
			JSONObject infResult = doIservice(obj);
			if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "素材上传失败！");
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealMaterial["+serialId+"]=====调用素材接口异常："+infResult);
				return result;
			}
			JSONArray data = new JSONArray();
			if(infResult.getJSONObject("respData")!=null){
				data = infResult.getJSONObject("respData").getJSONObject("data").getJSONArray("fileIds");
			}
			result.put("respData", data);
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "处理完成");
			return result;
		}catch (Exception e) {
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).error("WechatWorkStrategyService.dealMaterial["+serialId+"]=====素材上传处理失败："+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "素材上传处理失败！");
			return result;
		}
	}

	/**
	 * 处理人群包信息，并保存企微发送记录
	 * @param strategys 策略对象
	 * @param packId 人群包ID
	 * @param packName 人群包名称
	 * @param serialId 流水ID
	 * @return
	 * @throws Exception
	 */
	public JSONObject saveRecord(JSONObject strategys,String crowdPackId ,String packId ,String packName,long serialId) throws Exception{
		String pushTime = DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss");
		//处理人群包信息
		JSONObject crowdPackJSON = dealCrowdPack(packId , packName, crowdPackId , serialId,
				strategys);
		JSONObject data = crowdPackJSON.getJSONObject("respData");
		String strategyId = strategys.getString("ID");
		//保存发送记录
		EasyRecord record =  new EasyRecord("C_NO_AS_WECHAT_SEND_RECORD", "ID");
		record.put("ID", crowdPackId);
		record.put("TRIGGER_TYPE", strategys.getString("TRIGGER_TYPE"));
		record.put("CROWD_PACK_ID", packId);
		record.put("CROWD_PACK_NAME", packName);
		record.put("CROWD_PACK_GROUP_ID", strategys.getString("CROWD_PACK_GROUP_ID"));
		record.put("STRATEGY_ID", strategyId);
		record.put("PUSH_USER_NUM", data.getInteger("pushUserNum"));
		record.put("CREATE_TIME", DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss"));
		record.put("DATE_ID", DateUtil.getCurrentDateStr("yyyyMMdd"));
		String createAcc = strategys.getString("OPERATE_AGENT");
		if(StringUtils.isEmpty(createAcc)){
			createAcc = "system";
		}
		record.put("CREATE_ACC", createAcc);
		record.put("PUSH_USER_TIME", pushTime);
		record.put("PUSH_SUCC_NUM", data.getJSONArray("pushSuccess")!=null ? data.getJSONArray("pushSuccess").size():0);
		record.put("IS_GET_RESULT", 2);
		//发送状态：1无人群信息，2创建人群包失败，3处理成功，4人群处理失败
		int status = data.getIntValue("dealStatus");
		if(status==4){
			//处理人群信息失败，进行删除人群包
			JSONObject infResult = deleteCrowdPack(crowdPackId);
			if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====删除人群包失败："+infResult);
				status = 7;
			}
		}
		record.put("CROWD_PACK_STATUS", status);
		getQuery().save(record);

		//处理推送用户信息
		//dealUser(crowdPackJSON, strategys, crowdPackId, packId, pushTime, true);
		return crowdPackJSON;
	}
	public static void main(String[] args) {
		Date date = DateUtil.getDate("yyyy-MM-dd", "2022-07-21 12:00:00");
		System.out.println(DateUtil.getDateStr(date));
		new FileChangedExclusiveReadLockStrategy();
	}

	/**
	 * 发送创建人群包，推送人群包成员等操作
	 * @param packId 人群包ID
	 * @param packName 人群包名称
	 * @param crowdPackId 人群包批次ID
	 * @param serialId 流水ID
	 * @return
	 */
	public JSONObject dealCrowdPack1(String packId ,String packName,String crowdPackId,long serialId,JSONObject strategys) throws Exception{
		String orgCode = strategys.getString("ORG_CODE");
		String brandCode = strategys.getString("BRAND_CODE");
		String prodCode = strategys.getString("PROD_CODE");
		String faultCode = strategys.getString("FAULT_CODE");
		String strategyStartTime = strategys.getString("EFFECT_START_TIME");
		String strategyEndTime = strategys.getString("EFFECT_END_TIME");
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====人群信息处理开始：packId:"+packId+",packName:"+packName+",crowdPackId:"+crowdPackId);
		JSONObject result = new JSONObject();
		//获取人群包人群信息
		JSONObject param = new JSONObject();
		int pageNo = 1;
		int pageSize = Constants.getWechatPushCrowUserNum();
		param.put("pageType", 3);
		param.put("pageIndex", pageNo);
		param.put("pageSize", pageSize);
		com.yunqu.cc.activeService.utils.EasySQL easySQL = new com.yunqu.cc.activeService.utils.EasySQL("select t1.* from C_NO_AS_CROWD t1");
		easySQL.append("where 1=1");
		easySQL.append(packId,"and t1.CROWD_PACK_ID=?");
		//添加用户状态筛选，已经处理成功的不再重复处理
		easySQL.append(1,"and t1.SERVICE_STATUS=?");
		//免打扰状态
		easySQL.append(0,"and t1.IS_DISTURB=?");
		easySQL.append(strategyStartTime,"and t1.CREATE_TIME>=?");
		easySQL.append(strategyEndTime," and t1.CREATE_TIME<=?");
		//筛选人群包的品牌品类信息,以及障碍码信息
		easySQL.append(orgCode,"and t1.ORG_CODE=?");
		//easySQL.append(brandCode,"and t1.BRAND_CODE=?");
		//easySQL.append(prodCode,"and t1.PROD_CODE=?");
		String[] brandCodes = brandCode.split(",");
		if(brandCodes.length==1){
			easySQL.append(brandCode,"and t1.BRAND_CODE=?");
		}else if(brandCodes.length>1){
			easySQL.appendIn(brandCodes,"and t1.BRAND_CODE");
		}

		String[] prodCodes = prodCode.split(",");
		if(prodCodes.length==1){
			easySQL.append(prodCode,"and t1.PROD_CODE=?");
		}else if(prodCodes.length>1){
			easySQL.appendIn(prodCodes,"and t1.PROD_CODE");
		}

		easySQL.append(faultCode,"and t1.FAULT_CODE=?");
		//判断免打扰配置表是否存在改数据
		easySQL.append("and not exists(select * from C_NO_AS_NOT_DISTURB where CUST_PHONE =t1.CUST_PHONE)");
		//
		if(StringUtils.equals(strategys.getString("TIME_TYPE"), "3")){
			int minute = strategys.getIntValue("REAL_TIME_MINUTE");
			String time = strategys.getString("strategyTime");
			//根据当前是否计算间隔时间前的时间
			String times = DateUtil.addMinute("yyyy-MM-dd HH:mm:ss", time, -minute);
			easySQL.append(times,"and t1.CREATE_TIME<=?");
		}

		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]："+easySQL.getSQL()+"--params:"+JSON.toJSONString(easySQL.getParams()));
		EasyQuery easyQuery = getQuery();
		easyQuery.setMaxRow(pageSize);
		JSONObject crows = queryForPageList(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl(), false, param, easyQuery);
		int total = crows.getInteger("totalRow");
		if(crows==null || total<=0){
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====无人群信息");
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理失败，未查询到人群信息");

			JSONObject respData = new JSONObject();
			respData.put("dealStatus", 1);
			result.put("respData", respData);
			return result;
		}

		//推送失败的人群信息
		JSONArray pushError = new JSONArray();
		//推送成功人群
		JSONArray pushSuccess = new JSONArray();
		//根据查询的用户创建人群包
		JSONObject infParam = new JSONObject();
		infParam.put("crowdPackId", crowdPackId);
		infParam.put("crowdPackName", packName);
		infParam.put("customerCount", total);
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====开始创建人群包：total:"+total);
		//JSONObject infResult = wechatWorkService.addCrowdPack(infParam);
		JSONObject obj = new JSONObject();
		obj.put("command","addCrowdPack");
		obj.put("params", infParam);
		JSONObject infResult = doIservice(obj);
		if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====创建人群包失败："+infResult);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "创建人群包失败！");
			JSONObject respData = new JSONObject();
			respData.put("pushUserNum", total);
			respData.put("dealStatus", 2);
			result.put("respData", respData);
			return result;
		}


		int pageTotal = crows.getIntValue("totalPage");
		//记录所有请求失败的JSON,后续做一次失败兼容
		JSONArray infError = new JSONArray();
		do{
			JSONArray users = crows.getJSONArray("data");
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====处理第"+pageNo+"批人群信息("+users.size()+")......");
			infParam = new JSONObject();
			infParam.put("crowdPackId", crowdPackId);
			infParam.put("totalPage", pageTotal);
			infParam.put("pageNo", pageNo);
			infParam.put("pageSize", pageSize);
			List<String> customerMobiles = new ArrayList<>();
			for (int i = 0;i<users.size();i++) {
				JSONObject user = users.getJSONObject(i);
				customerMobiles.add(user.getString("CUST_PHONE"));
			}
			infParam.put("customerMobiles", customerMobiles);
			//infResult = wechatWorkService.pushCrowdPack(infParam);
			obj = new JSONObject();
			obj.put("command","pushCrowdPack");
			obj.put("params", infParam);
			infResult = doIservice(obj);
			if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====推送人员失败："+infResult);
				pushError.addAll(users);
			}else{
				pushSuccess.addAll(users);
				//存储失败用户JSON和请求。后续重试是使用
				JSONObject infErrorJson = new JSONObject();
				infErrorJson.put("infParam", infParam);
				infErrorJson.put("users", users);
				infError.add(infErrorJson);
			}
			pageNo = pageNo + 1;
			param.put("pageIndex", pageNo);
			crows = queryForPageList(easySQL.getSQL(), easySQL.getParams(), new JSONMapperImpl(), false, param, easyQuery);
		}while(pageNo < (pageTotal+1));

		infParam = new JSONObject();
		infParam.put("crowdPackId", crowdPackId);
		//infResult = wechatWorkService.queryCrowdPack(infParam);
		obj = new JSONObject();
		obj.put("command","queryCrowdPack");
		obj.put("params", infParam);
		infResult = doIservice(obj);
		if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====查询推送状态失败："+infResult);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "人群包处理失败！");
			JSONObject respData = new JSONObject();
			respData.put("pushUserNum", total);
			respData.put("dealStatus", 4);
			result.put("respData", respData);
			return result;
		}
		int importStatus = 4;
		JSONObject data = infResult.getJSONObject("respData").getJSONObject("data");
		if(data!=null && (StringUtils.equals(data.getString("importStatus"),"1") || StringUtils.equals(data.getString("importStatus"),"2"))){
			pushSuccess.clear();
			//infError
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====推送状态为待引入，或引入中重新推送失败用户");
			for(int i =0;i<infError.size();i++){
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====处理第"+i+"批失败人群信息......");
				JSONObject infErrorJson = infError.getJSONObject(i);
				//infResult = wechatWorkService.pushCrowdPack(infErrorJson.getJSONObject("infParam"));
				obj = new JSONObject();
				obj.put("command","pushCrowdPack");
				obj.put("params", infErrorJson.getJSONObject("infParam"));
				infResult = doIservice(obj);
				if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
					CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====推送人员失败："+infResult);
					pushError.addAll(infErrorJson.getJSONArray("users"));
				}else{
					pushSuccess.addAll(infErrorJson.getJSONArray("users"));
				}
			}
			infParam = new JSONObject();
			infParam.put("crowdPackId", crowdPackId);
			//infResult = wechatWorkService.queryCrowdPack(infParam);
			obj = new JSONObject();
			obj.put("command","queryCrowdPack");
			obj.put("params", infParam);
			infResult = doIservice(obj);
			if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====查询推送状态失败："+infResult);
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "人群包处理失败！");
				JSONObject respData = new JSONObject();
				respData.put("pushUserNum", total);
				respData.put("dealStatus", 4);
				result.put("respData", respData);
				return result;
			}
			data = infResult.getJSONObject("respData").getJSONObject("data");
			if(data!=null && (StringUtils.equals(data.getString("importStatus"),"3"))){
				importStatus = 3;
			}else{
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====推送状态为待引入，或引入中");
			}
		}else if(data!=null && StringUtils.equals(data.getString("importStatus"),"3")){
			importStatus = 3;
		}

		JSONObject respData = new JSONObject();
		respData.put("pushError", pushError);
		respData.put("pushSuccess", pushSuccess);
		respData.put("pushUserNum", total);
		respData.put("dealStatus", importStatus);
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====人群信息处理完成：推送失败用户"
				+pushError.size()+",推送成功用户："+pushSuccess.size()+",处理总人数："+total+",处理状态："+importStatus);
		result.put("respData", respData);
		result.put("respDesc", "处理完成");
		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		if(importStatus==4){
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理失败");
		}
		return result;

	}

	public JSONObject dealCrowdPack(String packId ,String packName,String crowdPackId,long serialId,JSONObject strategys) throws Exception{
		String orgCode = strategys.getString("ORG_CODE");
		String brandCode = strategys.getString("BRAND_CODE");
		String prodCode = strategys.getString("PROD_CODE");
		String faultCode = strategys.getString("FAULT_CODE");
		String strategyStartTime = strategys.getString("EFFECT_START_TIME");  // 策略开始时间
		String strategyEndTime = strategys.getString("EFFECT_END_TIME");  	  // 策略结束时间
		// 策略配置生效时间不再当前时间范围内跳过本次执行
		{
			Date now = new Date();
			Date effStart = DateUtil.getDate("yyyy-MM-dd HH:mm:ss", strategyStartTime);
			Date effEnd = DateUtil.getDate("yyyy-MM-dd HH:mm:ss", strategyEndTime);
			if (effStart != null && effEnd != null && (now.before(effStart) || now.after(effEnd))) {
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info(
						"WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====策略未在生效期内，跳过执行："+strategyStartTime+" ~ "+strategyEndTime);
				JSONObject result = new JSONObject();
				JSONObject respData = new JSONObject();
				respData.put("pushError", new JSONArray());
				respData.put("pushSuccess", new JSONArray());
				respData.put("pushUserNum", 0);
				respData.put("dealStatus", 1); // 与“无人群信息”一致的处理分支，外层会安全退出
				result.put("respData", respData);
				result.put("respDesc", "策略未在生效期内，跳过执行");
				// 返回非成功码以便上层停止后续流程（素材/推送等），但结构完整便于后续处理
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				return result;
			}
		}
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====人群信息处理开始：packId:"+packId+",packName:"+packName+",crowdPackId:"+crowdPackId);
		JSONObject result = new JSONObject();
		//获取人群包人群信息
		int pageNo = 1;
		int pageSize = 3000;
		com.yunqu.cc.activeService.utils.EasySQL easySQL = new com.yunqu.cc.activeService.utils.EasySQL("select t1.* from C_NO_AS_CROWD t1");
		easySQL.append("where 1=1");
		easySQL.append(packId,"and t1.CROWD_PACK_ID=?");
		//添加用户状态筛选，已经处理成功的不再重复处理
		easySQL.append(1,"and t1.SERVICE_STATUS=?");
		//免打扰状态
		easySQL.append(0,"and t1.IS_DISTURB=?");
		//20230821：调整SQL查询条件，按create_date进行过滤，该字段对应的索引查询更快
		//easySQL.append(strategyStartTime,"and t1.CREATE_TIME>=?");
		//easySQL.append(strategyEndTime," and t1.CREATE_TIME<=?");
		String beginDateInt = strategyStartTime.substring(0,10).replace("-","");
		String endDateInt = strategyEndTime.substring(0,10).replace("-","");
		easySQL.append(beginDateInt,"and t1.CREATE_DATE>=?");
		easySQL.append(endDateInt," and t1.CREATE_DATE<=?");
		Date begin = DateUtil.getDate("yyyy-MM-dd HH:mm:ss", strategyStartTime);
		Date end = DateUtil.getDate("yyyy-MM-dd HH:mm:ss", strategyEndTime);
		//筛选人群包的品牌品类信息,以及障碍码信息
		easySQL.append(orgCode,"and t1.ORG_CODE=?");
		//easySQL.append(brandCode,"and t1.BRAND_CODE=?");
		//easySQL.append(prodCode,"and t1.PROD_CODE=?");
		String[] brandCodes = brandCode.split(",");
		if(brandCodes.length==1){
			easySQL.append(brandCode,"and t1.BRAND_CODE=?");
		}else if(brandCodes.length>1){
			easySQL.appendIn(brandCodes,"and t1.BRAND_CODE");
		}
		String[] prodCodes = prodCode.split(",");
		if(prodCodes.length==1){
			easySQL.append(prodCode,"and t1.PROD_CODE=?");
		}else if(prodCodes.length>1){
			easySQL.appendIn(prodCodes,"and t1.PROD_CODE");
		}
		easySQL.append(faultCode,"and t1.FAULT_CODE=?");
		//判断免打扰配置表是否存在改数据
//		easySQL.append("and not exists(select * from C_NO_AS_NOT_DISTURB where CUST_PHONE =t1.CUST_PHONE)");
		//
		easySQL.append("and not exists(SELECT 1 FROM C_NO_AS_SENSITIVE_CROWD TT2 " +
				"JOIN C_NO_AS_CROWD_PACK TT4 ON TT2.CROWD_PACK_ID = TT4.ID WHERE 1 = 1 ");
		easySQL.append(" AND TT2.PHONE = t1.CUST_PHONE ");
		easySQL.append("QWQD", " AND TT4.BG_CROWD_CODE = ? ");
		easySQL.append(")");
		Date timeDate = null;
		if(StringUtils.equals(strategys.getString("TIME_TYPE"), "3")) {
			int minute = strategys.getIntValue("REAL_TIME_MINUTE");
			String time = strategys.getString("strategyTime");
			//根据当前是否计算间隔时间前的时间
			String times = DateUtil.addMinute("yyyy-MM-dd HH:mm:ss", time, -minute);
			//easySQL.append(times,"and t1.CREATE_TIME<=?");
			String timesInt = times.substring(0, 10).replace("-", "");
			easySQL.append(timesInt, " and t1.CREATE_DATE<=?");
			timeDate = DateUtil.getDate("yyyy-MM-dd HH:mm:ss", times);
		}
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]："+easySQL.getSQL()+"--params:"+JSON.toJSONString(easySQL.getParams()));
		EasyQuery easyQuery = getQuery();
		easyQuery.setMaxRow(pageSize);

		JSONArray dealUser = new JSONArray();
		List<JSONObject> crows;
		do{
			Date finalTimeDate = timeDate;
			crows = easyQuery.queryForList(easySQL.getSQL(), easySQL.getParams(), pageNo, pageSize, new EasyRowMapper<JSONObject>() {
				@Override
				public JSONObject mapRow(ResultSet rs, int convertField) {
					try{
						String createTime =rs.getString("CREATE_TIME");
						Date createDate = DateUtil.getDate("yyyy-MM-dd HH:mm:ss", createTime);
						if(createDate.getTime()<begin.getTime() || createDate.getTime()>end.getTime() ||
								(finalTimeDate !=null && createDate.getTime() > finalTimeDate.getTime())){
							CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====不满足策略进行过滤：" + createDate.getTime()+","+begin.getTime()+","+end.getTime()+(finalTimeDate!=null?","+finalTimeDate.getTime():""));
							return null;
						}
						ResultSetMetaData meta = rs.getMetaData();
						int columnCount = meta.getColumnCount();
						String[] column = new String[columnCount];

						for(int i = 0; i < columnCount; ++i) {
							column[i] = meta.getColumnLabel(i + 1).toUpperCase();
						}
						JSONObject row = new JSONObject(true);
						for(int i = 0; i < columnCount; ++i) {
							String value = rs.getString(i + 1);
							if (value == null || "".equals("null")) {
								value = "";
							}
							row.put(column[i], value);
						}
						dealUser.add(row);
						return row;
					}catch (Exception e){
						return null;
					}
				}
			});
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====查询第"+pageNo+"页人群信息("+crows.size()+","+dealUser.size()+")......");
			pageNo = pageNo + 1;
		}while(CommonUtil.listIsNotNull(crows) && crows.size() == pageSize);

		if(dealUser==null || dealUser.size()<=0){
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====无人群信息");
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理失败，未查询到人群信息");

			JSONObject respData = new JSONObject();
			respData.put("dealStatus", 1);
			result.put("respData", respData);
			return result;
		}
		int total = dealUser.size();
		//推送失败的人群信息
		JSONArray pushError = new JSONArray();
		//推送成功人群
		JSONArray pushSuccess = new JSONArray();
		//根据查询的用户创建人群包
		JSONObject infParam = new JSONObject();
		infParam.put("crowdPackId", crowdPackId);
		infParam.put("crowdPackName", packName);
		infParam.put("customerCount", total);
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====开始创建人群包：total:"+total);
		//JSONObject infResult = wechatWorkService.addCrowdPack(infParam);
		JSONObject obj = new JSONObject();
		obj.put("command","addCrowdPack");
		obj.put("params", infParam);
		JSONObject infResult = doIservice(obj);
		if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====创建人群包失败："+infResult);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "创建人群包失败！");
			JSONObject respData = new JSONObject();
			respData.put("pushUserNum", total);
			respData.put("dealStatus", 2);
			result.put("respData", respData);
			return result;
		}
		JSONArray users = new JSONArray();
		List<String> customerMobiles = new ArrayList<>();
		pageNo = 1;
		pageSize = Constants.getWechatPushCrowUserNum();
		int pageTotal = (dealUser.size()/pageSize)+((dealUser.size()%pageSize)!=0?1:0);
		//记录所有请求失败的JSON,后续做一次失败兼容
		JSONArray infError = new JSONArray();
		for (int i = 1; i <= dealUser.size(); i++) {
			JSONObject user = dealUser.getJSONObject(i-1);
			users.add(user);
			customerMobiles.add(user.getString("CUST_PHONE"));
			if (i % pageSize == 0 || i == dealUser.size()) {
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====处理第"+pageNo+"批人群信息("+users.size()+")......");
				infParam = new JSONObject();
				infParam.put("crowdPackId", crowdPackId);
				infParam.put("totalPage", pageTotal);
				infParam.put("pageNo", pageNo);
				infParam.put("pageSize", pageSize);
				infParam.put("customerMobiles", customerMobiles);
				//infResult = wechatWorkService.pushCrowdPack(infParam);
				obj = new JSONObject();
				obj.put("command","pushCrowdPack");
				obj.put("params", infParam);
				infResult = doIservice(obj);
				if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
					CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====推送人员失败："+infResult);
					pushError.addAll(users);
				}else{
					pushSuccess.addAll(users);
					//存储失败用户JSON和请求。后续重试是使用
					JSONObject infErrorJson = new JSONObject();
					infErrorJson.put("infParam", infParam);
					infErrorJson.put("users", users);
					infError.add(infErrorJson);
				}
				users = new JSONArray();
				customerMobiles = new ArrayList<>();
				pageNo = pageNo + 1;
			}
		}
		//清楚内存中的数据
		users.clear();
		customerMobiles.clear();
		dealUser.clear();
		infParam = new JSONObject();
		infParam.put("crowdPackId", crowdPackId);
		//infResult = wechatWorkService.queryCrowdPack(infParam);
		obj = new JSONObject();
		obj.put("command","queryCrowdPack");
		obj.put("params", infParam);
		infResult = doIservice(obj);
		if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====查询推送状态失败："+infResult);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "人群包处理失败！");
			JSONObject respData = new JSONObject();
			respData.put("pushUserNum", total);
			respData.put("dealStatus", 4);
			result.put("respData", respData);
			return result;
		}
		int importStatus = 4;
		JSONObject data = infResult.getJSONObject("respData").getJSONObject("data");
		if(data!=null && (StringUtils.equals(data.getString("importStatus"),"1") || StringUtils.equals(data.getString("importStatus"),"2"))){
			pushSuccess.clear();
			//infError
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====推送状态为待引入，或引入中重新推送失败用户");
			for(int i =0;i<infError.size();i++){
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====处理第"+i+"批失败人群信息......");
				JSONObject infErrorJson = infError.getJSONObject(i);
				//infResult = wechatWorkService.pushCrowdPack(infErrorJson.getJSONObject("infParam"));
				obj = new JSONObject();
				obj.put("command","pushCrowdPack");
				obj.put("params", infErrorJson.getJSONObject("infParam"));
				infResult = doIservice(obj);
				if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
					CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====推送人员失败："+infResult);
					pushError.addAll(infErrorJson.getJSONArray("users"));
				}else{
					pushSuccess.addAll(infErrorJson.getJSONArray("users"));
				}
			}
			infParam = new JSONObject();
			infParam.put("crowdPackId", crowdPackId);
			//infResult = wechatWorkService.queryCrowdPack(infParam);
			obj = new JSONObject();
			obj.put("command","queryCrowdPack");
			obj.put("params", infParam);
			infResult = doIservice(obj);
			if(infResult==null || !StringUtils.equals(infResult.getString("respCode"), GWConstants.RET_CODE_SUCCESS)){
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====查询推送状态失败："+infResult);
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "人群包处理失败！");
				JSONObject respData = new JSONObject();
				respData.put("pushUserNum", total);
				respData.put("dealStatus", 4);
				result.put("respData", respData);
				return result;
			}
			data = infResult.getJSONObject("respData").getJSONObject("data");
			if(data!=null && (StringUtils.equals(data.getString("importStatus"),"3"))){
				importStatus = 3;
			}else{
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====推送状态为待引入，或引入中");
			}
		}else if(data!=null && StringUtils.equals(data.getString("importStatus"),"3")){
			importStatus = 3;
		}

		JSONObject respData = new JSONObject();
		respData.put("pushError", pushError);
		respData.put("pushSuccess", pushSuccess);
		respData.put("pushUserNum", total);
		respData.put("dealStatus", importStatus);
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealCrowdPack["+serialId+"]=====人群信息处理完成：推送失败用户"
				+pushError.size()+",推送成功用户："+pushSuccess.size()+",处理总人数："+total+",处理状态："+importStatus);
		result.put("respData", respData);
		result.put("respDesc", "处理完成");
		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		if(importStatus==4){
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理失败");
		}
		return result;
	}
	/**
	 * 处理推送后的用户
	 * @param type 1推送成功，2推送失败
	 * @param users 用户集合
	 * @param sendId 发送记录ID
	 * @param packId 人群包ID
	 * @param pushTime 推送时间
	 * @throws Exception
	 */
	private void dealUser(String type ,JSONArray users,String sendId,String packId,String pushTime,String createAcc,String priority,long serialId) throws Exception{
		if(users==null || users.size()<=0){
			return;
		}
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>开始处理本批用户明细:" + users.size());
		/*JSONObject detail = new JSONObject();
		detail.put("SEND_ID", sendId);
		detail.put("CROWD_PACK_ID", packId);
		detail.put("CREATE_TIME", DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss"));
		detail.put("CREATE_ACC", createAcc);
		detail.put("PUSH_TIME", pushTime);
		detail.put("IS_PUSH", type);
		detail.put("IS_SEND", 0);
		detail.put("IS_CLICK", 2);
		detail.put("ID_DEAL", 2);*/
		//detail.put("STRATEGY_ID", strategyId);
		String insertSql = "insert into C_NO_AS_WECHAT_SEND_DETAIL"
				+ "(ID,SEND_ID,CUST_PHONE,CUST_ID,CUST_NAME,CUST_ADDRESS,ORG_CODE,ORG_NAME,BRAND_CODE,BRAND_NAME,PROD_CODE,PROD_NAME"
				+ ",CROWD_PACK_ID,CREATE_TIME,CREATE_ACC,PUSH_TIME,IS_PUSH,IS_SEND,IS_CLICK,ID_DEAL) "
				+ "values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

		String updateSql = "update C_NO_AS_CROWD set SERVICE_STATUS=?,PRIORITY=? where ID=?";

		List<Object[]> insertParams = new ArrayList<Object[]>();
		List<Object[]> updateParams = new ArrayList<Object[]>();
		for(int i = 0 ;i<users.size();i++){
			JSONObject user = users.getJSONObject(i);
			/*EasyRecord record =  new EasyRecord("C_NO_AS_WECHAT_SEND_DETAIL", "ID");
			record.putAll(detail);
			record.put("ID", RandomKit.randomStr());
			record.put("CUST_PHONE", user.getString("CUST_PHONE"));
			record.put("CUST_ID", user.getString("ID"));
			record.put("CUST_NAME", user.getString("CUST_NAME"));
			record.put("CUST_ADDRESS", user.getString("CUST_ADDRESS"));
			//record.put("CUST_LEVEL", user.getString("CUST_PHONE"));
			//record.put("CUST_POINTS", user.getString("CUST_PHONE"));
			record.put("ORG_CODE", user.getString("ORG_CODE"));
			record.put("ORG_NAME", user.getString("ORG_NAME"));
			record.put("BRAND_CODE", user.getString("BRAND_CODE"));
			record.put("BRAND_NAME", user.getString("BRAND_NAME"));
			record.put("PROD_CODE", user.getString("PROD_CODE"));
			record.put("PROD_NAME", user.getString("PROD_NAME"));
			getQuery().save(record);*/
			String name = user.getString("CUST_NAME");
			if(StringUtils.isNotBlank(name) && name.length()>20) {
				//防止客户名字字段过长，进行截取，按oracle数据库1个中文占3个字节，截取前20个字符
				CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>人群名称过长："+name);
				name = name.substring(0, 20);
			}
			Object[] insertParam = new Object[]{RandomKit.randomStr(),sendId,user.getString("CUST_PHONE"),user.getString("ID"), name,
					user.getString("CUST_ADDRESS"),user.getString("ORG_CODE"),user.getString("ORG_NAME"),user.getString("BRAND_CODE"),user.getString("BRAND_NAME"),
					user.getString("PROD_CODE"),user.getString("PROD_NAME"),packId,DateUtil.getCurrentDateStr("yyyy-MM-dd HH:mm:ss"),createAcc,
					pushTime,type,0,2,2};
			insertParams.add(insertParam);

			//推送成功，更新用户表的状态
			if(StringUtils.equals(type, "1")){
				/*EasyRecord userRecord=  new EasyRecord("C_NO_AS_CROWD", "ID");
				userRecord.put("ID", user.getString("ID"));
				userRecord.put("SERVICE_STATUS", "5");
				userRecord.put("PRIORITY", priority);
				getQuery().update(userRecord);*/
				Object[] updateParam = new Object[]{"5",priority,user.getString("ID")};
				updateParams.add(updateParam);
			}
		}
		try {
			if (insertParams != null && insertParams.size() > 0) {
				getQuery().executeBatch(insertSql, insertParams);
			}
		} catch (Exception e) {
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>处理本批用户明细异常："+e.getMessage(), e);
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).error("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>处理本批用户明细异常："+e.getMessage(), e);
		}
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>结束处理本批用户明细");
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>开始更新本批用户状态");
		try {
			if (updateParams != null && updateParams.size() > 0) {
				getQuery().executeBatch(updateSql, updateParams);
			}
		} catch (Exception e) {
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>更新本批用户状态异常："+e.getMessage(), e);
			CommLogger.getCommLogger("wechatWorkStrategy-"+packId).error("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>更新本批用户状态异常："+e.getMessage(), e);
		}
		CommLogger.getCommLogger("wechatWorkStrategy-"+packId).info("WechatWorkStrategyService.dealUser["+serialId+"]====>>>>结束更新本批用户状态");
	}

	/**
	 * 获取数据源
	 */
	public EasyQuery getQuery() {
		return EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
	}


	@SuppressWarnings({ "unchecked", "rawtypes" })
	private JSONObject queryForPageList(String sql, Object[] params, EasyRowMapper<?> rowMapper, boolean emptyPage,JSONObject param,EasyQuery easyQuery) {
		if (rowMapper == null) {
			rowMapper = new MapRowMapperImpl();
		}
		int pageIndex = -1;
		int pageSize = 10;
		int total = -1;
		int pageType = 1;
		try {
			pageType = param.getIntValue("pageType");
			pageType = pageType == 0 ? 1 : pageType;
			pageIndex = param.getIntValue("pageIndex");
			pageSize = param.getIntValue("pageSize");
			pageSize = pageSize == 0 ? 1 : pageSize;
		} catch (Exception var12) {
			logger.warn("无法获取到当前分页的数据，对于Dao对象查询来说，[pageIndex,pageSize]这两个参数是必须的!", var12);
		}

		JSONObject resultJson = new JSONObject();
		Object list = new ArrayList();
		try {
			if (emptyPage) {
				total = 0;
			} else {
				if (pageIndex < 0 && pageType == 1 || pageType > 2) {
					String countSql = "select count(1) from (" + sql + ") temp";
					total = easyQuery.queryForInt(countSql, params);
				}
				list = easyQuery.queryForList(sql, params, pageIndex, pageSize, (EasyRowMapper) rowMapper);
			}
		} catch (SQLException var13) {
			logger.error("DaoContext.queryForPageList()->处理查询结果错误，原因：" + var13.getMessage(), var13);
			resultJson.put("msg", var13.getMessage());
			resultJson.put("state", 0);
			return resultJson;
		}

		if (pageType != 2) {
			resultJson.put("totalRow", total);
			resultJson.put("totalPage", (total + (pageSize - 1)) / pageSize);
		}

		resultJson.put("msg", "请求成功!");
		resultJson.put("state", 1);
		resultJson.put("pageSize", pageSize);
		resultJson.put("pageNumber", pageIndex);
		resultJson.put("data", list);
		resultJson.put("pageType", pageType);
		return resultJson;
	}

	private JSONObject doIservice(JSONObject obj){
		JSONObject result = new JSONObject();
		try {
			IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
			result.put("respDesc", "IService请求异常");
		}
		return result;
	}

}
