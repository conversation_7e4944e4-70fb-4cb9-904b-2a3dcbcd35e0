package com.yunqu.cc.consumer.base;

import com.yq.busi.common.util.ConfigUtil;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_NAME = "yw-ds";     //默认数据源名称

	public final static String MARS_DS_NAME = "mars-ds";     //Mars数据源

	public final static String STAT_DS_NAME = "stat-ds";     //Mars数据源

	public final static String APP_NAME = "cc-consumer";     //应用

	public static final String START_COMSUMER_FLAG = "Y";

	public static String getMqAddr() {
		return "";
	}

	/**
	 * 获取本地队列容量
	 */
	public static int getLocalQueueCapacity() {
		return Integer.parseInt(ConfigUtil.getString(APP_NAME, "LOCAL_QUEUE_CAPACITY"));
	}

	/**
	 * 获取本地队列消费者线程数量
	 */
	public static int getLocalQueueConsumerThreadNum() {
		return Integer.parseInt(ConfigUtil.getString(APP_NAME, "LOCAL_QUEUE_CONSUMER_SIZE"));
	}

	/**
	 * 企微-企业id
	 */
	public static String  getEnterpriseWechatId() {
		return ConfigUtil.getString(APP_NAME,"ENTERPRISE_WECHAT_ID","100");
	}
}
