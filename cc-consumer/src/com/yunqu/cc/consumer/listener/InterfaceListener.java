package com.yunqu.cc.consumer.listener;

import com.yunqu.cc.consumer.base.Constants;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;
import javax.servlet.annotation.WebListener;
import java.util.ArrayList;
import java.util.List;


@WebListener
public class InterfaceListener extends ServiceContextListener{

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {
		
		List<ServiceResource> list = new ArrayList<ServiceResource>();
		ServiceResource sensitiveService = new ServiceResource();
		sensitiveService.appName = Constants.APP_NAME;
		sensitiveService.className = "com.yunqu.cc.consumer.inf.SensitiveService";
		sensitiveService.description = "敏感词监控接口";
		sensitiveService.serviceId = "CONSUMER_SENSITIVE_WORD";
		sensitiveService.serviceName = "敏感词监控接口";
		list.add(sensitiveService);

		return list;
	}

}
