package com.yunqu.cc.consumer.service.queue.local;

import com.yunqu.cc.consumer.base.CommonLogger;
import com.yunqu.cc.consumer.service.queue.local.impl.SensitiveWordQueue;
import org.apache.log4j.Logger;

/**
 * 队列管理器，用于管理所有队列的生命周期
 */
public class QueueManager {
    private static Logger logger = CommonLogger.getLogger("queue");
    
    // 单例模式
    private static volatile QueueManager instance;
    
    /**
     * 获取队列管理器实例
     * @return 队列管理器实例
     */
    public static QueueManager getInstance() {
        if (instance == null) {
            synchronized (QueueManager.class) {
                if (instance == null) {
                    instance = new QueueManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化所有队列
     */
    public void initAllQueues() {
        logger.info("初始化所有队列");
        
        // 初始化敏感词检测队列
        SensitiveWordQueue.getInstance();
        
        logger.info("所有队列初始化完成");
    }
    
    /**
     * 停止所有队列
     */
    public void stopAllQueues() {
        logger.info("停止所有队列");
        
        // 停止敏感词检测队列
        SensitiveWordQueue.getInstance().stop();
        
        logger.info("所有队列已停止");
    }
} 