package com.yunqu.cc.consumer.service.queue.local.impl;

import com.alibaba.fastjson.JSONArray;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.consumer.base.CommonLogger;
import com.yunqu.cc.consumer.base.Constants;
import com.yunqu.cc.consumer.model.SensitiveModel;
import com.yunqu.cc.consumer.service.queue.local.LocalMessageQueue;
import org.apache.log4j.Logger;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.kit.RandomKit;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import static com.yunqu.cc.consumer.base.QueryFactory.getQuery;

/**
 * 敏感词检测消息队列
 */
public class SensitiveWordQueue extends LocalMessageQueue {
    private static Logger logger = CommonLogger.getLogger("sensitive");

    // 缓存实例
    private static EasyCache cache = CacheManager.getMemcache();

    // 单例模式
    private static volatile SensitiveWordQueue instance;
    
    /**
     * 获取敏感词队列实例
     * @return 敏感词队列实例
     */
    public static SensitiveWordQueue getInstance() {
        if (instance == null) {
            synchronized (SensitiveWordQueue.class) {
                if (instance == null) {
                    // 创建队列实例，队列名称为sensitive-word，默认容量为1000，消费者线程数为5
                    instance = new SensitiveWordQueue("sensitive-word", Constants.getLocalQueueCapacity(), Constants.getLocalQueueConsumerThreadNum());
                }
            }
        }
        
        // 分离实例创建和启动逻辑，增强线程安全性
        if (!instance.isRunning()) {
            synchronized (SensitiveWordQueue.class) {
                if (!instance.isRunning()) {
                    // 启动队列
                    instance.start();
                }
            }
        }
        
        return instance;
    }
    
    /**
     * 构造函数
     * @param queueName 队列名称
     * @param capacity 队列容量
     * @param consumerThreads 消费者线程数
     */
    private SensitiveWordQueue(String queueName, int capacity, int consumerThreads) {
        super(queueName, capacity, consumerThreads);
    }
    
    /**
     * 处理敏感词检测消息
     */
    @Override
    protected void processMessage(JSONObject resqJson) {
        try {
            // 队列状态日志
            logger.info(CommonUtil.getClassNameAndMethod(this) + "队列名称:" +  this.getQueueName() + ",当前队列长度: " + this.getQueueSize());
            logger.info(CommonUtil.getClassNameAndMethod(this) + "开始处理敏感词检测消息-->");
            JSONArray data = resqJson.getJSONArray("data");
            
            // 过滤只保留roomid不为空的群聊数据
            logger.info(CommonUtil.getClassNameAndMethod(this) + "过滤前的消息数量: " + data.size());
            JSONArray groupData = new JSONArray();
            if(data!=null && data.size()>0){
                for (int i = 0; i < data.size(); i++) {
                    JSONObject item = data.getJSONObject(i);
                    String roomId = item.getString("roomid");
                    if (roomId != null && !roomId.isEmpty()) {
                        groupData.add(item);
                    }
                }
            }
            logger.info(CommonUtil.getClassNameAndMethod(this) + "过滤后的消息数量: " + groupData.size());

            // 打印或后续处理
            JSONArray result = groupByRoomId(groupData);
            logger.info(CommonUtil.getClassNameAndMethod(this) + "分组后的消息: " + JSONObject.toJSONString(result));
            if(result!=null && result.size()>0){
                for (int r = 0; r < result.size(); r++) {
                    JSONObject resultData = result.getJSONObject(r);
                    logger.info(CommonUtil.getClassNameAndMethod(this) + "resultData: " + resultData);
                    //群id
                    String roomId = resultData.getString("roomid");

                    JSONObject wxGroupMsg = getWxGroupMasterId(roomId);

                    logger.info(CommonUtil.getClassNameAndMethod(this) + "wxGroupMsg: " + wxGroupMsg);

                    //群主ID
                    String masterId = wxGroupMsg.getString("owner");
                    //群名称
                    String roomName = wxGroupMsg.getString("name");

                    JSONArray chatList = resultData.getJSONArray("chatList");

                    JSONObject swqResult = sendInsideQuality(chatList); //调用质检平台进行敏感词检测
                    logger.info(CommonUtil.getClassNameAndMethod(this) + "swqResult: " + swqResult);
                    if(swqResult != null && swqResult.getString("respCode").equals(GWConstants.RET_CODE_SUCCESS)){
                        //成功 愿意干啥干啥  返沪的serviceResult格式在data.text
                        JSONObject resultJson = swqResult.getJSONObject("respData");
                        JSONArray dataArray = resultJson.getJSONArray("data");
                        if (dataArray != null && dataArray.size() > 0){
                            for (int i = 0; i < dataArray.size(); i++) {
                                JSONObject item = dataArray.getJSONObject(i);
                                //命中敏感词
                                String highLightWord = item.getString("highLightWord");
                                //消息发送时间
                                String msgtime = item.getString("msgtime");
                                // 将字符串时间戳转换为 long 类型
                                long timestamp = Long.parseLong(msgtime);
                                // 消息id
                                String msgid = item.getString("msgid");

                                // 将时间戳（毫秒）转换为 LocalDateTime
                                LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());

                                // 定义日期时间格式
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                                // 格式化日期时间
                                String formattedDateTime = dateTime.format(formatter);
                                // 打印或使用 formattedDateTime
                                logger.info("Formatted Date Time: " + formattedDateTime);

                                //发送人
                                String from = item.getString("from");

                                saveSensitiveWordRecords(roomId,roomName,from,from,highLightWord,formattedDateTime);

                                // 更新群聊消息明细表
//                                updateRecordIsTriggerSensitive(msgid);

                                // 如果检测到敏感词，需要调用企微卡片通知功能，进行提醒
                                if (masterId != null){
                                    String trigger = from != null && from.startsWith("1")? from : "客户";
                                    String time = DateUtil.getDateStrByTimeStamp(Long.parseLong(msgtime)).substring(5,16);
                                    JSONObject wbcResult = sendWxButtonCardMessage(roomId,roomName,masterId,highLightWord,trigger,time);
                                    if(wbcResult != null && "000".equals(wbcResult.getString("respCode"))){
                                        logger.info(CommonUtil.getClassNameAndMethod(this) + "发送成功, "+"roomId:" + roomId +",roomName:"+ roomName + "masterId:" + masterId + "param: " + item );
                                    }else{
                                        logger.info(CommonUtil.getClassNameAndMethod(this) + "发送失败, "+"roomId:" + roomId +",roomName:"+ roomName + "masterId:" + masterId + "param: " + item );
                                    }
                                }else{
                                    logger.info(CommonUtil.getClassNameAndMethod(this) + "当前roomId无法找到群主ID,无法发送卡片. ");
                                }
                            }
                            updateOrderIsTriggerSensitive(roomId);
                        }
                    }
                }
            }
            System.out.println(result.toJSONString());

            logger.info(CommonUtil.getClassNameAndMethod(this) + "完成处理敏感词检测消息-->");
        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + "处理敏感词检测消息异常-->"+ e.getMessage());
        }
    }

    /**
     *  调用质检平台进行敏感词检测
     */
    public JSONObject sendInsideQuality(JSONArray chatList) throws Exception{
        IService service = ServiceContext.getService("MIXGW_INSIDE_QUALITY_INTERFACE");
        JSONObject params = new JSONObject();
        params.put("msgId", RandomKit.randomStr());
        params.put("chatList", chatList);

        JSONObject jsonParams  = new JSONObject();
        jsonParams.put("command","sensitiveWordsQuality");
        jsonParams.put("params",params);
        return service.invoke(jsonParams);
    }

    /**
     *  调用企微接口-根据群ID获取群主ID
     */
    public JSONObject getWxGroupMasterId(String roomId) throws Exception{
        IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
        JSONObject params = new JSONObject();
        params.put("roomId",roomId);

        JSONObject jsonParams  = new JSONObject();
        jsonParams.put("command","getGroupMasterAcc");
        jsonParams.put("params",params);
        JSONObject invoke = service.invoke(jsonParams);
        if(invoke != null && "000".equals(invoke.getString("respCode"))){
            return invoke.getJSONObject("respData");
        }
        return new JSONObject();
    }

    /**
     * 更新订单触发敏感词
     * @param chatId
     */
    public void updateOrderIsTriggerSensitive(String chatId){
        EasyRecord record = new EasyRecord("C_NO_GROUP_ORDERS","CHAT_ID").setPrimaryValues(chatId);
        record.set("IS_TRIGGER_SENSITIVE","1");
        try {
            getQuery().update(record);
        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + "更新订单触发敏感词异常-->"+e.getMessage(), e);
        }
    }

    /**
     * 更新群聊消息触发敏感词
     * @param msgid
     */
    public void updateRecordIsTriggerSensitive(String msgid){
        EasyRecord record = new EasyRecord("C_NO_WECOM_GROUP_RECORD","MSG_ID").setPrimaryValues(msgid);
        record.set("IS_TRIGGER_SENSITIVE","1");
        try {
            getQuery().update(record);
        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + "更新订单触发敏感词异常-->"+e.getMessage(), e);
        }
    }

    /**
     *  调用企微接口-发送按钮卡片消息
     */
    public JSONObject sendWxButtonCardMessage(String groupId,String groupName,String userId,String sensitiveWord,String triggerAccount,String triggerTime) throws Exception{
        IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
        JSONObject params = new JSONObject();
        params.put("userId",userId); //如果有多个,需要用"|"分割
        params.put("sensitiveWord",sensitiveWord);
        params.put("groupName",groupName);
        params.put("triggerAccount",triggerAccount);
        params.put("triggerTime",triggerTime);
        params.put("roomId",groupId);

        JSONObject jsonParams  = new JSONObject();
        jsonParams.put("command","sendWxButtonCardMessage");
        jsonParams.put("params",params);
        return service.invoke(jsonParams);
    }


    /**
     * 获取群聊名称
     */
    public String getRoomName(String roomId)  {
        String roomName = "";
        EasySQL wechatNameSql = new EasySQL();
        wechatNameSql.append(roomId, "SELECT GROUP_CHAT_NAME FROM YWDB.C_NO_GROUP_ORDERS WHERE CHAT_ID = ?");
        try {
            roomName = getQuery().queryForString(wechatNameSql.getSQL(), wechatNameSql.getParams());
        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + "获取群聊名称异常-->"+ e.getMessage(),e);
            return roomName;
        }
        return roomName;
    }

    /**
     * 根据群ID获取坐席昵称
     * @param roomId 群ID
     * @return 坐席昵称，如果获取失败返回null
     */
    private String getAgentNameByRoomId(String roomId,String wxAccountId) {
        if (StringUtils.isBlank(roomId)) {
            return null;
        }

        String cacheKey = "AGENT_NAME_" + wxAccountId + roomId;
        try {
            // 先从缓存获取
            String cachedName = cache.get(cacheKey);
            if (StringUtils.isNotBlank(cachedName)) {
                return cachedName;
            }

            // 缓存未命中，调用接口获取
            IService service = ServiceContext.getService("MIXGW_MCSP_INTEFACE");
            JSONObject request = new JSONObject();
            JSONObject data = new JSONObject();
            JSONObject restParams = new JSONObject();
            restParams.put("enterpriseWechatId", Constants.getEnterpriseWechatId()); //企业ID 美的：100,小天鹅:200
            restParams.put("groupI1d", "");
            restParams.put("wechatGroupId", roomId);
            data.put("restParams", restParams);
            request.put("command", "getGroupDetail");
            request.put("data", data);

            JSONObject result = service.invoke(request);
            if (result != null && GWConstants.RET_CODE_SUCCESS.equals(result.getString("respCode"))) {
                JSONObject respData = result.getJSONObject("respData");
                JSONObject dataObj = respData.getJSONObject("data");
                if (dataObj != null) {
                    String ownerName = dataObj.getString("ownerName");
                    if (StringUtils.isNotBlank(ownerName)) {
                        // 存入缓存，有效期1小时
                        cache.put(cacheKey, ownerName, 60 * 60);
                        logger.info(CommonUtil.getClassNameAndMethod(this) + "获取到坐席昵称并存入缓存: " + ownerName + ", roomId: " + roomId);
                        return ownerName;
                    }
                }
            }

            logger.warn(CommonUtil.getClassNameAndMethod(this) + "未能获取到坐席昵称, roomId: " + roomId);
            return null;

        } catch (ServiceException e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + "调用getGroupDetail接口异常, roomId: " + roomId + ", 异常: " + e.getMessage(), e);
            return null;
        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + "获取坐席昵称异常, roomId: " + roomId + ", 异常: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据微信ID获取客户昵称
     * @param userId 微信用户ID
     * @return 客户昵称，如果获取失败返回null
     */
    private String getCustomerNameByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }

        String cacheKey = "CUSTOMER_NAME_" + userId;
        try {
            // 先从缓存获取
            String cachedName = cache.get(cacheKey);
            if (StringUtils.isNotBlank(cachedName)) {
                return cachedName;
            }
            // 缓存未命中，调用接口获取
            IService service = ServiceContext.getService("MIXGW_QYWX_INTEFACE");
            JSONObject request = new JSONObject();
            request.put("command", "getExtCustInfo");
            JSONObject params = new JSONObject();
            params.put("userId", userId);
            request.put("params", params);

            JSONObject result = service.invoke(request);

            if (result != null && result.getJSONObject("external_contact") != null) {
                JSONObject extCustInfo = result.getJSONObject("external_contact");
                String customerName = extCustInfo.getString("name");
                if (StringUtils.isNotBlank(customerName)) {
                    // 存入缓存，有效期1小时
                    cache.put(cacheKey, customerName, 60 * 60);
                    logger.info(CommonUtil.getClassNameAndMethod(this) + "获取到客户昵称并存入缓存: " + customerName + ", userId: " + userId);
                    return customerName;
                }
            }

            logger.warn(CommonUtil.getClassNameAndMethod(this) + "未能获取到客户昵称, userId: " + userId);
            return null;

        } catch (ServiceException e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + "调用getExtCustInfo接口异常, userId: " + userId + ", 异常: " + e.getMessage(), e);
            return null;
        } catch (Exception e) {
            logger.error(CommonUtil.getClassNameAndMethod(this) + "获取客户昵称异常, userId: " + userId + ", 异常: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 保存企微群聊敏感词触发记录表
     */
    public void saveSensitiveWordRecords(String roomId,String roomName,String wxAccountName,String wxAccountId,String keyWord,String triggerTime) {
        logger.info(CommonUtil.getClassNameAndMethod(this) + "开始保存敏感词触发记录, roomId: " + roomId + ", wxAccountId: " + wxAccountId + ", keyWord: " + keyWord);

        SensitiveModel sensitiveModel = new SensitiveModel();
        sensitiveModel.setRoomId(roomId);
        sensitiveModel.setRoomName(roomName);
//        sensitiveModel.setWxAccountName(wxAccountName);
        sensitiveModel.setWxAccountId(wxAccountId);
        sensitiveModel.setKeyWord(keyWord);

        // 根据wxAccountId判断是否为手机号，如果是手机号则isAgent为1，否则为0
        String isAgent = (wxAccountId != null && wxAccountId.matches("^[0-9]{11}$")) ? "1" : "0";

        // 动态获取触发企微账号名称
        String finalAccountName = wxAccountName; // 默认使用原有名称
        if ("1".equals(isAgent)){
            // 获取坐席昵称
            String agentName = getAgentNameByRoomId(roomId,wxAccountId);
            if (StringUtils.isNotBlank(agentName)) {
                finalAccountName = agentName;
                logger.info(CommonUtil.getClassNameAndMethod(this) + "成功获取坐席昵称: " + agentName + ", 替换原名称: " + wxAccountName);
            } else {
                cache.put( "AGENT_NAME_" + wxAccountId + roomId, finalAccountName, 60 * 10);
                logger.warn(CommonUtil.getClassNameAndMethod(this) + "未能获取坐席昵称，使用原名称: " + wxAccountName);
            }
        } else {
            // 获取客户昵称
            String customerName = getCustomerNameByUserId(wxAccountId);
            if (StringUtils.isNotBlank(customerName)) {
                finalAccountName = customerName;
                logger.info(CommonUtil.getClassNameAndMethod(this) + "成功获取客户昵称: " + customerName + ", 替换原名称: " + wxAccountName);
            } else {
                cache.put("CUSTOMER_NAME_" + wxAccountId, finalAccountName, 60 * 10);
                logger.warn(CommonUtil.getClassNameAndMethod(this) + "未能获取客户昵称，使用原名称: " + wxAccountName);
            }
        }

        // 设置最终的账号名称
        sensitiveModel.setWxAccountName(finalAccountName);
        logger.info(CommonUtil.getClassNameAndMethod(this) + "最终使用的账号名称: " + finalAccountName);

        // 保存敏感词模型
        sensitiveModel.saveSensitiveModel(sensitiveModel, triggerTime, isAgent);
        logger.info(CommonUtil.getClassNameAndMethod(this) + "敏感词触发记录保存完成");
    }
    /**
     * 按roomid分组并封装为[{"roomid":"","chatList":[{"msgtime":"","channel":"","text":"","from":""}]}]格式
     */
    private static  JSONArray groupByRoomId(JSONArray groupData) {
        Map<String, JSONArray> roomMap = new HashMap<>();
        for (int i = 0; i < groupData.size(); i++) {
            JSONObject item = groupData.getJSONObject(i);
            if (!"text".equals(item.getString("msgtype"))){
                continue;
            }
            if (item.getJSONObject("text") == null ){
                continue;
            }
            String roomId = item.getString("roomid");
            JSONObject chatObj = new JSONObject();
            chatObj.put("msgtime", item.getString("msgtime"));
            chatObj.put("msgid", item.getString("msgid"));
            String from = item.getString("from");
            chatObj.put("channel", (from != null && from.matches("^[0-9]{11}$")) ? "0" : "1");
            String content = item.getJSONObject("text").getString("content");
            // 处理引用/回复消息格式，只提取原文内容
            if (content != null && content.contains("------")) {
                // 如果包含分隔符，只取分隔符后面的内容
                String[] parts = content.split("------");
                if (parts.length > 1) {
                    content = parts[parts.length - 1].trim(); // 取最后一部分并去除首尾空格
                }
            }

            if (content != null && content.contains("- - - - - - - - - - - - - - -")) {
                // 如果包含分隔符，只取分隔符后面的内容
                String[] parts = content.split("- - - - - - - - - - - - - - -");
                if (parts.length > 1) {
                    content = parts[parts.length - 1].trim(); // 取最后一部分并去除首尾空格
                }
            }

            chatObj.put("text", content);
            chatObj.put("from", from);
            roomMap.computeIfAbsent(roomId, k -> new JSONArray()).add(chatObj);
        }
        JSONArray result = new JSONArray();
        roomMap.forEach((roomId, chatList) -> {
            JSONObject groupObj = new JSONObject();
            groupObj.put("roomid", roomId);
            groupObj.put("chatList", chatList);
            result.add(groupObj);
        });
        return result;
    }



    public static void main(String[] args) {
        String a = "[{\"tolist\":[\"wmPAQDcQAAYchdzPjKJbQHog3gbjjQrQ\",\"wmPAQDcQAAwyL7TyqSj9Fmoce5nAQ4uA\",\"wmPAQDcQAAsQdvLQeBiQgdIagjgtSFmw\",\"wmPAQDcQAAmyUT8ap-su4M_jQhL62oaA\",\"wmPAQDcQAA7V1Bv5ayLDhkIiW_c8RpTQ\",\"wmPAQDcQAA71VMwAh5gGnfCaYgb6lZgQ\",\"13715683094\",\"18038722296\"],\"msgtime\":1753669532126,\"msgid\":\"6141883488192116796_1753669536242_external\",\"action\":\"send\",\"from\":\"18943410214\",\"text\":{\"content\":\"牢大来了 原神有救了\"},\"msgtype\":\"text\",\"roomid\":\"wrPAQDcQAAQf7N-3goyu7wCdLrkDDCLA\"},{\"tolist\":[\"wmPAQDcQAAYchdzPjKJbQHog3gbjjQrQ\",\"wmPAQDcQAAwyL7TyqSj9Fmoce5nAQ4uA\",\"wmPAQDcQAAsQdvLQeBiQgdIagjgtSFmw\",\"wmPAQDcQAAmyUT8ap-su4M_jQhL62oaA\",\"wmPAQDcQAA7V1Bv5ayLDhkIiW_c8RpTQ\",\"wmPAQDcQAA71VMwAh5gGnfCaYgb6lZgQ\",\"13715683094\",\"18038722296\"],\"msgtime\":1753669537294,\"msgid\":\"10734081813083356753_1753669541245_external\",\"action\":\"send\",\"from\":\"18943410214\",\"text\":{\"content\":\"有毒吗\"},\"msgtype\":\"text\",\"roomid\":\"wrPAQDcQAAQf7N-3goyu7wCdLrkDDCLA\"},{\"tolist\":[\"wmPAQDcQAAeSSw77swkvri20UZVVNg-g\",\"wmPAQDcQAA7V1Bv5ayLDhkIiW_c8RpTQ\",\"13715683094\",\"18038722296\"],\"msgtime\":1753669547785,\"msgid\":\"13039641050719309390_1753669551196_external\",\"action\":\"send\",\"from\":\"18943410214\",\"text\":{\"content\":\"牢大的原神\"},\"msgtype\":\"text\",\"roomid\":\"wrPAQDcQAArYX10geWAQBhbghn1NSdOA\"},{\"tolist\":[\"wmPAQDcQAAeSSw77swkvri20UZVVNg-g\",\"wmPAQDcQAA7V1Bv5ayLDhkIiW_c8RpTQ\",\"13715683094\",\"18038722296\"],\"msgtime\":1753669559256,\"msgid\":\"8145029024351178465_1753669563209_external\",\"action\":\"send\",\"from\":\"18943410214\",\"text\":{\"content\":\"牢牢的大 牢大的牢\"},\"msgtype\":\"text\",\"roomid\":\"wrPAQDcQAArYX10geWAQBhbghn1NSdOA\"},{\"tolist\":[\"wmPAQDcQAAeSSw77swkvri20UZVVNg-g\",\"wmPAQDcQAA7V1Bv5ayLDhkIiW_c8RpTQ\",\"13715683094\",\"18038722296\"],\"msgtime\":1753669564547,\"msgid\":\"15121186472775745006_1753669568219_external\",\"action\":\"send\",\"from\":\"18943410214\",\"text\":{\"content\":\"有毒的测试\"},\"msgtype\":\"text\",\"roomid\":\"wrPAQDcQAArYX10geWAQBhbghn1NSdOA\"},{\"tolist\":[\"wmPAQDcQAAYchdzPjKJbQHog3gbjjQrQ\",\"wmPAQDcQAAwyL7TyqSj9Fmoce5nAQ4uA\",\"wmPAQDcQAAsQdvLQeBiQgdIagjgtSFmw\",\"wmPAQDcQAAmyUT8ap-su4M_jQhL62oaA\",\"wmPAQDcQAA7V1Bv5ayLDhkIiW_c8RpTQ\",\"13715683094\",\"18943410214\",\"18038722296\"],\"msgtime\":1753669588710,\"msgid\":\"7158704667875782340_1753669592424_external\",\"action\":\"send\",\"from\":\"wmPAQDcQAA71VMwAh5gGnfCaYgb6lZgQ\",\"text\":{\"content\":\"我等下看 有没有推给我\"},\"msgtype\":\"text\",\"roomid\":\"wrPAQDcQAAQf7N-3goyu7wCdLrkDDCLA\"},{\"emotion\":{\"md5sum\":\"7e7160e0b61a67cc1b8992119e1f756d\",\"sdkfileid\":\"CiAxZDI5ZDQ2ZjBlYzU5MmI3ZDNjYjVmNTYxODMwNGNhMxI4TkRkZk1UWTRPRGcxTnpBMk9UWTVORGMzTWw4MU9EVTVOVFEzTlRkZk1UYzFNelkyT1RZd01RPT0aIDc2NjE2ZTY5NmI2MjczNzY2MzZlN2E2Yjc2NzY2NzY5\",\"width\":240,\"fileUrl\":\"https://oss-cn-guian-uat.midea.com/wecom-excel-export-sit/chat-history-202507-7e7160e0b61a67cc1b8992119e1f756d.gif?AWSAccessKeyId=bhGuInKaoZH5gzRqa7VU6MVf&Expires=1754965743&Signature=iZPfGjqPk2NRKQA01n5jFKM6KKg%3D&1=1\",\"imagesize\":14697,\"type\":1,\"height\":240},\"tolist\":[\"wmPAQDcQAAeSSw77swkvri20UZVVNg-g\",\"wmPAQDcQAA7V1Bv5ayLDhkIiW_c8RpTQ\",\"18943410214\",\"18038722296\"],\"msgtime\":1753669597604,\"msgid\":\"1689013373076596284_1753669601269_external\",\"action\":\"send\",\"from\":\"13715683094\",\"msgtype\":\"emotion\",\"roomid\":\"wrPAQDcQAArYX10geWAQBhbghn1NSdOA\"}]";
        //JSONArray groupData = JSON.parseArray(a);
       // System.out.println(groupByRoomId(groupData));

        String content = "这是一条引用/回复消息：\\n\\\"欧阳旭\\n原神神原神牢大\\\"\\n------\\n好好好牢大";
        if (content != null && content.contains("------")) {
            // 如果包含分隔符，只取分隔符后面的内容
            String[] parts = content.split("------");
            if (parts.length > 1) {
                content = parts[parts.length - 1].trim(); // 取最后一部分并去除首尾空格
            }
        }
        System.out.println(content);

    }
} 